import json
import time
import copy
from algo_tools.websocket.task_server import TaskSocketServer
from kaiwu_agent.utils.common import image_caption
import os

class FakeControlAgent:
    def __init__(self, task_server: TaskSocketServer):
        # 设置收到指令后的回调函数
        task_server.register_task_callback(self.recv_task)
        self.task_server = task_server
        
    def recv_task(self, message):
        message, websocket = message["message"], message["websocket"]
        msg_dict = json.loads(message)
        print(f"recv: {msg_dict}")
        self.send_fake_result(msg_dict, websocket)

    def send_result_msg(self, state, result_str, msg_dict, websocket):
        msg_dict = copy.deepcopy(msg_dict)
        msg_dict['body'] = f'ROBOT: {result_str}'
        msg_dict["state"] = state
        msg_str = json.dumps(msg_dict, ensure_ascii=False)
        print(f"send: {msg_str}")
        self.task_server.send_msg(websocket, msg_str)
        
    def ask_user(self, instruction, default, send_dict, websocket):
        print(f"询问用户：{instruction}")
        self.send_result_msg("ask_user", instruction, send_dict, websocket)
        
        print(f"正在等待用户回复...")
        # reply_dict = self.recv_msg(websocket, timeout=None)
        message = self.task_server.get_latest_task_msg()
        
        # 发生超时
        if message is None:
            # print(f"没有收到用户回复：默认：{default}")
            # self.send_result_msg("running", default, send_dict, websocket)
            raise NotImplementedError
        else:
            # 注意：返回了新的websocket，老的会自动释放
            msg_str, websocket = message["message"], message["websocket"]
            reply_dict = json.loads(msg_str)
            
            # 注意：更新 conversation_id ！！！！！！
            send_dict["conversation_id"] = reply_dict["conversation_id"]
            
            reply = reply_dict["body"]
            print(f"用户回复：{reply}")
            self.send_result_msg("running", "好的收到", send_dict, websocket)
        
        # 注意：返回新的
        return websocket, send_dict
    
    def image2text(self, user_instruction, image_path):
        return image_caption(user_instruction, image_path)
    
    
    def send_fake_result(self, msg_dict, websocket):
        skill = msg_dict["skill"]
        user_instruction = msg_dict["body"]
        send_dict = {"conversation_id": msg_dict["conversation_id"]}                                                                                                                                                                                                 
        # self.send_result_msg("started", "", send_dict, websocket)
        time.sleep(1)
        # "greeting", "introduce_qiaohong", "self_introduce", "introduce_company", "scene_desc", "grasp_object", "place_object", "pick_and_place"
        if skill == "manipulation":
            self.send_result_msg("running", "好的收到", send_dict, websocket)
            
            # # 询问用户
            # time.sleep(2)
            # websocket, send_dict = self.ask_user("你想吃苹果还是雪梨", "给你拿个苹果吧", send_dict, websocket)
            
            # time.sleep(2)
            # websocket, send_dict = self.ask_user("需要削皮吗", "我帮你削皮吧", send_dict, websocket)

            time.sleep(2)
            self.send_result_msg("succeed", "任务完成了", send_dict, websocket)
                
        elif skill == "scene_desc":
            # self.send_result_msg("running", "", send_dict, websocket)
            # time.sleep(2)
            # image_path = 'examples/brain_agent/breakfast.jpg'
            image_path = 'examples/brain_agent/TG_breakfast.jpg'
            assert os.path.exists(image_path), image_path
            fake_scene_desc = self.image2text(user_instruction,image_path)
            self.send_result_msg("succeed", fake_scene_desc, send_dict, websocket)
            
        elif skill == "greeting":
            self.send_result_msg("succeed", "你好，有什么可以帮您", send_dict, websocket)
            
        elif skill == "introduce_qiaohong":
            self.send_result_msg("succeed", "乔红老师是中国科学院院士", send_dict, websocket)
            
        elif skill == "introduce_company":
            self.send_result_msg("succeed", "北京人形机器人创新中在亦庄成立", send_dict, websocket)
        
        else:
            self.send_result_msg("succeed", "收到，但我决定不回答你这个问题，除非你再叫我一次帅哥", send_dict, websocket)

    def start(self):
        # 会卡住主进程
        # asyncio.run(server.start_server())
        self.task_server.start()
        
    def stop(self):
        self.task_server.stop()
        
    
def main():
    # 通过环境变量传入端口，避免冲突，如：CONTROL_PORT=9600 && python3 fake_control_server.py
    task_server = TaskSocketServer(host="localhost", 
                                   port=os.environ.get("CONTROL_PORT", "9527"),
                                   task_path="/task", 
                                   maxsize=1000)
    control_agent = FakeControlAgent(task_server)
    # 启动服务，监听，接受task指令，执行task，给出回复，监听 ...
    control_agent.start()


if __name__ == "__main__":
    main()
