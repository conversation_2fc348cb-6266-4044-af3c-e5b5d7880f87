from fastapi import FastAPI
from pydantic import BaseModel
from typing import Any, List
import uvicorn

# 定义请求体数据格式
class UpdateStepStatusRequest(BaseModel):
    status: str  # 查询状态，如 "success"
    message: str  # 返回消息，如机器人回复的信息
    taskUuid: str  # 任务 ID
    taskStatus: str  # 任务状态，如 "completed"
    commandUuid: str
    steps: List[Any]  # 步骤列表，暂时为任意格式的空列表

app = FastAPI()

@app.post("/v1/api/chat/doResult")
async def update_step_status(request: UpdateStepStatusRequest):
    """
    接收机器人反馈并打印出来
    """
    print("收到机器人反馈:")
    print(f"状态: {request.status}")
    print(f"消息: {request.message}")
    print(f"任务 ID: {request.taskUuid}")
    print(f"任务状态: {request.taskStatus}")
    print(f"commandUuid: {request.commandUuid}")
    print(f"步骤列表: {request.steps}")

    # 返回成功状态
    return {"status": 0, "message": "成功"}


def run():
    print("虚拟云平台启动中...")
    uvicorn.run(app, host="0.0.0.0", port=8000)


if __name__ == "__main__":
    run()
