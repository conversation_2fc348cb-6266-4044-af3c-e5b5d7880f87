import json
import time
import queue
import uvicorn
import requests
import threading
from fastapi import FastAP<PERSON>
from pydantic import BaseModel


class Message(BaseModel):
    Task: str  # i.e., user instruction
    Conversation_ID: int = -1
    State: str  # task state, including ["Pending", "Running", "Finish", "Failure"]
    Data: str  # content that needs to be sent to brain agent, the skill status including ["Pending", "Running", "Finish", "Failure"]
    Additional_Info: str = ''  # Additional information to be sent to brain agent


class Command(BaseModel):
    Task: str
    Conversation_ID: int

class InstructionController:
    def __init__(self) -> None:
        self.message_queue = queue.Queue()  # Queue for message communication
        self.app = FastAPI()
        self.setup_routes()

    def setup_routes(self):
        @self.app.post("/control_agent_task_command")
        async def receive_command(command: Command):
            print(f"Received command: {command}")
            # Schedule a follow-up message after 20ms
            threading.Thread(target=self.send_message, args=(command, 0.2)).start()
            return {"status": "command_received"}

    def send_message(self, command: Command, delay: float):
        """Send a message to brain_agent_receiver after a specified delay."""
        # Wait for specified delay
        time.sleep(delay)
        # Set up payload
        data = {
               "Plan": json.dumps([{"skill1": "open drawer"},
                         {"skill2": "pick up tissue"},
                         {"skill3": "place tissue near the plate"},
                         {"skill4": "close drawer"}]),
               "Skill_Status": json.dumps([{"skill1": "Pending"},
                         {"skill2": "Pending"},
                         {"skill3": "Pending"},
                         {"skill4": "Pending"}])
               }
        payload = {
            "Task": command.Task,
            "State": "Running",
            "Data": json.dumps(data),
            "Additional_Info": '',
            "Conversation_ID": command.Conversation_ID
        }
        
        # Send message to brain_agent_receiver
        url = "http://localhost:8080/brain_agent_receiver"
        response = requests.post(url, json=payload)
        print(response.json())

        # Wait for specified delay
        time.sleep(delay)
        # Set up payload
        data = {
               "Skill_Status": json.dumps([{"skill1": "Running"},
                         {"skill2": "Pending"},
                         {"skill3": "Pending"},
                         {"skill4": "Pending"}])
               }
        payload = {
            "Task": command.Task,
            "State": "Running",
            "Data": json.dumps(data),
            "Additional_Info": '',
            "Conversation_ID": command.Conversation_ID
        }
        
        # Send message to brain_agent_receiver
        url = "http://localhost:8080/brain_agent_receiver"
        response = requests.post(url, json=payload)
        print(response.json())

        # Wait for specified delay
        time.sleep(delay)
        # Set up payload
        data = {
               "Skill_Status": json.dumps([{"skill1": "Finish"},
                         {"skill2": "Running"},
                         {"skill3": "Pending"},
                         {"skill4": "Pending"}])
               }
        payload = {
            "Task": command.Task,
            "State": "Running",
            "Data": json.dumps(data),
            "Additional_Info": '',
            "Conversation_ID": command.Conversation_ID
        }
        
        # Send message to brain_agent_receiver
        url = "http://localhost:8080/brain_agent_receiver"
        response = requests.post(url, json=payload)
        print(response.json())

        # Wait for specified delay
        time.sleep(delay)
        # Set up payload
        data = {
               "Skill_Status": json.dumps([{"skill1": "Finish"},
                         {"skill2": "Finish"},
                         {"skill3": "Finish"},
                         {"skill4": "Finish"}])
               }
        payload = {
            "Task": command.Task,
            "State": "Finish",
            "Data": json.dumps(data),
            "Additional_Info": '',
            "Conversation_ID": command.Conversation_ID
        }
        
        # Send message to brain_agent_receiver
        url = "http://localhost:8080/brain_agent_receiver"
        response = requests.post(url, json=payload)
        print(response.json())

    def run(self):
        """Start FastAPI server"""
        uvicorn.run(self.app, host="0.0.0.0", port=8088)

if __name__ == "__main__":
    instruction_controller = InstructionController()
    instruction_controller.run()
