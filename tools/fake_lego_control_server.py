import uvicorn
import requests
import random
import time
from fastapi import FastAPI
from pydantic import BaseModel
from algo_tools.utils.msg_monitor import MessageMonitor
from kaiwu_agent.agents.lego_control_agent import LegoControlCommand, LegoControlResult


class FakeLegoControlServer:
    def __init__(self,
                 port=9528,
                 result_server="http://localhost:9529/lego/result") -> None:
        self.port = port
        self.result_server = result_server
        self.message_monitor = MessageMonitor(recv_callback=self.recv_command)
        
        self.app = FastAPI()
        self.setup_routes()

    def setup_routes(self):
        @self.app.post("/v1/command")
        async def v1_command(command: LegoControlCommand):
            print(f"Received pick_place command: {command}")
            
            # 异步处理，立即返回收到
            self.message_monitor.send(command)
            
            return {"code": 0, "message": "收到指令"}
    
    def send_result(self, result: LegoControlResult):
        payload = result.model_dump()
        response = requests.post(self.result_server, json=payload)
        print(response.json())
    
    def recv_command(self, command):
        # check cmd
        print(command.Pick_Place)
        
        # running
        result = LegoControlResult(
            Conversation_ID=command.Conversation_ID,
            State="Running",
            Response="正在执行乐高抓取放置任务"
        )
        self.send_result(result)
        
        time.sleep(2)
        
        # finish
        choice = random.choice(range(1))
        if choice == 1:
            result = LegoControlResult(
                Conversation_ID=command.Conversation_ID,
                State="Succeed",
                Response="任务成功"
            )
        elif choice == 2:
            result = LegoControlResult(
                Conversation_ID=command.Conversation_ID,
                State="Failed",
                Response="抓取指定积木失败"
            )
        elif choice == 3:
            result = LegoControlResult(
                Conversation_ID=command.Conversation_ID,
                State="Failed",
                Response="放置积木到指定位置失败"
            )
        else:
            raise NotImplementedError
            
        self.send_result(result)
    
    def run(self):
        """Start FastAPI server"""
        self.message_monitor.start()
        uvicorn.run(self.app, host="0.0.0.0", port=self.port)


if __name__ == "__main__":
    logo_control_server = FakeLegoControlServer(
        port=9528,
        result_server="http://localhost:9529/lego/result"
    )
    logo_control_server.run()
