import requests
import json

from kaiwu_agent.agents.lego_control_agent import LegoControlCommand

# FastAPI 服务端地址
url = "http://10.10.245.125:9528/v1/command"

def send_request(payload: dict):
    """
    发送 POST 请求到服务器
    """
    try:
        response = requests.post(url, json=payload)
        return response
    except Exception as e:
        print(f"请求发送失败: {e}")
        return None

def run():
    """
    主程序，负责接受用户指令并处理
    """
    print("欢迎使用云端模拟器！")
    print("请输入用户指令，程序将自动构建并发送消息。输入 'exit' 退出程序。\n")
    
    while True:
        command = input("请输入用户指令 (command): ").strip()
        if command.lower() == "exit":
            print("程序已退出。")
            break
        
        # 生成数据
        # payload = generate_payload(command)
        if command == "1":
            payload = LegoControlCommand(
                Conversation_ID="12131414717414",
                Pick_Place={
                    "color": "yellow",
                    "shape": (1, 2),
                    "position": (0, 0)
                }   
            ).model_dump()
        elif command == "2":
            payload = LegoControlCommand(
                Conversation_ID="12131414717414",
                Pick_Place={
                    "color": "green",
                    "shape": (1, 2),
                    "position": (1, 0)
                }   
            ).model_dump()
        elif command == "3":
            payload = LegoControlCommand(
                Conversation_ID="12131414717414",
                Pick_Place={
                    "color": "blue",
                    "shape": (1, 1),
                    "position": (2, 0)
                }   
            ).model_dump()
        else:
            raise NotImplementedError
        
        # 打印即将发送的 JSON 数据
        print("\n即将发送的数据:")
        print(json.dumps(payload, indent=4, ensure_ascii=False))

        # 发送请求并处理响应
        response = send_request(payload)
        if response:
            print("\n服务器响应:")
            print(f"状态码: {response.status_code}")
            print(f"响应内容: {response.json()}\n")

        print("-" * 50)


if __name__ == "__main__":
    run()
