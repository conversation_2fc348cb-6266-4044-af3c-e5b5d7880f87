#!/bin/bash

SESSION_NAME="agent_wrc"
WORKDIR="/home/<USER>/projects/kaiwu_agent"

# 检查虚拟环境是否存在
if [ ! -d "$WORKDIR/.venv" ]; then
    echo "未找到 .venv 虚拟环境，请先创建"
    exit 1
fi

# 如果session已存在则kill
if tmux has-session -t $SESSION_NAME 2>/dev/null; then
    echo "已存在名为 $SESSION_NAME 的tmux session，正在kill..."
    tmux kill-session -t $SESSION_NAME
fi

BASE_CMD="cd $WORKDIR && source .venv/bin/activate && python main.py -v react -u compose_wrc -em gpt-41-lego"
declare -a configs=("box_move_wrc" "lightbulb_check_wrc" "gift_package_wrc" "electrician_wrc" "sorter_wrc")

# 启动新的tmux session
tmux new-session -d -s $SESSION_NAME

# tmux select-layout tiled

# 在第一个窗口运行第一个命令
tmux send-keys -t $SESSION_NAME:0 "$BASE_CMD -c ${configs[0]}" C-m

# 新建3个窗口并分别运行命令
for i in 1 2 3 4
do
    tmux new-window -t $SESSION_NAME:$i
    tmux send-keys -t $SESSION_NAME:$i "$BASE_CMD -c ${configs[$i]}" C-m
done

# 附加到tmux session
tmux attach-session -t $SESSION_NAME
