# -*- coding: utf-8 -*-
"""Welcome to the KAIWU AGENT setup.py.

We strongly recommand you install via pypi source instead.
"""

import io
import os.path as osp

from setuptools import find_packages
from setuptools import setup


REQ_DIR = "."
HERE = osp.abspath(osp.dirname(__file__))


def load_requirements(file_path):
    """Parse the package dependencies listed in a requirements file."""
    valid_lines = []
    with open(file_path, "r", encoding="utf-8") as file:
        for line in file.readlines():
            line = line.strip()
            if len(line) > 0 and line[0].isalpha():
                valid_lines.append(line)
            elif "-r" in line and "-r" == line.strip()[:2]:
                splits = line.split()
                assert len(splits) == 2, len(splits)
                valid_lines += load_requirements(
                    osp.join(osp.dirname(file_path), splits[1])
                )
    return valid_lines


def get_long_description():
    """Import the README and use it as the long-description.

    Note: this will only work if 'README.md' is present in your MANIFEST.in file!
    """
    try:
        with io.open(osp.join(HERE, "README.md"), encoding="utf-8") as f:
            long_description = "\n" + f.read()
    except FileNotFoundError:
        long_description = "Please refer to short description"
    return long_description


def load_version():
    """Load the package's __version__.py module as a dictionary.

    Note: this will only work if '__version__.py' exist!
    """
    import kaiwu_agent
    return kaiwu_agent.__version__


if __name__ == "__main__":
    setup(
        name="kaiwu_agent",
        use_scm_version=True,
        description="KaiWu AGENT.",
        long_description=get_long_description(),
        long_description_content_type="text/markdown",
        # author="",
        # author_email="",
        python_requires=">=3.11.10",
        url="http://git.x-humanoid.com/kaiwu/agent/kaiwu_agent",
        packages=find_packages(
            exclude=["dist.*", "dist", "tests", "*.tests", "*.tests.*", "tests.*"]
        ),
        install_requires=load_requirements(osp.join(REQ_DIR, "requirements.txt")),
        extras_require={
            # TODO
            # "develop": load_requirements(osp.join(REQ_DIR, "./develop.txt")),
        },
        include_package_data=True,
        license="Apache License 2.0",
        classifiers=[
            # Trove classifiers
            # Full list: https://pypi.python.org/pypi?%3Aaction=list_classifiers
            "License :: OSI Approved :: Apache Software License",
            "Programming Language :: Python",
            "Programming Language :: Python :: 3",
            "Programming Language :: Python :: 3.8",
            "Programming Language :: Python :: Implementation :: CPython",
            "Programming Language :: Python :: Implementation :: PyPy",
        ],
    )
