# 配置系统设计
为提升框架的通用性、扩展性、易用性，在配置方案上参考了`mmdetection`、`crewai`等经典框架，决定
- 采用多重配置覆盖机制，按优先级加载配置文件并合并，命令行参数优先级最高，以适应内外部开发者、开物平台workflow接入、生产或开发环境的灵活部署需求
```
- /path/to/config.yaml   # agent配置
- .env                   # 密码、key，和风天气、博查搜索等
- model.yaml             # openai等模型账号
- main.py            # 在命令行传入一些参数，并在运行时构造一些变量，作为最高优的全局变量
```
- 采用Registry注册器机制，便于扩展Tool、Agent、Model等组件，便于在yaml里定义组件参数，并使用注册器实例化

## 示例
首先，创建并修改上述三个配置文件
```
cp examples/chat/tiangong_chat.yaml /path/to/config.yaml
cp .example.env .env
cp model.example.yaml. model.yaml
```

在`.env`里配置账号
```
HEFENG_LOOKUP_URL="https://geoapi.qweather.com/v2/city/lookup"
HEFENG_API_URL="https://devapi.qweather.com/v7/weather/now"
HEFENG_API_KEY=xxx
```

在`model.yaml`里配置模型
```
model_servers:
  openai:
    model: gpt-4.1
    api_key: your_key
    base_url: https://api.openai.com/v1
```

在`kaiwu_agent/utils/registry.py` 设置了以下组件注册器
```
TOOLS = Registry("TOOLS")
MODELS = Registry("MODELS")
MSG_CLIENTS = Registry("MSG_CLIENTS")
AGENTS = Registry("AGENTS")
GRAPH_CREATORS = Registry("GRAPH_CREATORS")
```

使用注册器，在`kaiwu_agent/agents/agent/graph_brain.py` 注册一个Agent组件
```
@AGENTS.register_module()
class GraphBrainAgent(BaseBrainAgent):
    """Brain Agent uses CompiledGraph instance as executor."""
    def __init__(self):
        ...
```

在`examples/chat/tiangong_chat.yaml`里使用前面注册好的Agent、Tool等组件
```
brain_agent:
  type: GraphBrainAgent
  msg_client: "{{ msg_client }}"  # 双花括号表示在运行时使用全局变量 msg_client 进行替换，避免重复实例化
  graph_creator: *graph_creator
  system_prompt: *system_prompt
  recv_music_path: "{{ CURRENT_DIR }}/examples/music/recv/3.wav"
  finish_music_path: "{{ CURRENT_DIR }}/examples/music/finish/2.wav"
  ...

tools: &tools
  - type: BoChaSearch        # 在 .env 里配置账号
  - type: HeFengWeather      # 在 .env 里配置账号
  - type: KnowledgeSearchV1
    # 把你QA问答对、知识库文档以markdown格式写清楚，目前只支持一个或多个`.md`文档（路径用:号隔开）
    doc_file: examples/rag/xhumanoid_intro.md
    # 当doc_file改变，务必同步修改tool_desc，去总结概括性介绍你的文档内容，如果写不清楚则大模型不会去调用该工具，则无法从你的文档中提取信息进行回答
    tool_desc: '知识库检索工具: 可回答和公司相关的问题，例如公司的人员、产品、组织架构等，例如"介绍一下你们公司"。'
```

- 上面有一个设计，使用双花括号的占位符，在`main.py`运行时会进行占位符替换，有效避免了组件的重复实例化，同时方便在yaml里引用一些在运行时才获得的变量
```
context = {
    "CURRENT_TIME": current_time, 
    "CURRENT_WEEKDAY": current_weekday,
    "CURRENT_DIR": os.path.dirname(__file__),
    **env_vars, 
    **cli_vars,
}
filled = resolve_placeholders(file_cfg, context, strict=False)
```

在`main.py`里完成配置加载、覆盖、占位符替换，并用得到的`cfg_dict`进行组件实例化
```
# 构建共享变量 msg_client
msg_client = MSG_CLIENTS.build_from_cfg(cfg_dict["msg_client"])

# 构建 brain agent，并自动递归构建 graph_creator, model, tools 等子组件
brain_agent = AGENTS.build_from_cfg(cfg_dict["brain_agent"])
```

最后，启动，并在命令行传入参数进行最高优覆盖
```
python3 main.py --model-server chatany --model gpt-4.1 -c examples/chat/tiangong_chat.yaml
```
