import logging
import argparse
from kaiwu_agent.messages.msg_client import VoiceSocketClient, FastAPIInstructListener, ComposeClient
from kaiwu_agent.utils.logger import logger
from kaiwu_agent.configs.base import BRAIN_VERSIONS, CONTROL_VERSIONS, \
    PERCEPTION_VERSIONS, USER_INTERFACE_VERSIONS
from kaiwu_agent.configs.brain_agent_v1v2 import get_tg_brain_agent_v1, get_tg_brain_agent_v2
from kaiwu_agent.configs.brain_agent_v3 import get_tg_brain_agent_v3
from kaiwu_agent.configs.brain_agent_react import get_tg_brain_agent_react
from kaiwu_agent.configs.visual_brain_agent import get_visual_brain_agent
from kaiwu_agent.configs.chat_agent import get_tg_chat_brain_agent
from kaiwu_agent.configs.config import VLM_MODELS, LLM_MODELS
try:
    from kaiwu_agent.configs.interact_agent import get_interact_agent, get_interact_agent_v2, get_interact_agent_v3
except Import<PERSON>rror as err:
    logger.warning(f"import interct agent failed, run `make interact` to install, error: {err}")
    get_interact_agent = None
    get_interact_agent_v2 = None
    get_interact_agent_v3 = None


def get_brain_agent(version: BRAIN_VERSIONS,
                    plan_model: str,
                    execute_model: str,
                    control_agent_version: CONTROL_VERSIONS,
                    perception_version: PERCEPTION_VERSIONS,
                    simulation = False,
                    save_folder = "assets/data",
                    user_interface_version = USER_INTERFACE_VERSIONS.V1_VOICE_CLIENT,
                    message_appending=False,
                    stream=False):
    if user_interface_version == USER_INTERFACE_VERSIONS.V1_VOICE_CLIENT:
        msg_client = VoiceSocketClient(
            uri="ws://localhost:8765",
            voice_path="/voice",
            tts_path="/ttsplay",
            # TODO: 目前只接受一个指令！
            maxsize=1,
            connect_timeout=3.0
        )
    elif user_interface_version == USER_INTERFACE_VERSIONS.V2_UI:
        msg_client = FastAPIInstructListener(
            host="0.0.0.0",
            port=8164,
            # fake_kauwuyun_recv.py，测试用
            # result_url="http://localhost:8000/v1/api/chat/doResult", 
            # Join那边提供的
            result_url="http://localhost:8080/echo/", 
            # TODO: 目前只接受一个指令！
            maxsize=1
        )
    elif user_interface_version == USER_INTERFACE_VERSIONS.COMPOSE:
        # debug
        enable_ws_input = False
        enable_ws_output = True
        enable_api_input = True
        enable_api_output = True

        if control_agent_version == CONTROL_VERSIONS.BOX_MOVE:
            port = 8164  # 天工 8164端口
            ws_url = "************"
        elif control_agent_version == CONTROL_VERSIONS.LIGHTBULB_CHECK:
            port = 8165  # UR 8165端口
            ws_url = "**************"
        elif control_agent_version == CONTROL_VERSIONS.GIFT_PACKAGE:
            port = 8166  # Franka 8166端口
            ws_url = "*************"
        elif control_agent_version == CONTROL_VERSIONS.ELECTRICIAN:
            port = 8167  # 电工 8167端口
            ws_url = "***********"
        else:
            port = 8164  # 默认端口
            ws_url = "localhost"

        ws_client = VoiceSocketClient(
            uri=f"ws://{ws_url}:8765",
            voice_path="/voice",
            tts_path="/ttsplay",
            # TODO: 目前只接受一个指令！
            maxsize=1,
            connect_timeout=3.0,
            enable_input=enable_ws_input,
            enable_output=enable_ws_output
        )
        
        api_client = FastAPIInstructListener(
            host="0.0.0.0",
            port=port,
            # fake_kauwuyun_recv.py，测试用
            result_url="http://localhost:9091/v1/api/chat/multiResult", 
            # Join那边提供的
            # result_url="http://localhost:9091/echo/", 
            # TODO: 目前只接受一个指令！
            maxsize=1,
            enable_input=enable_api_input,
            enable_output=enable_api_output
        )
        
        msg_client = ComposeClient(
            maxsize=1,
            client_list=[ws_client, api_client]
        )

    elif user_interface_version == USER_INTERFACE_VERSIONS.COMPOSE_WRC:
        # debug
        enable_ws_input = False
        enable_ws_output = True
        enable_api_input = True
        enable_api_output = True

        if control_agent_version == CONTROL_VERSIONS.BOX_MOVE_WRC:
            port = 8164  # 天工 8164端口
            ws_url = "localhost"
        elif control_agent_version == CONTROL_VERSIONS.LIGHTBULB_CHECK_WRC:
            port = 8165  # UR 8165端口
            ws_url = "localhost"
        elif control_agent_version == CONTROL_VERSIONS.GIFT_PACKAGE_WRC:
            port = 8166  # Franka 8166端口
            ws_url = "localhost"
        elif control_agent_version == CONTROL_VERSIONS.ELECTRICIAN_WRC:
            port = 8167  # 电工 8167端口
            ws_url = "localhost"
        elif control_agent_version == CONTROL_VERSIONS.SORTER_WRC:
            port = 8168
            ws_url = "localhost"
        else:
            port = 8164  # 默认端口
            ws_url = "localhost"
        
        ws_client = VoiceSocketClient(
            uri=f"ws://{ws_url}:8765",
            voice_path="/voice",
            tts_path="/ttsplay",
            # TODO: 目前只接受一个指令！
            maxsize=1,
            connect_timeout=3.0,
            enable_input=enable_ws_input,
            enable_output=enable_ws_output
        )
        
        api_client = FastAPIInstructListener(
            host="0.0.0.0",
            port=port,
            # fake_kauwuyun_recv.py，测试用
            result_url="http://************:9091/v1/api/chat/multiResult", 
            # Join那边提供的
            # result_url="http://localhost:9091/echo/", 
            # TODO: 目前只接受一个指令！
            maxsize=1,
            enable_input=enable_api_input,
            enable_output=enable_api_output
        )
        
        msg_client = ComposeClient(
            maxsize=1,
            client_list=[ws_client, api_client]
        )

    else:
        raise NotImplementedError(f"unsupported version: {user_interface_version}")
    
    em = LLM_MODELS[execute_model]
    pm = VLM_MODELS[plan_model]
    em_str = f"use LLM as execute model: {execute_model}: {em} "
    pm_str = f"use VLM as planning model: {plan_model}: {pm}"
    
    if version == BRAIN_VERSIONS.V1_XF_SKILL:
        # 第一版：需要写句式的意图识别，无大模型，纯语义理解，只有control agent
        brain_agent = get_tg_brain_agent_v1(msg_client)
        
    elif version == BRAIN_VERSIONS.V2_CUSTOM_INTENT:
        # 第二版：增加自研意图模型、chat agent
        brain_agent = get_tg_brain_agent_v2(msg_client)
        
    elif version == BRAIN_VERSIONS.V3_LANG_GRAPH:
        logger.info(em_str)
        logger.info(pm_str)
        # 第三版：使用langgraph实现的brain agent，替代了意图模型，plan-execute-replan模式
        brain_agent = get_tg_brain_agent_v3(msg_client,
                                            display_client=None,
                                            plan_model=pm,
                                            execute_model=em,
                                            control_agent_version=control_agent_version,
                                            simulation=simulation)

    elif version == BRAIN_VERSIONS.V3_LANG_GRAPH_ReAct:
        logger.info(em_str)
        logger.info(pm_str)
        # v3.0.1：使用langgraph实现的brain agent，替代了意图模型，ReAct模式
        brain_agent = get_tg_brain_agent_react(msg_client,
                                               model=em,
                                               vlm_model=pm,
                                               simulation=simulation,
                                               control_agent_version=control_agent_version,
                                               perception_version=perception_version,
                                               stream_send=stream)
    elif version == BRAIN_VERSIONS.V4_VISUAL_AGENT:
        logger.info(pm_str)
        # v4：基于多模态大模型的Visual Brain Agent
        brain_agent = get_visual_brain_agent(msg_client,
                                             model=pm,
                                             simulation=simulation,
                                             control_agent_version=control_agent_version,
                                             save_folder=save_folder,
                                             message_appending=message_appending,
                                             stream=stream)
    elif version == BRAIN_VERSIONS.CHAT_BRAIN_AGENT:
        logger.info(em_str)
        brain_agent = get_tg_chat_brain_agent(
                msg_client,
                model=em,
        )
    else:
        raise NotImplementedError(f"unsupported version: {version}")
    
    return brain_agent


def main(version=BRAIN_VERSIONS.V3_LANG_GRAPH_ReAct,
         plan_model="gpt-4o",
         execute_model="gpt-4o",
         control_agent_version=CONTROL_VERSIONS.V1_WEBSOCKETS,
         perception_version=PERCEPTION_VERSIONS.V1_SCENE_DECS,
         simulation=False,
         save_folder='',
         user_interface_version=USER_INTERFACE_VERSIONS.V1_VOICE_CLIENT,
         interact_agent = False,
         message_appending=False,
         stream=False,
         use_xunfei=True,
         asr_llm_tts=False,
         qwen=False
         ):
    brain_agent = get_brain_agent(version,
                                  plan_model,
                                  execute_model,
                                  control_agent_version,
                                  perception_version=perception_version,
                                  simulation=simulation,
                                  save_folder=save_folder,
                                  user_interface_version=user_interface_version,
                                  message_appending=message_appending,
                                  stream=stream)
    if not interact_agent:
        # 开始监听，让主程序常驻，直到遇到 ctrl-c
        brain_agent.start()
        # 清理
        brain_agent.stop()
        logger.info("Kaiwu Agent Done.")
        logging.shutdown()
    else:
        if qwen:
            assert get_interact_agent_v3 is not None
            interact_agent = get_interact_agent_v3(brain_agent, use_xunfei).start()
            interact_agent.stop()

        elif asr_llm_tts:
            assert get_interact_agent_v2 is not None
            interact_agent = get_interact_agent_v2(brain_agent).start()
            interact_agent.stop()

        else:
            assert get_interact_agent is not None
            interact_agent = get_interact_agent(brain_agent, use_xunfei).start()
            interact_agent.stop()
        logger.info("Interact Agent Done.")
        logging.shutdown()

def decide_brain_version(version_str):
    if '1' in version_str or 'xf' in version_str:
        return BRAIN_VERSIONS.V1_XF_SKILL
    elif '2' in version_str or 'intent' in version_str:
        return BRAIN_VERSIONS.V2_CUSTOM_INTENT
    elif '3' in version_str or 'langraph' in version_str:
        return BRAIN_VERSIONS.V3_LANG_GRAPH
    elif 'react' in version_str or 'ReAct' in version_str:
        return BRAIN_VERSIONS.V3_LANG_GRAPH_ReAct
    elif '4' in version_str or 'viusal' in version_str:
        return BRAIN_VERSIONS.V4_VISUAL_AGENT
    elif 'chat' in version_str:
        return BRAIN_VERSIONS.CHAT_BRAIN_AGENT
    else:
        raise NotImplementedError
    

def decide_control_version(version_str):
    if '1' in version_str:
        control_version = CONTROL_VERSIONS.V1_WEBSOCKETS 
    elif '2' in version_str: 
        control_version = CONTROL_VERSIONS.V2_FASTAPI
    elif '3' in version_str or 'lego' == version_str:
        control_version = CONTROL_VERSIONS.V3_LEGO
    elif '4' in version_str or 'chat' in version_str:
        control_version = CONTROL_VERSIONS.V4_CHAT
    elif '5' in version_str or 'lego_ur' == version_str:
        control_version = CONTROL_VERSIONS.V5_LEGO_UR
    elif 'clean' == version_str:
        control_version = CONTROL_VERSIONS.TABLE_CLEANING
    elif "box_move" == version_str:
        control_version = CONTROL_VERSIONS.BOX_MOVE
    elif "lightbulb_check" == version_str:
        control_version = CONTROL_VERSIONS.LIGHTBULB_CHECK
    elif "gift_package" == version_str:
        control_version = CONTROL_VERSIONS.GIFT_PACKAGE
    elif "electrician" == version_str:
        control_version = CONTROL_VERSIONS.ELECTRICIAN
    elif "box_move_wrc" == version_str:
        control_version = CONTROL_VERSIONS.BOX_MOVE_WRC
    elif "electrician_wrc" == version_str:
        control_version = CONTROL_VERSIONS.ELECTRICIAN_WRC
    elif "gift_package_wrc" == version_str:
        control_version = CONTROL_VERSIONS.GIFT_PACKAGE_WRC
    elif "lightbulb_check_wrc" == version_str:
        control_version = CONTROL_VERSIONS.LIGHTBULB_CHECK_WRC
    elif "sorter_wrc" == version_str:
        control_version = CONTROL_VERSIONS.SORTER_WRC
    else:
        raise NotImplementedError(version_str)
    return control_version
    
def decide_user_interface_version(version_str):
    if "voice" == version_str:
        user_interface_version = USER_INTERFACE_VERSIONS.V1_VOICE_CLIENT
    elif "ui" == version_str:
        user_interface_version = USER_INTERFACE_VERSIONS.V2_UI
    elif "compose" == version_str:
        user_interface_version = USER_INTERFACE_VERSIONS.COMPOSE
    elif "compose_wrc" == version_str:
        user_interface_version = USER_INTERFACE_VERSIONS.COMPOSE_WRC
    else:
        raise NotImplementedError
    return user_interface_version
    
if __name__ == '__main__':
    llm_choices = LLM_MODELS.keys()
    vlm_choices = VLM_MODELS.keys()
    # 创建ArgumentParser对象
    parser = argparse.ArgumentParser(description="Brain Agent输入参数")

    # 添加参数
    parser.add_argument("-v", "--version", type=str, default='v3', help="brain agent版本关键词")
    parser.add_argument("-em", "--execute_model", type=str,
                        choices=llm_choices,
                        default="gpt-41",
                        help="任务执行模型（默认:gpt-41）,可选{llm_choices}")
    parser.add_argument("-pm", "--plan_model", type=str,
                        choices=vlm_choices,
                        default="gpt-41",
                        help="任务规划模型（默认: gpt-41）,可选{vlm_choices}")
    parser.add_argument("-c", "--control_agent_version", type=str,
                        default="1",
                        help="control_agent版本，默认为1，可选为1、2、3")
    parser.add_argument("-p", "--perception_version", type=int,
                        default=1,
                        help="perception工具版本，默认为1，可选为1或者2")
    parser.add_argument("--simulation",
                        action="store_true",  # 如果提供了该选项，值为 True；否则为 False
                        help="启用仿真模式，使用config.py的image_path作为仿真图片，否则获取实时的图片。")
    parser.add_argument("-s", "--save", type=str,
                        default="assets/data",
                        # !TODO: 支持其他Agent版本
                        help="数据保存路径，目前限定于VisualBrainAgent生效。")
    parser.add_argument("--appending", action="store_true",
                        help="新消息appending而不是直接丢弃。")
    parser.add_argument("--stream", action="store_true",
                        help="流式输出文本。")
    parser.add_argument("-u", "--user", type=str,
                        default="voice",
                        choices=["voice", "ui", "compose", "compose_wrc"],
                        help="用户访问模式，目前接受voice和UI，分别是指通过对机器人说话访问和通过前端UI访问。")
    parser.add_argument("--interact_agent",
                        action="store_true",
                        help="是否启动interact agent。")
    parser.add_argument("--use_xunfei",action="store_true",help="在启动intereact agent时，使用讯飞套件。")
    parser.add_argument("--asr_llm_tts", action="store_true",help="asr-llm-tts mode for interact agent")
    parser.add_argument("--qwen", action="store_true",help="qwen-omni version for interact agent")
    # 解析参数
    args = parser.parse_args()
    brain_agent_version = decide_brain_version(args.version)
    control_agent_version = decide_control_version(args.control_agent_version)
    perception_version = PERCEPTION_VERSIONS.V1_SCENE_DECS if args.perception_version == 1 else PERCEPTION_VERSIONS.V2_PERCETION
    user_interface_version = decide_user_interface_version(args.user)

    main(
        version=brain_agent_version,
        plan_model=args.plan_model,
        execute_model=args.execute_model,
        control_agent_version=control_agent_version,
        perception_version=perception_version,
        simulation=args.simulation,
        save_folder=args.save,
        user_interface_version=user_interface_version,
        interact_agent=args.interact_agent,
        message_appending=args.appending,
        stream=args.stream,
        use_xunfei=args.use_xunfei,
        asr_llm_tts=args.asr_llm_tts,
        qwen=args.qwen
    )
