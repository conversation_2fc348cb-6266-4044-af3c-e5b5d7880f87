import time
from kaiwu_agent.utils.env import env_manager
from algo_tools.messages.msg_client import BaseMsgClient


def get_latest_instruct(msg_client: BaseMsgClient, timeout=None):
    start_time = time.time()
    while True:
        if env_manager.is_set("STOP"):
            return "receive system stop command, not wait anymore"
        else:
            # 检查是否有新消息
            msg_dict = msg_client.get_latest_msg(timeout=1)
            if msg_dict is not None:
                return msg_dict["instruction"]
            elif timeout is not None and time.time() - start_time > timeout:
                return f"wait for user response timeout {timeout}s"
    