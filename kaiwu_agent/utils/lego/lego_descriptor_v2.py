import os
import base64
import cv2
import requests
import json
import re
import httpx
import numpy as np
from openai import OpenAI
import openai
from collections import defaultdict
from kaiwu_agent.utils.common import timing_wrapper
import logging
logger = logging.getLogger(__name__)


class BlockImageProcessor:
    def __init__(self, 
                 prompt_text, 
                 api_key, 
                 det_url,
                 camera_params: dict,
                 color_map: list,
                 block_map: dict,
                 gpt_url="https://api.chatanywhere.tech/v1", 
                 model="gpt-4o"):
        # 配置API和代理
        self.prompt_text = prompt_text
        self.api_key = api_key
        self.det_url = det_url
        self.camera_params = camera_params
        self.color_map = color_map
        self.block_map = block_map
        self.gpt_url = gpt_url
        self.model = model 
        
        self.client = OpenAI(
            api_key=self.api_key, 
            base_url=self.gpt_url,
            timeout=httpx.Timeout(connect=3.0, read=10.0, write=10, pool=10),  # 连接超时 3 秒，读取超时 5 秒，总超时 8 秒
            max_retries=3,
            # default_headers={"x-stainless-skip-status-check": "true"}
        )
 
        self.lego_description = {}
    
    def encode_image(self, image_path):
        """将图片转换为base64格式"""

        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode("utf-8")

    @timing_wrapper
    def generate(self, image):
        """调用GPT生成积木序列结果"""
        logger.info(self.prompt_text)
        try:
            completion = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a helpful assistant."},
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": self.prompt_text,
                            },
                        ]
                        + [
                            {
                                "type": "image_url",
                                "image_url": {"url": f"data:image/jpeg;base64,{image}"},
                            }
                        ],
                    },
                ],
            )
            response = completion.choices[0].message.content
        except openai.APITimeoutError as err:
            logger.error(f"generate: {err}")
            response = ''

        # logger.info(response)
        return response

    @timing_wrapper
    def draw_and_crop_image(
        self, input_path, output_path, x, y, width, height, angle,
    ):
        """裁剪图像并绘制矩形框"""
        image = cv2.imread(input_path)
        if image is None:
            logger.info("无法读取输入图像，请检查路径！")
            return

        # 获取图像尺寸
        (h, w) = image.shape[:2]

        # 计算旋转中心点
        center = (w // 2, h // 2)

        # 获取旋转矩阵
        rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)

        # 执行旋转变换
        rotated_image = cv2.warpAffine(image, rotation_matrix, (w, h))

        # 在旋转后的图像上绘制矩形框
        rotated_image_with_rect = rotated_image.copy()
        # 旋转可能会改变坐标系，因此需要调整矩形框的位置
        rect_points = np.array([(x, y), (x + width, y), (x + width, y + height), (x, y + height)])
        transformed_points = cv2.transform(np.array([rect_points]), rotation_matrix)[0]

        # 绘制变换后的矩形框
        cv2.polylines(rotated_image_with_rect, [transformed_points], True, (0, 255, 0), 2)

        # 计算旋转后的矩形框的新位置
        min_x = int(min(transformed_points[:, 0]))
        max_x = int(max(transformed_points[:, 0]))
        min_y = int(min(transformed_points[:, 1]))
        max_y = int(max(transformed_points[:, 1]))

        # 裁剪旋转后的图像中的指定区域
        cropped_image = rotated_image[min_y:max_y, min_x:max_x]

        # 保存裁剪后的图像
        output_dir = os.path.dirname(output_path)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        cv2.imwrite(output_path, cropped_image)
        logger.info(f"crop image saved at: {output_path}")
        return output_path

    def load_images_from_folder(self, folder):
        """从文件夹加载图片"""
        images = []
        supported_extensions = [".png", ".jpg", ".jpeg", ".bmp", ".gif", ".tiff"]

        for root, dirs, files in os.walk(folder):
            for file in files:
                ext = os.path.splitext(file)[1].lower()
                if ext in supported_extensions:
                    img_path = os.path.join(root, file)
                    images.append(img_path)

        return images

    # 计算两个框的交集与并集的比例
    def iou(self, box1, box2):
        # 计算交集的坐标
        inter_xmin = max(box1[0], box2[0])
        inter_ymin = max(box1[1], box2[1])
        inter_xmax = min(box1[2], box2[2])
        inter_ymax = min(box1[3], box2[3])

        # 计算交集面积
        inter_width = max(0, inter_xmax - inter_xmin + 1)
        inter_height = max(0, inter_ymax - inter_ymin + 1)
        inter_area = inter_width * inter_height

        # 计算每个边界框的面积
        area_box1 = (box1[2] - box1[0] + 1) * (box1[3] - box1[1] + 1)
        area_box2 = (box2[2] - box2[0] + 1) * (box2[3] - box2[1] + 1)

        # 计算并返回IoU
        iou = inter_area / float(area_box1 + area_box2 - inter_area)
        return iou

    def nms(self, bboxes, scores, threshold=0.9):
        if len(bboxes) == 0:
            return []

        # 将输入转换为numpy数组以便于处理
        bboxes = np.array(bboxes)
        scores = np.array(scores)

        # 获取按分数降序排列的索引
        order = scores.argsort()[::-1]

        keep = []  # 用来保存最终保留的边界框索引
        while order.size > 0:
            # 将当前得分最高的框加入结果中
            index_maxscore = order[0]
            keep.append(index_maxscore)

            # 计算其余边界框与当前最高分边界框的IoU
            ious = np.array(
                [self.iou(bboxes[index_maxscore], bboxes[i]) for i in order[1:]]
            )

            # 保留低于阈值的边界框索引
            indices = np.where(ious <= threshold)[0]

            # 更新order以进行下一轮循环
            order = order[indices + 1]  # 因为忽略了第一个元素，所以需要+1

        return keep

    @timing_wrapper
    def segment_objects(self, local_image_path, res, block_map):
        """调用后端API进行物体分割"""
        res = res + " large size blue block." + " small size red block." + " medium size yellow block." + " medium size green block."
        prompt_text = f"{res}"
        sub_prompt = list(filter(None, prompt_text.split(". ")))
        sub_prompt = [item.lower() for item in sub_prompt]

        files = {"picture": ("1_2.jpg", open(local_image_path, "rb"), "image/jpeg")}

        # 按句号拆分并去除空格

        blocks = [block.strip() for block in prompt_text.split(".") if block.strip()]

        # 使用 map 进行转换
        mapped_blocks = [block_map[block] for block in blocks if block in block_map]

        prompt_text = ". ".join(mapped_blocks) + "."
        # 发送POST请求
        logger.info(prompt_text)
        url = f"{self.det_url}?prompt={prompt_text}?"

        try:
            response = requests.post(url, 
                                     files=files,
                                     timeout=(5, 15))  # 最多5秒连接，最多15秒读取
            response_json = response.json()
        except requests.exceptions.ConnectTimeout:
            logger.info("连接超时")
            return "连接超时"
        except requests.exceptions.ReadTimeout:
            logger.info("读取超时")
            return "读取超时"
        except requests.exceptions.Timeout:
            logger.info("请求超时")
            return "请求超时"
        except requests.exceptions.RequestException as e:
            logger.info("请求失败:", e)
            return "请求失败"

        # 去除末尾的点
        response_json["res"]["labels"] = [
            x.strip(".") for x in response_json["res"]["labels"]
        ]
        # 去除labels中的空值及其对应的bbox和score
        if "res" in response_json and all(
            key in response_json["res"] for key in ["scores", "bboxes", "labels"]
        ):
            scores = response_json["res"]["scores"]
            bboxes = response_json["res"]["bboxes"]
            labels = response_json["res"]["labels"]

            # 确保所有三个列表都有数据
            if scores and bboxes and labels:
                filtered_scores, filtered_bboxes, filtered_labels = zip(
                    *[
                        (score, bbox, label)
                        for score, bbox, label in zip(scores, bboxes, labels)
                        if label
                    ]
                )

                response_json["res"]["scores"] = list(filtered_scores)
                response_json["res"]["bboxes"] = list(filtered_bboxes)
                response_json["res"]["labels"] = list(filtered_labels)
            else:
                logger.info("One or more lists are empty.")
        else:
            logger.info("'res' key not found or missing required keys.")

        reversed_block_map = {value: key for key, value in block_map.items()}
        logger.info(response_json["res"]["labels"])
        response_json["res"]["labels"] = [
            reversed_block_map[label] for label in response_json["res"]["labels"]
        ]

        # 调用nms函数
        keep_indices = self.nms(
            response_json["res"]["bboxes"],
            response_json["res"]["scores"],
            threshold=0.9,
        )

        for key in ["bboxes", "scores", "labels"]:
            response_json["res"][key] = [
                response_json["res"][key][i] for i in keep_indices
            ]

        # 返回结果
        return response_json

    def calculate_len(self, objects, grid_distance=45):
        """
        计算积木的长度大小
        """
        boxes = objects["res"]["bboxes"]
        labels = objects["res"]["labels"]
        lens = {}

        for i, lb in enumerate(labels):
            # import ipdb;ipdb.set_trace()
            key = lb
            for k, v in enumerate(lens):
                if v == lb:
                    key = lb + f"{i}"

            block_len = round((boxes[i][2] - boxes[i][0]) / grid_distance)
            logger.info(f"{block_len}, {(boxes[i][2] - boxes[i][0]) / grid_distance}")
            lens[key] = block_len

        return lens

    def group_and_normalize(self, layer, layer_threshold):
        # 第一步：初始化变量
        grouped = defaultdict(list)  # 用于存储分组后的键值对
        used_keys = set()  # 用于跟踪已经处理过的键
        # 第二步：根据阈值分组
        for key1, value1 in layer.items():
            if key1 in used_keys:
                continue
            group_key = key1  # 使用第一个键作为组的标识符
            for key2, value2 in layer.items():
                if abs(value1 - value2) <= layer_threshold and key2 not in used_keys:
                    grouped[group_key].append((key2, value2))
                    used_keys.add(key2)
            if group_key not in used_keys:  # 确保自身也被加入组中
                grouped[group_key].append((group_key, value1))
                used_keys.add(group_key)

        # 第三步：计算每个组的平均值
        averages = []
        for group_key, items in grouped.items():
            avg_value = sum(item[1] for item in items) / len(items)
            averages.append((group_key, avg_value))

        # 第四步：根据平均值排序，并为每个组分配新的值
        sorted_averages = sorted(averages, key=lambda x: x[1], reverse=True)
        new_values = {item[0]: idx for idx, item in enumerate(sorted_averages)}

        # 第五步：创建新的字典，应用新的值
        new_layer = {}
        for group_key, items in grouped.items():
            for key, _ in items:
                new_layer[key] = new_values[group_key]
        return new_layer
    
    def calculate_pos(self, objects, head_height, grid_distance=45, layer_threshold=50):
        """计算积木的位置坐标"""
        res = objects["res"]
        # key - value ：颜色 - （高度/层数，横向位置）
        position = {}
        # key - value ：颜色 - 图片像素横向坐标
        left_side_pos = {}
        # key - value ：颜色 - 图片像素高度
        layer = {}

        for i, lb in enumerate(res["labels"]):
            key = lb
            for k, v in enumerate(position):
                if v == lb:
                    key = lb + f"{i}"
            # 位置为元组类型，（高度/层数，横向位置）
            pos = (0, 0)
            position[key] = pos
            # 最左边坐标
            left_side = res["bboxes"][i][0]
            left_side_pos[key] = left_side
            # 如果上下边差值大于阈值，认为检测框框到了大头，减去头部高度
            if (abs(res["bboxes"][i][1] - res["bboxes"][i][3]) > (layer_threshold + head_height) * 0.83):
                res["bboxes"][i][1] += head_height
            # 求高度，方法：（上边 + 下边）/ 2
            high = (res["bboxes"][i][1] + res["bboxes"][i][3]) / 2
            layer[key] = high

        new_layer = self.group_and_normalize(layer, layer_threshold)

        new_layer = sorted(
            new_layer.items(),
            key=lambda x: (x[1], left_side_pos[x[0]]),
        )

        # 设置第0层的积木位置
        bot_pos = left_side_pos[next(iter(new_layer))[0]]
        bot_pos_format = 0

        # 计算积木的横向位置
        for i, tuple_item in enumerate(new_layer):
            # 假设永远将第0层的积木摆放在位置0
            if i > 0:
                # 获取当前和上一层积木的位置
                current_pos = left_side_pos[tuple_item[0]]
                # 计算上当前积木和第0层积木位置的差值
                diff_pos = abs((current_pos - bot_pos))
                # 如果差值大于grid_distance（一格的距离）
                if diff_pos > grid_distance:
                    # 当前积木比第0层积木位置值大
                    if current_pos > bot_pos:
                        # 大多少个grid_distance就加多少格的距离
                        current_pos_format = round(
                            bot_pos_format + diff_pos / grid_distance
                        )
                    # 当前积木比第0层积木位置值小
                    else:
                        # 小多少个grid_distance就减多少格的距离
                        current_pos_format = round(
                            bot_pos_format - diff_pos / grid_distance
                        )
                # 如果当前积木比第0层积木位置值大，且差值大于处于[grid_distance-20, grid_distance]的距离
                elif diff_pos <= grid_distance and diff_pos > (grid_distance * 0.8):
                    if current_pos > bot_pos:
                        current_pos_format = round(
                            bot_pos_format + diff_pos / grid_distance
                        )
                    else:
                        current_pos_format = round(
                            bot_pos_format - diff_pos / grid_distance
                        )
                # 如果当前积木与第0层积木位置的差值小于grid_distance（一格的距离）
                else:
                    # 和第0层积木的位置值相等
                    current_pos_format = bot_pos_format
                position[tuple_item[0]] = (new_layer[i][1], current_pos_format)
        return position

    def transform_data(
        self,
        size_dict,
        pos_dict,
        color_map=["blue", "red", "green", "yellow"],
    ):
        """把计算出来的大小和位置转换成json格式的积木信息"""
        block_info_list = []
        # 记录pos值正负
        negative_flag = 0
        # 记录最小的负值
        negative_value = 0
        for key in size_dict:
            key.lower()
            layer_info = {
                "layer": None,
                "color": None,
                "shape": (1, size_dict[key]),
                "pos": None,
            }

            # 提取层号（假设层号从0开始）
            layer_info["layer"] = pos_dict[key][0]

            # 提取颜色
            for color in color_map:
                if color in key:
                    layer_info["color"] = color

            # 设置位置
            layer_info["pos"] = pos_dict[key]
            # 如果积木的位置小于0，则设置flag = 1，并记录最小的负值
            if pos_dict[key][1] < 0:
                negative_flag = 1
                if pos_dict[key][1] < negative_value:
                    negative_value = pos_dict[key][1]

            block_info_list.append(layer_info)

        # 如果有负值，则将所有积木的位置都加上最小的负值，使得所有的位置都是正数
        if negative_flag == 1:
            for block_info in block_info_list:
                block_info["pos"] = (
                    block_info["pos"][0],
                    block_info["pos"][1] - negative_value,
                )

        # 构建最终的 JSON 格式
        block_info_list = sorted(
            block_info_list, key=lambda x: (x["pos"][0], x["pos"][1])
        )
        result = {"block_info": block_info_list}

        return result

    def write_to_json(self, data, output_json_path):
        """将数据写入JSON文件"""
        try:
            with open(output_json_path, "w", encoding="utf-8") as json_file:
                json.dump(data, json_file, ensure_ascii=False, indent=4)
            logger.info(f"数据已成功写入 {output_json_path}")
        except Exception as e:
            logger.info(f"写入 JSON 文件时出错: {e}")

    def cv_imageTreat(
        self, json_resp, image_path, index, output_path, visualization=False
    ):
        """根据API返回的数据在图像上绘制边框"""
        data = json_resp
        objects = data["res"]["bboxes"]
        labels = data["res"]["labels"]
        boxes = []

        for i, bbox in enumerate(objects):
            boxes.append((bbox, labels[i] if i < len(labels) else ""))

        image = cv2.imread(image_path)

        if boxes:
            for i, (bbox, label) in enumerate(boxes):
                x_min, y_min, x_max, y_max = map(int, bbox)
                rectangle_img = cv2.rectangle(
                    image, (x_min, y_min), (x_max, y_max), (0, 0, 255), 2
                )
                label_position = (
                    (x_min, y_min - 10) if y_min - 10 > 10 else (x_min, y_min + 20)
                )
                rectangle_img = cv2.putText(
                    rectangle_img,
                    label,
                    label_position,
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.5,
                    (0, 255, 0),
                    1,
                    cv2.LINE_AA,
                )

        else:
            rectangle_img = image
            logger.info("没有识别出有效边框")

        output_filename = f"{output_path}/rectangle_image_{index + 1}.jpg"
        if not os.path.exists(output_path):
            os.makedirs(output_path)
        cv2.imwrite(output_filename, rectangle_img)
        logger.info(f"画框后的图像已保存: {output_filename}")
    
    def process_image(self, initial_image_path, cropped_image_path, x, y, width, height, angle):
        if os.path.isdir(initial_image_path):
            image_files = [
                os.path.join(initial_image_path, f)
                for f in os.listdir(initial_image_path)
                if f.endswith((".png", ".jpg", ".jpeg", ".gif"))
            ]
        else:
            image_files = [initial_image_path]

        cropped_image_path_list = []
        os.makedirs(cropped_image_path, exist_ok=True)

        for i, image in enumerate(
            image_files
        ):  # self.load_images_from_folder(initial_image_path)):
            cropped_image_path_list.append(
                self.draw_and_crop_image(
                    image,
                    os.path.join(cropped_image_path, f"cropped_image_{i}.jpg"),
                    x,
                    y,
                    width,
                    height,
                    angle,
                )
            )
        return cropped_image_path_list
    
    def vlm_inference(self, cropped_image_path_list):
        base64_images = {}

        for i in range(len(cropped_image_path_list)):
            base64_images[f"base64_image{i}"] = self.encode_image(
                os.path.join(cropped_image_path_list[i])
            )

        responses = [
            self.generate(base64_images[f"base64_image{i}"])
            for i in range(len(base64_images))
        ]

        logger.info(responses)
        return responses
    
    def process_detection(self,
                          cropped_image_path_list,
                          responses, 
                          block_map, 
                          rectangle_image_path, 
                          grid_distance, 
                          head_height, 
                          layer_threshold, 
                          color_map):
        output_jsons = []
        for i, image in enumerate(cropped_image_path_list):
            response_json = self.segment_objects(image, responses[i], block_map)
            if isinstance(response_json, str):
                return response_json 
            elif isinstance(response_json, dict) and response_json["res"]["labels"] == []:
                return "没有检测到积木"

            self.cv_imageTreat(response_json, image, i, rectangle_image_path)

            # 利用边框信息计算每层积木的长度和位置
            lens = self.calculate_len(response_json, grid_distance)

            label_pos = self.calculate_pos(
                response_json, head_height, grid_distance, layer_threshold
            )
            output_json = self.transform_data(lens, label_pos, color_map)
            # output_jsons一个列表，保存了每张图片的积木信息{"block_info":["layer": 0"color": "blue","shape": "1×3","pos": "(0,0)"]}
            output_jsons.append(output_json)
        return output_jsons
    
    @timing_wrapper
    def execute(
        self,
        initial_image_path,
        result_dir
    ):
        """执行所有步骤"""
        
        cropped_image_path = result_dir                 # 裁剪后图像的保存路径
        output_json_path = f"{result_dir}/result.json"  # 输出json文件路径
        rectangle_image_path = result_dir               # 带有检测边界框的图像保存目录
        x, y, width, height = self.camera_params["crop_size"]
        angle = self.camera_params["angle"]
        head_height = self.camera_params["head_height"]
        grid_distance = self.camera_params["grid_distance"]
        layer_threshold = self.camera_params["layer_threshold"]
        color_map = self.color_map
        block_map = self.block_map
        
        # 1. 裁剪图片
        cropped_image_path_list = self.process_image(initial_image_path, 
                                                     cropped_image_path, 
                                                     x, 
                                                     y, 
                                                     width,
                                                     height,
                                                     angle)
        # 2. 生成图像的base64编码并获取GPT的响应
        responses = self.vlm_inference(cropped_image_path_list)
        
        # 3. 检测出每块积木的边框，利用边框信息计算每层积木的长度和位置，生成json格式结果
        output_jsons = self.process_detection(cropped_image_path_list, responses, 
                          block_map, 
                          rectangle_image_path, 
                          grid_distance, 
                          head_height, 
                          layer_threshold, 
                          color_map)
        # 4. 输出结果到JSON文件
        self.write_to_json(output_jsons, output_json_path)

        # 5. save gpt responses and lego description results for lego examiner
        self.lego_description = {"gpt_response":responses, "description_res":output_jsons}
        return output_jsons
    

    def postprocess_examine(self, description, examine):
        """ 
        compare description & examiner result.
        if first layer first left block has same color & size,  assume first block is the same block
        horizontal move total examiner result position 
        """
        description_blocks = description[0]['block_info']
        try:
            examine_blocks = examine[0]["block_info"]
        except Exception as e:
            logger.error(f"No block detected!!!Please check!!!")
            # logger.error(f"Error: {e}")
            return []

        des_init_block = description_blocks[0]
        examine_init_block = examine_blocks[0]

        #if first layer left most block has difference
        if des_init_block != examine_init_block:
            keys = ["layer", "color", "shape"]
            same_init_block = True
            for key in keys:
                if des_init_block[key] != examine_init_block[key]:
                    same_init_block = False
                    break

            # if first layer left most block in same layer, has same color&shape, only different in pos.
            # calculate init block horizontal dif, assign dif to total blocks for horizontal movement
            if same_init_block:
                des_pos = des_init_block["pos"][1]
                exm_pos = examine_init_block["pos"][1]
                pos_dif = exm_pos-des_pos
                
                for block_index in range(len(examine_blocks)):
                    
                    # 获取当前 pos 元组
                    current_pos = examine_blocks[block_index]["pos"]

                    # 修改元组中的第二个值
                    new_pos = (current_pos[0], current_pos[1] - pos_dif)

                    # 将新元组重新赋值给 "pos"
                    examine_blocks[block_index]["pos"] = new_pos
                # logger.info(examine_blocks)
                examine[0]["block_info"] = examine_blocks
                logger.info(f"horizontal move {pos_dif} position")
                return examine 
            else:
                logger.error(f"Different from first block!!!Please check!!!")
        #if first layer left most block are same
        else:
            return examine
        
    def compare_blocks(self, description_blocks, examine_blocks):
        """compare description block results & examiner blocks results.
        
        Args:
            description_blocks: description block results list.
            examine_blocks: examiner blocks results list.

        Error Type:
            extra_block: extra block appear during examine, will return extra block data in "block_info"
            missing_block: missed block during examine will return missed block data in "block_info"
            key_failed: failed in one key:[color/shape/pos],return: 
                        "failed_key": failed_key type
                        "groundtruth": gt block data of description for failed block
                        "inference": examiner block result for failed block
                        "block_info": failed block correct part keys info


        Returns:
            List of differences of blocks result between description&examiner.
        """

        #for saving differences results
        differences = []
        # 构建三种映射关系
        # 1. 按(layer, pos, color)映射 - 检查形状差异
        examine_shape_map = {}  # {(layer, pos, color): shape}
        desc_shape_map = {}
        
        # 2. 按(layer, shape, pos)映射 - 检查颜色差异
        examine_color_map = {}  # {(layer, shape, pos): color}
        desc_color_map = {}
        
        # 3. 按(layer, color, shape)映射 - 检查位置差异
        examine_pos_map = defaultdict(list)  # {(layer, color, shape): [pos1, pos2]}
        desc_pos_map = defaultdict(list)

        # 处理examine.json
        for block in examine_blocks:
            # 用于形状检查
            shape_key = (block['layer'], tuple(block['pos']), block['color'])
            examine_shape_map[shape_key] = tuple(block['shape'])
            
            # 用于颜色检查
            color_key = (block['layer'], tuple(block['shape']), tuple(block['pos']))
            examine_color_map[color_key] = block['color']
            
            # 用于位置检查
            pos_key = (block['layer'], block['color'], tuple(block['shape']))
            examine_pos_map[pos_key].append(tuple(block['pos']))
            
        # 处理description.json
        for block in description_blocks:
            # 用于形状检查
            shape_key = (block['layer'], tuple(block['pos']), block['color'])
            desc_shape_map[shape_key] = tuple(block['shape'])
            
            # 用于颜色检查
            color_key = (block['layer'], tuple(block['shape']), tuple(block['pos']))
            desc_color_map[color_key] = block['color']
            
            # 用于位置检查
            pos_key = (block['layer'], block['color'], tuple(block['shape']))
            desc_pos_map[pos_key].append(tuple(block['pos']))
        
        # 记录已报告的差异，避免重复报告
        reported_blocks = set()

        # 1. 首先检查形状差异 (相同layer,pos,color但shape不同)
        all_shape_keys = sorted(set(examine_shape_map.keys()) & set(desc_shape_map.keys()))
        for key in all_shape_keys:
            if examine_shape_map[key] != desc_shape_map[key]:
                layer, pos, color = key
                reported_blocks.add((layer, pos, color))
                logger.info(
                    f"Failed key: shape. \nblock detail: layer={layer}, pos={list(pos)}, color={color}"
                    f"groundtruth: {list(desc_shape_map[key])}, inference: {list(examine_shape_map[key])}\n"
                )
                differences.append({"error_type":"key_failed", "failed_key": "shape", "groundtruth":list(desc_shape_map[key]), "inference": list(examine_shape_map[key]), "block_info":{"layer":layer, "pos": list(pos), "color":color}})

        # 2. 然后检查颜色差异 (相同layer,shape,pos但color不同)
        all_color_keys = sorted(set(examine_color_map.keys()) & set(desc_color_map.keys()))
        for key in all_color_keys:
            if examine_color_map[key] != desc_color_map[key]:
                layer, shape, pos = key
                # 检查是否已经因为形状差异报告过
                if (layer, pos, examine_color_map[key]) not in reported_blocks and \
                (layer, pos, desc_color_map[key]) not in reported_blocks:
                    reported_blocks.add((layer, pos, examine_color_map[key]))
                    reported_blocks.add((layer, pos, desc_color_map[key]))
                    logger.info(
                        f"Failed key: color. \nblock detail: layer={layer}, size={list(shape)}, pos={list(pos)}"
                        f"groundtruth: {desc_color_map[key]}, inference: {examine_color_map[key]}"
                    )
                    differences.append({"error_type":"key_failed","failed_key": "color", "groundtruth":desc_color_map[key], "inference": examine_color_map[key], "block_info":{"layer":layer, "pos": list(pos), "shape":list(shape)}})

        # 3. 最后检查位置差异 (相同layer,color,shape但pos不同)
        all_pos_keys = sorted(set(examine_pos_map.keys()) | set(desc_pos_map.keys()))
        for pos_key in all_pos_keys:
            layer, color, shape = pos_key
            
            # 检查是否有已报告的差异
            skip = False
            for pos in examine_pos_map.get(pos_key, []) + desc_pos_map.get(pos_key, []):
                if (layer, pos, color) in reported_blocks:
                    skip = True
                    break
            if skip:
                continue
                
            # 获取排序后的位置列表以便比较
            ex_positions = sorted(examine_pos_map.get(pos_key, []))
            desc_positions = sorted(desc_pos_map.get(pos_key, []))
            
            # 如果两边都有该类型积木
            if pos_key in examine_pos_map and pos_key in desc_pos_map:
                # 如果积木数量相同，比较每个位置
                if len(ex_positions) == len(desc_positions):
                    for ex_pos, desc_pos in zip(ex_positions, desc_positions):
                        if ex_pos != desc_pos:
                            logger.info(
                                f"Failed key: position. \nblock detail: layer={layer}, color={color}, size={list(shape)}"
                                f"groundtruth: {list(desc_pos)}, inference: {list(ex_pos)}"
                            )
                            differences.append({"error_type":"key_failed","failed_key": "position", "groundtruth":list(desc_pos), "inference": list(ex_pos), "block_info":{"layer":layer, "color": color, "shape":list(shape)}})
                else:
                    # 积木数量不同，报告缺失或多余
                    ex_set = set(ex_positions)
                    desc_set = set(desc_positions)
                    
                    for pos in ex_set - desc_set:
                        # logger.info(
                        #     f"Extra Block: layer={layer}, color={color}, size={list(shape)}, pos={list(pos)}\n"
                        # )
                        differences.append({"error_type":"extra_block", "block_info":{"layer":layer, "color": color, "shape":list(shape), "pos":list(pos)}})
                    for pos in desc_set - ex_set:
                        logger.info(
                            f"Missing Block: layer={layer}, color={color}, size={list(shape)}, pos={list(pos)}"
                        )
                        differences.append({"error_type":"missing_block", "block_info":{"layer":layer, "color": color, "shape":list(shape), "pos":list(pos)}})
            else:
                # 一边完全没有该类型积木
                if pos_key in examine_pos_map:
                    for pos in ex_positions:
                        logger.info(
                            f"Extra Block: layer={layer}, color={color}, size={list(shape)}, pos={list(pos)}"
                        )
                        differences.append({"error_type":"extra_block", "block_info":{"layer":layer, "color": color, "shape":list(shape), "pos":list(pos)}})
                else:
                    for pos in desc_positions:
                        logger.info(
                            f"Missing Block: layer={layer}, color={color}, size={list(shape)}, pos={list(pos)}"
                        )
                        differences.append({"error_type":"missing_block", "block_info":{"layer":layer, "color": color, "shape":list(shape), "pos":list(pos)}})

        return differences
    
    # postprocess description, set first layer left most block pos x = 0
    def postprocess_description(self, description):
        description_blocks = description[0]['block_info']
        if description_blocks[0]["pos"] == (0, 0):
            return description
        else:
            # layer left most block pos x 
            dif = description_blocks[0]["pos"][1]

            # horizontal move whole blocks
            for cur_block in description_blocks:
                current_pos = list(cur_block["pos"])
                new_pos = (current_pos[0], current_pos[1] - dif)
                cur_block["pos"] = new_pos

            return description


    def block_examiner(
        self, 
        initial_image_path,
        result_dir,
        lego_description,
        index
    ):
        cropped_image_path = result_dir                 # 裁剪后图像的保存路径
        output_json_path = f"{result_dir}/result.json"  # 输出json文件路径
        rectangle_image_path = result_dir               # 带有检测边界框的图像保存目录
        x, y, width, height = self.camera_params["crop_size"]
        angle = self.camera_params["angle"]
        head_height = self.camera_params["head_height"]
        grid_distance = self.camera_params["grid_distance"]
        layer_threshold = self.camera_params["layer_threshold"]
        color_map = self.color_map
        block_map = self.block_map
        
        # If no block description fetched
        if len(lego_description)==0:
            return {"status": False, "info":"No Lego description fetched"}
        
        # 1. 裁剪图片
        cropped_image_path_list = self.process_image(initial_image_path, 
                                                     cropped_image_path, 
                                                     x, 
                                                     y, 
                                                     width,
                                                     height,
                                                     angle)

        # 2. 获取积木描述时得到的bbox文本
        #'large size blue block. small size red block. large size blue block. small size red block. small size blue block. small size blue block. medium size yellow block. medium size yellow block. medium size green block. medium size green block'
        stack_description = lego_description["gpt_response"]
        
        # 3. 检测出每块积木的边框，利用边框信息计算每层积木的长度和位置，生成json格式结果
        output_jsons = self.process_detection(cropped_image_path_list, stack_description, 
                                block_map, 
                                rectangle_image_path, 
                                grid_distance, 
                                head_height, 
                                layer_threshold, 
                                color_map)
        # empty result
        if output_jsons == []:
            logger.error(f"No block detected!!!Please check!!!")
            return {
                "status": False,
                "info": "No block detected!",
                "output_jsons": []
            }
        
        # set description first layer left most block as 
        lego_description["description_res"] = self.postprocess_description(lego_description["description_res"])
        import time
        post_tic = time.time()
        examiner_result = self.postprocess_examine(lego_description["description_res"], output_jsons)
        post_toc = time.time()
        # logger.info(f"postprocess examiner result takes time:{post_toc-post_tic}")

        try:
            examiner_res = examiner_result[0]["block_info"]
        except Exception as e:
            logger.error(f"Error: {e}")
            examiner_res = []  # 设置默认值，避免后续索引访问错误
        
        # toc = time.time()
        # logger.info(f"examiner inference takes time:{toc-tic}")

        # 4. 输出结果到JSON文件
        self.write_to_json(examiner_result, output_json_path)

        init_description = lego_description["description_res"][0]["block_info"]
        
        # get current stacked block description
        stack_index = int(index)
        gt = init_description[:stack_index+1]
        
        
        errors = []  # 收集所有错误信息
        tic = time.time()
        errors = self.compare_blocks(gt, examiner_res)
        toc = time.time()
        if not errors:
            return {
                "status": True,
                "info": {},
                "output_jsons": examiner_result
            }
        else:
            return {
                "status": False,
                "info": {"errors": errors},
                "output_jsons": examiner_result
            }
