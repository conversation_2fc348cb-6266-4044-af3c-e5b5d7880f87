
import os
import requests
import cv2
import time
import os.path as osp
from openai import OpenAI
import base64
from kaiwu_agent.utils.image_process import encode_base64, resize_image, to_byte
import logging
logger = logging.getLogger()


Level_CFG = {
    "3": {
        # "dy_offset": 30,
        # "dx_fixed": 47,
        "dy_offset": 47,
        "dx_fixed": 80,
    },
    "4": {
        "dy_offset": 50,
        "dx_fixed": 80,
    }
}


class LegoDescriptor:
    def __init__(self, 
                 num_level,
                 det_url, 
                 gpt_api_key, 
                 gpt_url=None, 
                 model="gpt-4o", 
                 resize_factor=1.0,
                 prompt_text="blocks", 
                 prompt_text_gpt="描述图片中的场景", 
                 output_folder=None):
        """
        初始化图像处理器
        :param input_folder: 输入图片的文件夹路径
        :param output_folder: 输出图片的文件夹路径
        :param det_url: 接口 URL，用于请求检测， 当前接口"http://*************:8003/segment_objects" 暂不提供稳定接口
        :param prompt_text: 提示文本，用于接口的请求参数
        """
        assert resize_factor > 0, resize_factor
        
        self.num_level = num_level
        self.det_url = det_url
        self.gpt_api_key = gpt_api_key
        self.gpt_url = gpt_url
        self.model = model
        self.resize_factor = resize_factor
        self.prompt_text = prompt_text
        self.prompt_text_gpt = prompt_text_gpt
        self.output_folder = output_folder
        
        self.line_color =  (0, 0, 255) # (0, 255, 0)  # 绿色线条
        self.font = cv2.FONT_HERSHEY_SIMPLEX
        self.font_scale = 0.8 * resize_factor  # 字体大小
        self.font_thickness = max(1, int(2 * resize_factor))  # 字体厚度
        
        draw_cfg = Level_CFG[str(num_level)]
        self.dx_fixed = int(draw_cfg["dx_fixed"] * resize_factor)  # 固定网格宽度 当前位置 1x1的积木宽度
        self.dy_offset = int(draw_cfg["dy_offset"] * resize_factor)
        
        # 创建输出文件夹（如果不存在）
        if output_folder is not None:
            os.makedirs(self.output_folder, exist_ok=True)

        self.client = OpenAI(
            api_key=self.gpt_api_key, 
            base_url=self.gpt_url
        )

    def encode_image(self, image_path) -> str:
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    def generate(self, image):
        """gpt生成积木序列结果"""
        completion = self.client.chat.completions.create(
            model=self.model,
            messages=
            [
                {"role": "system", "content": "You are a helpful assistant."},
                {
                    "role": "user",
                    "content":
                        [
                            {
                                "type": "text",
                                "text": self.prompt_text_gpt
                            },
                        ] + [
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{[image]}"
                                }
                            }
                        ]
                }
            ],
            # temperature=0.1
        )

        response = completion.choices[0].message.content
        return response

    def process_image(self, image, basename='kaiwu_lego_realtime_img.png'):
        """
        处理单张图片，画框和坐标系
        :param image: cv2 image
        """
        start_time = time.time()

        # resize以便加速
        if self.resize_factor != 1.0:
            image = resize_image(image, self.resize_factor)
            
        # 将图像编码为字节流
        byte_image = to_byte(image, ext='.jpg')
        if not byte_image:
            logger.info("图像编码失败")
            return None

        # 调用接口获取 boxes
        files = {
            # "picture": (osp.basename(image_path), open(image_path, "rb"), "image/jpeg")
            "picture": (basename, byte_image, "image/jpeg")
        }
        response = requests.post(f"{self.det_url}?prompt={self.prompt_text}", files=files)
        response_data = response.json()
        
        det_time = time.time()

        if response_data.get("status") != 200 or "boxes" not in response_data["res"]:
            logger.info(f"No valid response for image: {basename}")
            return None

        boxes = response_data["res"]["boxes"]
        print(f"boxes: \n{boxes}")
        
        if len(boxes) == 0:
            return "错误：图像中没有找到积木"
        elif len(boxes) > 1:
            return "错误：图像中出现多个积木样例，请移走其它积木"
        
        try:
            self._draw_box_with_grid(image, boxes[0])
        except Exception as err:
            logger.error(err)
            return "错误：在把检测框绘制到图像上时出现错误"

        # TODO: 边界判断
        x_min, y_min, x_max, y_max = map(int, boxes[0])
        x_min -= 20
        x_max += 20
        y_min -= 20
        y_max += 20
        image = image[y_min:y_max, x_min:x_max]

        # 保存处理后的图像
        if self.output_folder is not None:
            name, ext = osp.splitext(basename)
            output_path = osp.join(self.output_folder, name + "_det" + ext)
            cv2.imwrite(output_path, image)
            logger.info(f"Image with det boxes saved: {output_path}")
        
        draw_time = time.time()
        
        # TODO
        # output_path = "outputs/lego/20241227-192230-RED.jpg" 
        # print(output_path)
        # base64_image = self.encode_image(output_path)

        base64_image = encode_base64(image, ext='.jpg')
        res = self.generate(base64_image)
        
        # 计时
        vlm_time = time.time()
        
        det_cost = (det_time - start_time) * 1000
        logger.info(f"det cost time: {det_cost:.1f}ms")
        
        draw_cost = (draw_time - det_time) * 1000
        logger.info(f"draw cost time: {draw_cost:.1f}ms")
        
        vlm_cost = (vlm_time - draw_time) * 1000
        logger.info(f"vlm cost time: {vlm_cost:.1f}ms")
        
        total_cost = (time.time() - start_time) * 1000
        logger.info(f"total cost time: {total_cost:.1f}ms")
        return res
    
    def _draw_box_with_grid(self, image, box):
        """
        在图片上画框和坐标系
        :param image: 输入图像
        :param box: 包含框的坐标 [x_min, y_min, x_max, y_max]
        """
        # 偏移框的 Y 值
        box = [box[0], box[1] + self.dy_offset, box[2], box[3]]  # box + self.dy_offset 去掉突起
        x_min, y_min, x_max, y_max = map(int, box)
        
        # 绘制框
        cv2.rectangle(image, (x_min, y_min), (x_max, y_max), self.line_color, 2)

        # 左下角点和框的宽高
        origin_x, origin_y = x_min, y_max  # 左下角点作为坐标原点
        width = x_max - x_min
        height = y_max - y_min

        # 固定宽度 dx 和高度 dy
        dx = self.dx_fixed  # 固定网格宽度
        dy = height // self.num_level  # 保持 Y 方向 4 段均分
        num_vertical_lines = max(1, width // dx + 1)  # 计算竖线数量（至少 1 条）

        # 绘制竖线
        for i in range(num_vertical_lines):
            x = origin_x + i * dx
            if x > x_max:  # 超出框范围则跳过
                break
            cv2.line(image, (x, y_min), (x, y_max), self.line_color, 2)

        # 绘制横线
        for j in range(self.num_level + 1):  # 画横线（4段 -> 5条线包括边界）
            y = origin_y - j * dy
            cv2.line(image, (x_min, y), (x_max, y), self.line_color, 2)

        # 标注坐标系点
        for i in range(num_vertical_lines):  # X 方向
            for j in range(self.num_level):  # Y 方向
                coord_x = origin_x + i * dx
                coord_y = origin_y - j * dy
                if coord_x > x_max or coord_y < y_min:  # 坐标超出框范围则跳过
                    continue
                text = f"({j},{i})"  # 新坐标格式：宽方向在前，高方向在后
                # 在每个网格点处添加坐标
                cv2.putText(image, text, (coord_x + 5, coord_y - 5),  
                            self.font, self.font_scale, self.line_color, self.font_thickness)
