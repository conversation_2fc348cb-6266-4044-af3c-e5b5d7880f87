from openai import OpenAI
import base64
import time

class Monitor:
    def __init__(self,
                 monitor_api_key="EMPTY", 
                 monitor_api_url="http://10.10.122.239:8008/v1", 
                 monitor_model_name="Qwen2-VL-7B-block-monitor", 
                 ):
        self.monitor_api_key = monitor_api_key
        self.monitor_base = monitor_api_url
        self.monitor_model_name = monitor_model_name
        self.client = OpenAI(
            api_key=self.monitor_api_key, 
            base_url=self.monitor_base
        )
        self.system_prompt = self.make_sys_prompt()

    def encode_image(self, image_path):
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    
    def make_sys_prompt(self):
        sys_prompt = """You are a helpful assistant. Assume you are good at lego block anomaly detection.
        The anomaly scenario types showed below:
        1. Block color dismatch: Lego block color in the input image not match the step instruct shows. example:The forth layer in instruct is blue block, but in the image the forth layer block color is yellow.
        2. Block Tilt: There is one or several layer of Lego block placed tilted in the input image.
        3. Block size dismatch: The block size in the input image not match the. exameple:The first layer in instruct is 1*2 size block, but in the image the first layer block size is 1*3.
        4. Block Gap: There is a gap between the placement of the two blocks.

        You output should ONLY be a JSON object with the following structure:
        {"result": status_result, "reason": status_explanation}

        the status_result in the json can be ["Succeed", "Failed", "Running"], 
        1. If the Instruct of block place has been finished successfully without any kind of anomaly scenario, status_result is "Succeed".
        2. If there is ANY kind of anomaly scenario type occured, status_result is "Failed".
        3. If there is nothing wrong but the task is still not finished, status_result is "Running".

        The status_explanation can be:
        a. When the status_result is "Succeed", status_explanation is None.
        b. When the status_result is "Failed", status_explanation first show the anomaly scenario type, and describe the status as the image shows."example":"anomaly scenario type is Block Tilt, The forth layer block tilted".
        c. When the status_result is "Running", status_explanation show the current task in a fixed format sentence, "example":"Current task is place the forth layer block on the top, No problem detected".
        """
        return sys_prompt
    
    # return format: dict {"succeed": bool,"response": str}
    def generate(self, user_prompt, image):
        try:
            #monitor inference
            completion = self.client.chat.completions.create(
                model=self.monitor_model_name,
                messages=
                [
                    {"role": "system", "content": self.system_prompt},
                    {
                        "role": "user",
                        "content":
                            [
                                {
                                    "type": "text",
                                    "text": user_prompt
                                },
                            ] + [
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/jpeg;base64,{[image]}"
                                    }
                                }
                            ]
                    }
                ]
            )

            response = completion.choices[0].message.content
            monitor_result = {
                "succeed": True,
                "response": response
            } 
        except Exception as e:
            print(f"An exception occurred: {type(e)}")
            print(f"Exception message: {e}")
            monitor_result={
                "succeed": False, 
                "response": str(e)+"VLM client generate response failed. Please retry to inference."
            }
        return monitor_result
        
    def monitor_infer(self, usr_prompt, image_path):
        base64_image = self.encode_image(image_path)

        tic = time.time()
        #block monitor inference
        res  = self.generate(usr_prompt, base64_image)
        toc = time.time()

        #calculate inference time
        vlm_cost = (toc - tic) * 1000
        print(f"vlm cost time: {vlm_cost:.1f}ms")
        return res
