from typing import Any, Dict
import logging
from pydantic import BaseModel
import pprint

logger = logging.getLogger(__name__)


class Registry:
    def __init__(self, name: str):
        """
        Initialize the registry.

        Args:
            name (str): The name of the registry, used for identification and error messages.
        """
        self.name = name
        self._dict: Dict[str, Any] = {}

    @property
    def module_names(self):
        return list(self._dict.keys())

    @property
    def modules(self):
        return self._dict

    def register_module(self, obj: Any = None, alias: str = None):
        """
        Register a class or function into the registry.

        This method supports two usage styles:

        1. As a decorator:
            @registry.register_module()
            class MyClass: ...

        2. As a direct call:
            registry.register_module(MyClass)

        Args:
            obj (Any): The object to register (class or function). Optional.
            alias (str): Optional name to use as the registry key.

        Returns:
            The registered object or a decorator.
        """
        if callable(obj):  # Direct registration: register_module(MyClass)
            return self._do_register(obj, alias)

        # Decorator usage: @register_module()
        def decorator(fn_or_cls: Any):
            return self._do_register(fn_or_cls, alias)

        return decorator

    def _do_register(self, obj: Any, alias: str = None):
        """
        Internal method to handle registration.

        Args:
            obj (Any): The object to register.
            alias (str): Optional alias to use as the registry key.

        Returns:
            The registered object.
        """
        key = alias or getattr(obj, '__name__', str(obj))
        if key in self._dict:
            raise KeyError(f"'{key}' is already registered in {self.name}")
        self._dict[key] = obj
        logger.debug(f"Registered {key} in {self.name}")
        return obj

    def build_from_cfg(self, cfg: Any):
        """
        Build an instance from a configuration mapping.

        Args:
            cfg (Dict[str, Any] or pydantic.BaseModel):
                Configuration mapping. Must contain the key 'type'.

        Returns:
            Instantiated object.
        """
        # 支持 pydantic BaseModel
        if isinstance(cfg, BaseModel):
            cfg = cfg.model_dump()

        if not isinstance(cfg, dict):
            raise TypeError(f"Config must be a dict for {self.name}, got {type(cfg)}")

        if 'type' not in cfg:
            raise KeyError(f"Missing 'type' in config for Registry[{self.name}], got {cfg}")
        
        obj_type = cfg['type']
        params = {k: v for k, v in cfg.items() if k != 'type'}

        # Resolve the type: string lookup or direct class reference
        if isinstance(obj_type, str):
            cls = self._dict.get(obj_type)
            if cls is None:
                available = list(self._dict.keys())
                raise KeyError(
                    f"type='{obj_type}' not found in Registry[{self.name}]. Available: {available}"
                )
        else:
            cls = obj_type

        logger.info(f"building `{obj_type}` from cfg:\n{pprint.pformat(params)}")
        # return cls(**params)
        try:
            return cls(**params)
        except Exception as e:
            # logger.error(f"Failed to build {obj_type}: {e}", exc_info=True)
            raise RuntimeError(f"Failed to build {obj_type}: {e}")


# 全局注册表
TOOLS = Registry("TOOLS")
MODELS = Registry("MODELS")
MSG_CLIENTS = Registry("MSG_CLIENTS")
AGENTS = Registry("AGENTS")
GRAPH_CREATORS = Registry("GRAPH_CREATORS")
