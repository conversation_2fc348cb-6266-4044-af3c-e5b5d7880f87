import heapq
import json
import logging
import os
import re
import time
from datetime import datetime
from functools import wraps
from typing import Callable

import cv2
import numpy as np
import requests
import supervision as sv
from algo_tools.utils.common import encode_image
from openai import OpenAI

from kaiwu_agent.configs.config import (PERCEPTION_SERVER_IP_PORT, VLM_MODELS)

logger = logging.getLogger(__name__)


def get_current_datetime():    
    current_datetime = datetime.now()
    weekday=current_datetime.weekday()
    # 将星期几转换为字符串
    weekday_str = ["周一", "周二", "周三", "周四", "周五", "周六", "周天"]
    current_weekday = weekday_str[weekday]
    # 获取当前时间
    current_time = current_datetime.strftime("%Y-%m-%d %H:%M:%S")
    return current_time, current_weekday


# 存储所有函数的执行时间
execution_times = {}


def timing_wrapper(func):
    """装饰器：统计函数执行时间，并记录到全局字典"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()  # 记录开始时间
        result = func(*args, **kwargs)  # 执行原函数
        elapsed_time = time.time() - start_time  # 计算耗时

        # 记录执行时间
        # if func.__name__ in execution_times:
        #     execution_times[func.__name__].append(elapsed_time)
        # else:
        #     execution_times[func.__name__] = [elapsed_time]
        execution_times[func.__name__] = [elapsed_time]

        return result
    return wrapper


def print_top_slowest_functions(n=10, save_txt=None):
    """打印执行时间最长的前 N 个函数"""
    avg_execution_times = {func: sum(times) / len(times) for func, times in execution_times.items()}
    
    # 获取前 N 个最慢的函数
    top_slowest = heapq.nlargest(n, avg_execution_times.items(), key=lambda x: x[1])

    log_list = []
    log_list.append(f"\n{get_uniq_datetime_str()}: 🔥 **前 {n} 个最耗时的函数:**")
    for i, (func, avg_time) in enumerate(top_slowest, start=1):
        log_list.append(f"{i}. {func} - 平均耗时: {avg_time:.6f} 秒")
    
    logger.info('\n'.join(log_list))
    
    if save_txt is not None:
        try:
            with open(save_txt, '+a') as file:
                file.writelines(log_list)
            logger.info(f"perf data saved to: {save_txt}")
        except Exception as err:
            logger.warning(f"写入失败： {save_txt}: {err}")
            pass


def split_by_first_punctuation(s: str, punctuation_set: set = set("，。；：？！,!?;: ")):
    for i, char in enumerate(s):
        if char in punctuation_set:
            # 前后是否为英文字符，若是，则可能是词的一部分，跳过
            if i > 0 and i + 1 < len(s):
                if s[i - 1].isalnum() and s[i + 1].isalnum():
                    continue  # 避免在英文单词中间切断
            return s[:i + 1], s[i + 1:], True
    return s, "", False


def read_parameters(json_file: str):
    """读取JSON参数文件"""
    file_path = json_file
    try:
        with open(file_path, 'r') as file:
            parameters = json.load(file)
            logger.info(f"读取的参数: {parameters}")
            return parameters
    except FileNotFoundError:
        logger.error(f"错误: 文件 {file_path} 不存在！")
    except json.JSONDecodeError:
        logger.error(f"错误: 无法解析 {file_path}，请检查 JSON 格式！")
    return {}


def correct_instruction(instruction):
    # TODO: 手动把天空、天宫、天公映射到天工
    instruction = re.sub(r'(?:天宫|天公|天空)', '天工', instruction)
    instruction = re.sub(r'(?:大剑|拔剑)', '搭建', instruction)
    instruction = re.sub(r'(?:开始发展)', '开始搭建', instruction)
    instruction = re.sub(r'(?:节目)', '积木', instruction)
    instruction = re.sub(r'(?:积木压力|积木阳历|积木杨丽|积木杨莉|积木样力)', '积木样例', instruction)
    instruction = re.sub(r'(?:明眸会思)', '明眸慧思', instruction)
    instruction = re.sub(r'(?:会思开悟)', '慧思开物', instruction)
    instruction = re.sub(r'(?:榆树科技|语数科技)', '宇树科技', instruction)
    return instruction


def get_public_ip():
    response = requests.get('https://api.ipify.org?format=json')
    if response.status_code == 200:
        return response.json()['ip']
    else:
        return "无法获取公共 IP"


def contains_keywords(input_string, keywords: list):
    # 将关键词列表拼接成正则表达式
    pattern = "|".join(map(re.escape, keywords))
    # 使用正则表达式进行匹配
    return bool(re.search(pattern, input_string))


def get_uniq_datetime_str():
    # 获取当前时间
    current_time = datetime.now()
    # 获取当前时间戳
    timestamp = int(time.time())
    # 格式化时间
    formatted_time = current_time.strftime("%Y_%m_%d_%H_%M_%S")
    # 将时间戳添加到格式化时间中
    return f"{formatted_time}_{timestamp}"

def langraph_tool_to_schema(func: Callable):
    return {
            "type": "function",
            "function": {
                "name": func.name,
                "description": func.description,
                "parameters": {
                    "type": "object",
                    "properties": func.args,
                    "required": list(func.args.keys()),
                    "additionalProperties": False,
                },
            },
    }

# TODO!
# 把它做成一个通用工具，model, key, url, prompt都传参
# 避免每次推理都初始化一个 OpenAI对象
# 参考 http://**********/kaiwu/agent/kaiwu_agent/-/blob/dev/kaiwu_agent/agents/chat_agent.py?ref_type=heads#L67
# 移动到kaiwu_agent/agents/chat_agent.py里，便于复用
def image_caption(instruction, image_path, model_name="gpt-4o"):
    system_prompt = '''你是一个高度先进的机器人视觉分析系统。你擅长从人形机器人的头部摄像机采集图片中识别环境中的物体。\
\n你的任务是按照指定格式，输出场景图片中与用户指令相关的所有目标状态和目标关系。\
\n你处于厨房场景中，重点关注其中可能存在的玉米、烤箱、灰色盘子、盘子架、抽屉、杯子等目标。\
\n###输出格式###\
[{"object": <object-name-n>, "location": <location->}，... ]
\n###Example###\
\n[{"object": "红苹果", "location": "位于桌子中央"},\
{"object": "烤箱架", "location": "在桌子右上角"},
{"object": "白盘子", "location": "在盘子架上"}]。\
\n###注意事项###\
\n1. object_name不能相同，请附加属性以使用细粒度类别进行区分，比如颜色。\
\n###用户指令###\
\nInstruction\
\n###场景图片###\
'''.replace('Instruction', instruction)
    base64_image = encode_image(image_path, new_size=(640, 480))
    client = OpenAI(
        api_key=VLM_MODELS[model_name]["api_key"],
        base_url=VLM_MODELS[model_name]["base_url"],
    )
    start_time = time.time()
    completion = client.chat.completions.create(
        model=model_name,
        messages=[
            {
                "role": "user",
                "content": [{
                    "type": "text",
                    "text": system_prompt
                    },
                    {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{base64_image}"
                        }
                    }
                ]
            }
        ]
    )
    print(f"VLM耗时: {time.time() - start_time}秒")
    return completion.choices[0].message.content


def perception(instruction, image_path, model_name="Qwen2-VL-72B-Instruct", detection_model='Grounding_DINO'):
    start_time = time.time()
    desciption = ""
    if detection_model == 'Grounding_DINO':
        # Perform detection with visualization enabled
        predictions, image_path = detection(image_path, visualization=True)
        # Extract specific components based on label and bbox flags
        if predictions:
            labels = predictions.get('label_names', [])
            desciption = str(list(set(labels)))  # Return unique labels
    elif detection_model == 'DINOX':
        predictions = detect_objects(image_path)
        desciption = ""
        if predictions:
            image_path, desciption = visualization(image_path, predictions)
    else:
        print(f"{detection_model} NotImplementedError.")
    print(f"vis image path: {image_path}")
    print(f"desciption: {desciption}")
    print(f"dectection inference time: {time.time() - start_time}秒")
    system_prompt = '''这里有一张真实环境图片，上面覆盖着边界框和相应的标记ID。\
\n如下是目标类别列表:\
\n{}\
\n你的任务是根据用户指令，识别机器人所处的环境，包括环境中的人物、物品及其位置关系。\
\n要求描述粒度要小，多个同类物品要逐个列出来，用位置、颜色等熟悉区分开来。\
\n###输出格式###\
ID n: <object-name-n>, <description-n>
\n###用户指令###\
\n{}'''.format(desciption, instruction)
    base64_image = encode_image(image_path, new_size=(640, 480))
    client = OpenAI(
        api_key=VLM_MODELS[model_name]["api_key"],
        base_url=VLM_MODELS[model_name]["base_url"],
    )
    start_time = time.time()
    
    completion = client.chat.completions.create(
        model=model_name,
        messages=[
            {
                "role": "user",
                "content": [{
                    "type": "text",
                    "text": system_prompt
                    },
                    {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{base64_image}"
                        }
                    }
                ]
            }
        ]
    )
    print(f"VLM耗时: {time.time() - start_time}秒")
    return completion.choices[0].message.content



def detect_objects(image_path, prompt="<universal_twice>"):
    headers = {
        "Content-Type": "application/json",
        "Token"       : "2b99f9ead4a640749215af64c05d63a0"
    }
    # !TODO: 固定输入尺寸，以稳定推理时间
    base64_image = encode_image(image_path, new_size=(640, 480))
    body = {
        "image"  :  f"data:image/jpeg;base64,{base64_image}",
        "prompts": [
            {"type": "text", "text": prompt},
        ],
    }

    max_retries = 60  # max retry times
    retry_count = 0

    # send request
    resp = requests.post(
        'https://api.deepdataspace.com/tasks/dinox',
        json=body,
        headers=headers
    )

    if resp.status_code == 200:
        json_resp = resp.json()
        print(json_resp)
        # {'code': 0, 'data': {'task_uuid': '092ccde4-a51a-489b-b384-9c4ba8af7375'}, 'msg': 'ok'}

        # get task_uuid
        task_uuid = json_resp["data"]["task_uuid"]
        print(f'task_uuid:{task_uuid}')

        # poll get task result
        while retry_count < max_retries:
            resp = requests.get(f'https://api.deepdataspace.com/task_statuses/{task_uuid}', headers=headers)
            if resp.status_code != 200:
                break
            json_resp = resp.json()
            if json_resp["data"]["status"] not in ["waiting", "running"]:
                break
            time.sleep(1)
            retry_count += 1

        if json_resp["data"]["status"] == "failed":
            print(f'failed resp: {json_resp}')
            return []
        elif json_resp["data"]["status"] == "success":
            predictions = []
            for object in json_resp.get('data').get('result').get('objects'):
                object.pop('mask')
                object.pop('hand')
                object.pop('pose')
                predictions.append(object)
            print(f'predictions: {predictions}')
            return predictions
        else:
            print(f'get task resp: {resp.status_code} - {resp.text}')
            return []
    else:
        print(f'detect_objects: Error: {resp.status_code} - {resp.text}')
        return []


def visualization(image_path, predictions):
    if not predictions:
        return image_path, ""
    # decode the prediction results
    classes = []
    for i, obj in enumerate(predictions):
        if obj.get('category') not in classes:
            classes.append(obj.get('category'))
    class_name_to_id = {name: id for id, name in enumerate(classes)}
    class_id_to_name = {id: name for name, id in class_name_to_id.items()}

    boxes = []
    class_names = []
    class_ids = []

    for idx, obj in enumerate(predictions):
        boxes.append(obj.get('bbox'))
        cls_name = obj.get('category').lower().strip()
        class_names.append(cls_name)
        class_ids.append(class_name_to_id[cls_name])

    boxes = np.array(boxes)
    class_ids = np.array(class_ids)
    labels = [
        f"{idx}" for idx in range(1, len(class_names) + 1)
    ]

    img = cv2.imread(image_path)
    detections = sv.Detections(
        xyxy = boxes,
        class_id = class_ids
    )

    box_annotator = sv.BoxAnnotator()
    annotated_frame = box_annotator.annotate(scene=img.copy(), detections=detections)

    label_annotator = sv.LabelAnnotator(text_scale=0.5)
    annotated_frame = label_annotator.annotate(scene=annotated_frame, detections=detections, labels=labels)
    name, ext = os.path.splitext(image_path)
    image_path = f"{name}_vis{ext}"
    cv2.imwrite(image_path, annotated_frame)
    result = ", ".join([f"ID {i+1}: {value}" for i, value in enumerate(class_names)])
    return image_path, result


def download_image(url, save_path):
    """
    Downloads an image from a given URL and saves it to the specified path.
    """
    if not url.startswith(('http://', 'https://')):
        url = f"http://{url}"
    response = requests.get(url, stream=True)
    if response.status_code == 200:
        with open(save_path, 'wb') as file:
            for chunk in response.iter_content(1024):
                file.write(chunk)
        return save_path
    raise Exception(f"Failed to download image. Status code: {response.status_code}")


def download_json(url):
    """
    Downloads and parses a JSON file from a given URL.
    Ensures the URL has the correct schema.
    """
    if not url.startswith(('http://', 'https://')):
        url = f"http://{url}"
    response = requests.get(url)
    if response.status_code == 200:
        return response.json()
    raise Exception(f"Failed to download JSON. Status code: {response.status_code}")


def detection(image_path, prompt="$: lvis", visualization=False):
    """
    Detects objects in an image and optionally provides segmentation and visualization.
    If visualization is enabled, the resulting image will be downloaded and saved.
    
    Args:
        image_path (str): Path to the input image.
        prompt (str): Prompt for object detection.
        visualization (bool): Whether to enable visualization.

    Returns:
        tuple: Predictions and path to the visualization image (if generated).
    """
    basename = os.path.basename(image_path)
    files = {
        "picture": (basename, open(image_path, "rb"), "image/jpeg")
    }

    # Send POST request to the new endpoint
    url = f"http://{PERCEPTION_SERVER_IP_PORT}/mm_predict?prompt={prompt}"
    response = requests.post(url, files=files)

    if response.status_code == 200:
        try:
            response_data = response.json()
            vis_image_url = response_data.get('out_img')
            json_output_url = response_data.get('out_json')

            # Download visualization image if available
            vis_save_path = image_path
            if visualization and vis_image_url:
                vis_save_path = f"{os.path.splitext(image_path)[0]}_vis{os.path.splitext(image_path)[1]}"
                download_image(vis_image_url, vis_save_path)

            # Download and parse JSON output if available
            json_data = None
            if json_output_url:
                json_data = download_json(json_output_url)

            return json_data, vis_save_path

        except requests.exceptions.JSONDecodeError:
            print("Error: Failed to decode JSON response from the server.")
    else:
        print(f"Error: Server responded with status code {response.status_code}.")
    return None, None


def clean_json_output(output: str, replace_nulls: bool = True) -> tuple[str, bool]:
    """
    Cleans a string containing JSON output, removing surrounding backticks and handling null-like values.

    Args:
        output: The string containing the JSON output, potentially with surrounding backticks.
        replace_nulls: Whether to replace "unknown", "na", and "null" string values with empty strings. Defaults to True.

    Returns:
        A tuple containing:
            - The cleaned JSON string.
            - A boolean indicating whether the JSON was successfully parsed and cleaned.
              Returns the original cleaned string and False if parsing fails.
    """
    output = output.strip()
    if output.startswith("```json"):
        output = output[7:]
    elif output.startswith("```"):
        output = output[4:]
    if output.endswith("```"):
        output = output[:-3]
    cleaned_output = output.strip()

    try:
        json_data = json.loads(cleaned_output)
    except json.JSONDecodeError as e:
        print(f"JSON decoding error: {e}")
        return cleaned_output, False

    def clean_json(data):
        if isinstance(data, dict):
            return {key: clean_json(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [clean_json(item) for item in data]
        elif isinstance(data, str):
            return "" if replace_nulls and data.lower() in ["unknown", "na", "null"] else data # 使用参数控制是否替换
        else:
            return data

    cleaned_json_data = clean_json(json_data)
    cleaned_output = json.dumps(cleaned_json_data, ensure_ascii=False, indent=2) # 增加indent，使输出更易读

    return cleaned_output, True

def get_video_frame(video_path, frame_index):
    # 打开视频文件
    cap = cv2.VideoCapture(video_path)
    
    # 检查视频是否成功打开
    if not cap.isOpened():
        print("Error: 无法打开视频文件")
        return None

    # 获取视频的总帧数
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    if frame_index >= total_frames:
        frame_index = total_frames - 1
    elif frame_index < -total_frames:
        frame_index = 0
    elif frame_index < 0:
        frame_index = total_frames + frame_index

    # 将读取位置移动到最后一帧
    cap.set(cv2.CAP_PROP_POS_FRAMES, frame_index)
    
    # 读取最后一帧
    ret, frame = cap.read()
    cap.release()
    
    if ret:
        # 生成保存路径，与原视频同路径，并加上 _last_frame 后缀
        base_name = os.path.splitext(video_path)[0]  # 去掉扩展名
        last_frame_path = f"{base_name}_last_frame.jpg"
        
        # 保存图片
        cv2.imwrite(last_frame_path, frame)
        print(f"最后一帧已保存为: {last_frame_path}")
        return last_frame_path
    else:
        print("Error: 无法读取最后一帧")
        return None


def extract_plan_and_remaining(text: str):
    """从文本中提取 'plan' 部分，并返回剩余的文本。
    
    参数:
        text (str): 输入的文本字符串。
        
    返回:
        tuple: 包含两部分的数据，第一个是提取的 plan 字典，第二个是去除掉 plan 后剩余的文本。
    """
    # 正则表达式匹配 plan 信息，包含更多可能的格式
    pattern = r'\{\s*"plan"\s*:\s*\[\s*(?:\{.*?\}\s*,*\s*)*\]\s*,\s*"task_status"\s*:\s*"(Pending|Running|Finish)"\s*\}'
    match = re.search(pattern, text, re.DOTALL)

    plan_data = None
    task_status = "Running"
    remaining_text = text

    if match:
        plan_json_str = match.group(0)
        # 去除plan和plan后的文本
        remaining_text = text[:match.start()].strip()
        # remaining_text = text.replace(plan_json_str, "").strip()  # 去除 plan JSON
        try:
            data = json.loads(plan_json_str)  # 解析 JSON 字符串为 Python 字典
            plan_data = data.get("plan")
            task_status = data.get("task_status")
        except json.JSONDecodeError:
            logger.info("JSON decoding failed.")
            # json解析失败, 但仍需要删除匹配到的plan, 不能影响非plan内容
            remaining_text = text.replace(plan_json_str, "").strip()
    
    # format plan_data
    plan_data = format_plan_data(plan_data)
    
    return plan_data, task_status, remaining_text


def format_plan_data(plan_data: list):
    if plan_data:
        # 给每一个新增一个sub_steps字段, 空字符串
        plan_data = [{"name": i.get("step_name"), "status": i.get("status"), "sub_steps": ""} for i in plan_data]
    return plan_data
