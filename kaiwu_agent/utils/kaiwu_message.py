import json
import requests
import logging
logger = logging.getLogger(__name__)


class KaiwuMessageClient:
    """发送消息"""
    def __init__(self, url: str = "http://10.0.3.111:8080/kaiwu-admin/v1/api/chat/doSimpleResult"):
        self.url = url
    
    def send(self, msg_dict: dict):
        assert isinstance(msg_dict, dict), msg_dict
        headers = {
            # 'Authorization': f'Bearer {self.token}',
            'Content-Type': 'application/json',
        }
        try:    
            response = requests.post(self.url, headers=headers, data=json.dumps(msg_dict), timeout=(3, 10))
            logger.info(f"给开物云发消息成功，msg_dict: {msg_dict}，response: {response.text}")
        except Exception as err:
            logger.warning(f"给开物云发消息失败，忽略，msg_dict: {msg_dict}, error: {err}")
        