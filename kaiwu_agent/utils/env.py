import os
import logging
logger = logging.getLogger(__name__)


class EnvManager():
    def is_set(self, env_suffix, val="1"):
        return os.environ.get(f"KAIWU_AGENT_{env_suffix}", None) == val

    def set_env(self, env_suffix, val="1"):
        name = f"KAIWU_AGENT_{env_suffix}"
        os.environ[name] = val
        logger.info(f"set env: {name}={val}")
    
    def unset_env(self, env_suffix):
        name = f"KAIWU_AGENT_{env_suffix}"
        if name in os.environ:
            os.environ.pop(name)    
        logger.info(f"unset env: {name}")

env_manager = EnvManager()


def get_return_if_stop():
    if env_manager.is_set("STOP"):
        {"tool_succeed": True, "answer": "receive system level stop command, stop running and return directly"}
    else:
        None
                