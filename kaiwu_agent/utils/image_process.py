import cv2
import base64


def resize_image(img, resize_factor):
    assert resize_factor > 0
    new_width = int(img.shape[1] * resize_factor)
    new_height = int(img.shape[0] * resize_factor)
    new_size = (new_width, new_height)
    print(f"new_size: {new_size}")
    new_img = cv2.resize(img, new_size)
    return new_img


def encode_base64(img, ext='.jpg'):
    # 将图像编码为字节流（比如 .jpg 格式）
    _, encoded_image = cv2.imencode(ext, img)

    # 将编码后的图像字节数据转换为 Base64 编码
    image_bytes = encoded_image.tobytes()
    image_base64 = base64.b64encode(image_bytes).decode('utf-8')
    return image_base64


def to_byte(img, ext='.jpg'):
    # 将图像编码为字节流
    success, encoded_image = cv2.imencode(ext, img)
    if not success:
        print("图像编码失败")
        return None
    else:
        # 将字节数据转换为二进制流
        return encoded_image.tobytes()
