import inspect

def function_to_schema(func) -> dict:
    type_map = {
        str: "string",
        int: "integer",
        float: "number",
        bool: "boolean",
        list: "array",
        dict: "object",
        type(None): "null",
    }

    try:
        signature = inspect.signature(func)
    except ValueError as e:
        raise ValueError(
            f"Failed to get signature for function {func.__name__}: {str(e)}"
        )

    parameters = {}
    for param in signature.parameters.values():
        try:
            param_type = type_map.get(param.annotation, "string")

        except KeyError as e:
            raise KeyError(
                f"Unknown type annotation {param.annotation} for parameter {param.name}: {str(e)}"
            )

        parameters[param.name] = {"type": param_type}

    required = [
        param.name
        for param in signature.parameters.values()
        if param.default == inspect._empty
    ]

    return {
        "type": "function",
        "function": {
            "name": func.__name__,
            "description": (func.__doc__ or "").strip(),
            "parameters": {
                "type": "object",
                "properties": parameters,
                "required": required,
            },
        },
    }

def pretty_print_conversation(messages):
    role_to_color = {
        "system": "red",
        "user": "green",
        "assistant": "blue",
        "function": "magenta",
    }
    
    for message in messages:
        if message["role"] == "system":
            print((f"system: {message['content']}\n", role_to_color[message['role']]))
        elif message["role"] == "user":
            print((f"user: {message['content']}\n", role_to_color[message['role']]))
        elif message["role"] == "assistant" and message.get("function_call"):
            print((f"assistant: {message['function_call']}\n", role_to_color[message['role']]))
        elif message["role"] == "assistant" and not message.get("function_call"):
            print((f"assistant: {message['content']}\n", role_to_color[message['role']]))
        elif message["role"] == "function":
            print((f"function ({message['name']}): {message['content']}\n", role_to_color[message["role"]]))
