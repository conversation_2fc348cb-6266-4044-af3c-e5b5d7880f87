import logging
import os
from datetime import datetime

# 获取当前日期时间作为文件名
current_datetime = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

# 构建日志文件的完整路径
log_dir = os.path.join("assets", "log")
os.makedirs(log_dir, exist_ok=True)  # 确保目录存在
log_file_path = os.path.join(log_dir, f"{current_datetime}.log")

# logging.basicConfig(level=logging.INFO)
# 配置日志记录器
logging.basicConfig(
    level=logging.INFO,  # 设置日志级别
    format="%(asctime)s-%(name)s-%(levelname)s-%(message)s",  # 日志格式
    handlers=[
        logging.FileHandler(log_file_path),  # 文件输出
        logging.StreamHandler()  # 控制台输出
    ]
)

logger = logging.getLogger(__name__)