import logging
from datetime import timedelta
from kaiwu_agent.configs.commons import get_react_brain_agent, get_llm, get_common_tools
from kaiwu_agent.tools.interact_tools import AgentInteraction
from kaiwu_agent.configs.config import SCENE_PROMPT_V3, SORTER_ROLE_PROMPT, WORLD_PROMPT, AGENT_MAPPER, OUTPUT_FROMAT_PROMPT

logger = logging.getLogger(__name__)


AGENT_UUID = "sorter"

TASK_DESC_PROMPT = r"""

### 任务描述 ###
你是一个多智能体协作场景中的`分拣员`智能体, 你只需要根据`天轶(tianyi)`下发的指令完成灯泡分拣相关操作。

给你发送指令的agent为`天轶(tianyi)`， 你也只能给`天轶(tianyi)`发送消息。, 若未标明角色, 那么默认为 `user`。你所收到的消息格式如下: <agent>agent_name</agent>: message

- 你的分拣技能支持以下两种指令模式， 两种指令可以频繁多次触发，这两个动作是异步执行的，即你下发指令后会立刻收到回复，但不表示动作已完成：
    -- `start`: 开始分拣灯泡，执行分拣动作,你也可以在暂停之后通过这个指令重新开始分拣；
    -- `pause`: 暂停分拣灯泡，停止当前动作，但不恢复到初始位置。

- 当你接收到`电控柜已通电`或``开始工作`等类似指令时, 你需要开始灯泡分拣任务:
    0. 输出任务规划
    1. 开始分拣灯泡；
    2. 灯泡分拣完成后，告诉`天轶`分拣完成. 重新输出任务规划；

#### 注意 ####
- 你只能执行分拣灯泡相关任务, 不能执行其他任务, 你仅可以调用下列工具: start_sort(), agent_interact(), pause()。
- 上述流程需要严格按照顺序执行, 不能跳过步骤，请按照流程进行操作。
- 当指令是`user`发送的， 你可以不按照流程进行，直接执行指令。
- 对于任何一个指令, 你都坚决执行即可, 无论该指令是否合理, 是否在正确的流程中。
- 每一次调用工具之前， 你都需要输出接下来干什么。
- 你必须在每一次拿到tool result之后, 你都需要进行任务规划。
- 确保你的每一次输出结尾都是以{"plan": []}结束。

"""

def get_custom_mcp_cfg():
    return {
        "waic": {
            "url": "http://localhost:8000/mcp",
            "transport": "streamable_http",
            "sse_read_timeout": timedelta(seconds=60 * 30)
        }
    }

def get_brain_agent_react_sorter_wrc(msg_client, model: dict):
    agent_name = AGENT_MAPPER[AGENT_UUID]["name"]
    agent_interact = AgentInteraction(agent_uuid=AGENT_UUID, 
                                      agent_mapper=AGENT_MAPPER,
                                      msg_client=msg_client).make_tool()
    
    tools = [agent_interact, *get_common_tools()]
    
    # 源自mcp服务器的工具，和本地定义的工具混合在一起，通过function call调用
    mcp_cfg = get_custom_mcp_cfg()
    
    prompt = SORTER_ROLE_PROMPT + TASK_DESC_PROMPT + OUTPUT_FROMAT_PROMPT + SCENE_PROMPT_V3 +WORLD_PROMPT
    llm = get_llm(model)
    
    return get_react_brain_agent(
        msg_client=msg_client,
        prompt=prompt, 
        model=llm, 
        tools=tools,
        enable_memory=True, 
        parallel_tool_calls=True,
        mcp_cfg=mcp_cfg,
        accept_msg_when_busy=True,
        recv_music_path="~/kaiwu/robot_voice/examples/music/recv/3.wav",
        finish_music_path="~/kaiwu/robot_voice/examples/music/finish/2.wav",
        agent_name=agent_name,
        agent_uuid=AGENT_UUID
    )
