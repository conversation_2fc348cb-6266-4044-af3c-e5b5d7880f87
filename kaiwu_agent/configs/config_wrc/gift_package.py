import logging
from datetime import timedelta
from kaiwu_agent.configs.commons import get_react_brain_agent, get_llm
from kaiwu_agent.tools.interact_tools import AgentInteraction
from kaiwu_agent.configs.config import SCENE_PROMPT_V3, FRANKA_ROLE_PROMPT, WORLD_PROMPT, AGENT_MAPPER, OUTPUT_FROMAT_PROMPT

logger = logging.getLogger(__name__)


AGENT_UUID = "gift_package"

TASK_DESC_PROMPT = r"""

### 任务描述 ###
你是一个多智能体协作场景中的`礼物打包`智能体, 你只需要根据`天轶(tianyi)`下发的指令完成礼物包装相关操作。

给你发送指令的agent为`天轶(tianyi)`， 你也只能给`天轶(tianyi)`发送消息。, 若未标明角色, 那么默认为 `user`。你所收到的消息格式如下: <agent>agent_name</agent>: message

- 当你接收到`电控柜已通电`或`开始工作`等类似指令时, 你需要开始打包工作。
    0. 输出任务规划；
    1. 开始礼品打包工作，并输出工具执行结果, 重新输出任务规划；
    2. 打包工作完成后，告诉`天轶`打包完成，并输出任务规划。

#### 注意 ####
- 你只能执行礼品装箱相关的任务, 不能执行其他任务, 你仅可以调用下列工具: agent_interact(), start()。
- 当没有明确指定需要装多少件礼品时, 无需询问用户， 直接装2件礼品。
- 上述流程需要严格按照顺序执行, 不能跳过步骤，请按照流程进行操作。
- 当指令是`user`发送的， 你可以不按照流程进行，直接执行指令。
- 对于任何一个指令, 你都坚决执行即可, 无论该指令是否合理, 是否在正确的流程中。
- 你必须在每一次拿到tool result之后, 你都需要进行任务规划。
- 确保你的每一次输出结尾都是以{"plan": []}结束。

"""

def get_custom_mcp_cfg():
    return {
        "waic": {
            "url": "http://localhost:8000/mcp",
            "transport": "streamable_http",
            "sse_read_timeout": timedelta(seconds=60 * 30)
        }
    }

def get_brain_agent_react_gift_package_wrc(msg_client, model: dict):
    agent_name = AGENT_MAPPER[AGENT_UUID]["name"]
    agent_interact = AgentInteraction(agent_uuid=AGENT_UUID, 
                                      agent_mapper=AGENT_MAPPER,
                                      msg_client=msg_client).make_tool()
    
    # 源自mcp服务器的工具，和本地定义的工具混合在一起，通过function call调用
    mcp_cfg = get_custom_mcp_cfg()
    
    prompt = FRANKA_ROLE_PROMPT + TASK_DESC_PROMPT + OUTPUT_FROMAT_PROMPT + SCENE_PROMPT_V3 +WORLD_PROMPT
    
    llm = get_llm(model)
    
    return get_react_brain_agent(
        msg_client=msg_client,
        prompt=prompt, 
        model=llm, 
        tools=[agent_interact],
        enable_memory=True, 
        parallel_tool_calls=True,
        mcp_cfg=mcp_cfg,
        accept_msg_when_busy=True,
        recv_music_path="~/kaiwu/robot_voice/examples/music/recv/3.wav",
        finish_music_path="~/kaiwu/robot_voice/examples/music/finish/2.wav",
        agent_name=agent_name,
        agent_uuid=AGENT_UUID
    )
