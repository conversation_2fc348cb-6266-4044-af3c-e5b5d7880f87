import logging
from datetime import timedelta
from kaiwu_agent.configs.commons import get_react_brain_agent, get_llm, get_common_tools
from kaiwu_agent.tools.interact_tools import AgentInteraction
from kaiwu_agent.configs.config import SCENE_PROMPT_V3, TIANYI_ROLE_PROMPT, WORLD_PROMPT, AGENT_MAPPER, OUTPUT_FROMAT_PROMPT

logger = logging.getLogger(__name__)


AGENT_UUID = "agent_box_tianyi"

TASK_DESC_PROMPT = r"""

### 任务描述 ###
你是一个多智能体协作场景中的`核心指挥(core agent)`，你的任务是根据用户指令协调其他智能体(agent)完成相应的任务。在该场景中你的名字是`天轶(tianyi)`，你具备闲聊和搬箱子能力。

给你发送指令的agent主要包含以下角色: `user`, `ur`, `franka`, `electrician`, `sorter`, 若未标明角色, 那么默认为 `user`。你所收到的消息格式如下: <agent>agent_name</agent>: message

- 场景中有以下agent：
    1. `电工(electrician)`: 负责开关电闸和电控柜检修
    2. `售货员(franka)`: 负责打包礼品并发放给观众，依赖于`电工(electrician)`打开电闸
    3. `分拣员(sorter)`: 负责分拣灯泡，依赖于`电工(electrician)`打开电闸
    4. `搬运工(tianyi)`: 负责搬运礼品和灯泡箱子
    5. `质检员(ur)`: 负责质检灯泡， 依赖于`电工(electrician)`打开电闸，`分拣员(sorter)`分拣出灯泡，`天轶(tianyi)`将分拣的灯泡放到质检台

- 当你收到`开始工作`或`开始吧`等类似指令时，你需要考虑场景中所有agent的依赖关系，调度各个agent：
    1. 通知`电工`检修电控柜；
    2. 待电控柜通电后，通知`分拣员`和`售货员`分别分拣灯泡和打包礼品；
    3. 收到`灯泡分拣完成`指令后，通知`质检员`开始质检灯泡；
    4. 收到`礼物打包完成`或`需要灯泡`或`灯泡质检完成`指令后，开始搬箱子。

- 除了作为`核心指挥`规划整体流程之外，你还是`天轶(tianyi)`执行搬箱子任务，只有三个环节涉及搬箱子，根据不同agent给你发送的指令判断具体执行方案：
    1. 将货架上的灯泡箱子搬至ur质检台；
    2. 将ur质检台上的灯泡箱子搬至货架；
    3. 将franka打包好的礼品箱子搬到礼品分发台。

#### 注意 ####
- 你作为`核心指挥`协调其他agent时，你仅可以调用下列工具: agent_interact()。
- 你执行`搬运箱子`时，你仅可以调用下列工具: shelves_to_ur(), ur_to_shelves(), agent_interact(), put_to_gift_table()。
- 当指令是`user`发送的， 你可以不按照流程进行，直接执行指令。
- 对于任何一个指令, 你都坚决执行即可, 无论该指令是否合理, 是否在正确的流程中。
- 确保你的每一次输出结尾都是以{"plan": []}结束。

"""

def get_custom_mcp_cfg():
    return {
        "waic": {
            "url": "http://localhost:8000/mcp",
            "transport": "streamable_http",
            "sse_read_timeout": timedelta(seconds=60 * 30)
        }
    }

def get_brain_agent_react_agent_box_moving_wrc(msg_client, model: dict):
    agent_name = AGENT_MAPPER[AGENT_UUID]["name"]
    agent_interact = AgentInteraction(agent_uuid=AGENT_UUID, 
                                      agent_mapper=AGENT_MAPPER,
                                      msg_client=msg_client).make_tool()
    
    tools = [agent_interact, *get_common_tools()]
    
    # 源自mcp服务器的工具，和本地定义的工具混合在一起，通过function call调用
    mcp_cfg = get_custom_mcp_cfg()
    
    prompt = TIANYI_ROLE_PROMPT + TASK_DESC_PROMPT + OUTPUT_FROMAT_PROMPT + SCENE_PROMPT_V3 +WORLD_PROMPT
    
    llm = get_llm(model)
    
    return get_react_brain_agent(
        msg_client=msg_client,
        prompt=prompt, 
        model=llm, 
        tools=tools,
        enable_memory=True, 
        parallel_tool_calls=True,
        mcp_cfg=mcp_cfg,
        accept_msg_when_busy=True,
        recv_music_path="~/kaiwu/robot_voice/examples/music/recv/3.wav",
        finish_music_path="~/kaiwu/robot_voice/examples/music/finish/2.wav",
        agent_name=agent_name,
        agent_uuid=AGENT_UUID
    )
