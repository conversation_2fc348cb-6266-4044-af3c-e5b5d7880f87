from kaiwu_agent.agents import TGControlAgentV1, TGControlAgentV2
from kaiwu_agent.configs.config import CONTROL_AGENT_IP, CONTROL_AGENT_PORT
from kaiwu_agent.configs.base import CONTROL_VERSIONS


def get_control_agent(msg_client, log_recv=True, version=CONTROL_VERSIONS.V1_WEBSOCKETS):
    if version == CONTROL_VERSIONS.V1_WEBSOCKETS:
        return TGControlAgentV1(
            url=f'ws://{CONTROL_AGENT_IP}:{CONTROL_AGENT_PORT}/chatsocket',
            msg_client=msg_client,
            wait_result=True,
            log_recv=log_recv,
            connect_timeout=20.0,
            recv_timeout=20.0
        )
    elif version == CONTROL_VERSIONS.V2_FASTAPI:
        return TGControlAgentV2(
            port=8080,
            server_url = f"http://{CONTROL_AGENT_IP}:{CONTROL_AGENT_PORT}/control_agent_task_command",
            msg_client=msg_client,
            wait_result=True,
            log_recv=log_recv,
            connect_timeout=240.0,
            recv_timeout=240.0
        )
    else:
        raise NotImplementedError(f"unsupported version: {version}")
