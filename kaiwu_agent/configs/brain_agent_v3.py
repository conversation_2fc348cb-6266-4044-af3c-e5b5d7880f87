from kaiwu_agent.agents import DifyChatAgent, TGBrainAgentV3, Act, Plan
from kaiwu_agent.tools.langgraph_tools import scene_desc_wrapper, ask_user_wrapper, \
    manipulation_wrapper, voice_play_wrapper, vlm_scene_desc_wrapper, \
    robotic_arm_operation_wrapper, release_hand_wrapper, look_up_wrapper
from langchain_openai import ChatOpenAI
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langgraph.prebuilt import create_react_agent
from kaiwu_agent.configs.base import CONTROL_VERSIONS
from kaiwu_agent.configs.control_agent import get_control_agent
import logging
logger = logging.getLogger()


def get_tg_brain_agent_v3(msg_client, 
                          display_client,
                          plan_model, 
                          execute_model, 
                          control_agent_version=CONTROL_VERSIONS.V1_WEBSOCKETS,
                          simulation=False):
    # 不传msg_client，中间结果暂不播报
    control_agent = get_control_agent(msg_client=None, log_recv=False, version=control_agent_version)
    
    chat_agent = DifyChatAgent(
        # agent
        # token="app-tvRzN7ni8kcRLUKg51c8XA3M", 
        # rag
        token="app-YvNWFJzQyX9x6LyBqDtui6NW",
        url='http://*************/v1/chat-messages'
    )
    
    # TODO (aurum.tian): 图片应动态传入
    scene_desc = scene_desc_wrapper(control_agent) if control_agent_version == CONTROL_VERSIONS.V1_WEBSOCKETS \
        else vlm_scene_desc_wrapper(simulation=simulation)
    manipulation = manipulation_wrapper(control_agent) if control_agent_version == CONTROL_VERSIONS.V1_WEBSOCKETS \
        else robotic_arm_operation_wrapper(control_agent)
    look_up = look_up_wrapper(chat_agent)
    # input_from_terminal: 从命令行输入指令，还是从麦克风获取指令
    ask_user = ask_user_wrapper(msg_client, input_from_terminal=False)
    voice_play = voice_play_wrapper(msg_client)
    release_hand = release_hand_wrapper(control_agent)
    
    tools = [scene_desc, ask_user, manipulation, look_up, voice_play, release_hand]

    # executor_prompt = hub.pull("zippocampus/robot-simple-agent-zh")
    # logger.info(f"\n{executor_prompt.pretty_repr()}")
    
    #####################
    # 注意：prompt加上【角色定义】、【任务描述】、【任务要求】 三块，指令跟随的效果更佳！！
    #####################
    role_definition = """
    角色定义：
    你是一个人形机器人，你的名字叫天工（听到“你好天空”、“你好天宫”、“天空天空”、“天宫天宫”等同音词，也是在叫你）。\
    你有头和四肢，你可以完成物品操作任务，但你目前还无法移动，你现在固定站在一张桌子面前。\
    你还配备了麦克风和喇叭，你可以通过语音交互的方式和用户聊天。你可以调用一系列工具去完成用户指令。
    """
    
    # 7. 在你执行物品抓取和放置任务前，必须确保抓取目标和放置位置是同时明确的，如不明确，必须先询问用户或者观察上下文得出，然后再执行抓取任务。
    executor_prompt = ChatPromptTemplate.from_messages(
        [
            (
                "system",
                role_definition + """
                
                任务描述：
                你要根据人类用户的意图，结合可用工具的描述，选出并调用合适的工具，并严格按照工具的反馈结果而不是你的主观臆测输出反馈。
                
                任务要求：
                1. 你擅长执行清晰的命令。指执行一些具有明确对象和明确要求的指令。 
                2. 你需要对用户友善，尤其是在回答用户提问和对用户进行提问时。 
                3. 请尽量使用你所拥有的tool，并合理的使用其产生的tool message。
                4. 请不要拒绝执行拿取、描述图像等任务。
                5. 如果用户告诉你退出当前程序，例如“退出吧”、“退下吧”、“结束吧”，并在最终输出的信息里说明用户要求退出，以便于后续的程序能一起退出。
                6. 你要频繁地调用语音播报工具，把你的思考过程、上一步执行的结果、下一步要做的事情告知用户。
                """
            ),
            ("placeholder", "{messages}"),
        ]
    )
    logger.info("-------------- executor_prompt ------------------")
    logger.info(f"\n{executor_prompt.pretty_repr()}")
    
    # ReAct agent
    llm = ChatOpenAI(
        model=execute_model["model"],
        api_key=execute_model["api_key"],
        base_url=execute_model["base_url"],
        )
    # TODO: 目前禁止并行调用多个tool（manipulation不支持并行），后续要支持同时聊天和操作再去掉
    llm = llm.bind_tools(tools, parallel_tool_calls=False)
    agent_executor = create_react_agent(llm, tools, state_modifier=executor_prompt)
    
    base_prompt = role_definition + """
    
    任务描述：
    For the given objective, come up with a simple step by step plan. \
    This plan should involve individual tasks, that if executed correctly will yield the correct answer. Do not add any superfluous steps. \
    The last step must clarify that the work has been down, and the system needs to be terminated. \
    The result of the final step should be the final answer. Make sure that each step has all the information needed. 
        
    任务要求：
    1. 你需要根据用户的指令、机器人目前所处的环境，以及机器人可以执行的技能来做计划，一定不要只靠想象去做任务规划。
    2. 如果信息不足，你不用一次性给出完整的计划，可以边执行边规划，或者先调用工具去从环境或从用户那里获取足够的信息，再做更长远的规划。
    3. 你规划的最后一步必须是把结果告知用户。
    4. 如果用户要求退出当前任务，例如"退出吧"、"退下吧"、"结束吧"，那剩余的步骤不用再执行。
    5. 如果用户指令不清晰，可以调用询问用户的工具去获得更多信息。
    6. 你必须要把你接下来要做的事情先告知用户，再去执行，执行结束后立刻播报结果。你要先总结再播报，播报的文字一定要简洁，控制在40个字以内。
    7. 你不用去确认和询问用户是否还有其它指令、其他需求或者可以帮助的地方，用户有需要会自行询问。
    8. 请输出中文或者英文，要和用户使用的语种保持一致，以便于再同一语种下交互，默认输出中文。
    9. 坚决执行用户明确的指令，不要做多余的规划，例如用户要求把手松开、把你手上的苹果放到盘子里，不要多此一举去确认手上有没有东西。
    10. 不要规划出'放置位置'不明确的操作步骤，例如'把柠檬放到用户指定的位置'、'把苹果递给用户'，应该先询问用户要把物品放到哪。
    11. 物品抓取和放置任务必须在一个步骤里完成，例如'把芒果放到绿色的碗里'，不要规划出'抓取芒果'和'将芒果放到绿色的碗里'两步。
    """
    # 10. 不支持同时抓取多个物品，例如'把苹果和芒果放到篮子里'应该拆解成两个指令'把苹果放到篮子里'、'把芒果放到篮子里'。\
    #     当要抓取的物品或放置位置不明确时，应该先询问用户得出。\
    #     当要抓取的物品和放置位置明确时，用一个'抓取和放置'指令一步完成，例如'把芒果抓起来然后放到篮子里'，而不是拆成'抓取芒果'和'将芒果放到篮子里'两个步骤。
    
    planner_prompt = ChatPromptTemplate.from_messages(
        [
            (
                "system",
                base_prompt
            ),
            ("placeholder", "{messages}"),
        ]
    )
    logger.info("-------------- planner_prompt ------------------")
    logger.info(f"\n{planner_prompt.pretty_repr()}")

    planner = planner_prompt | ChatOpenAI(
        model=plan_model["model"],
        api_key=plan_model["api_key"],
        base_url=plan_model["base_url"],
        temperature=0
    ).with_structured_output(Plan)
    
    replanner_prompt = ChatPromptTemplate.from_template(
        base_prompt + """
        
        Your objective was this:
        {input}

        Your original plan was this:
        {plan}

        You have currently done the follow steps:
        {past_steps}

        Update your plan accordingly. If no more steps are needed and you can return to the user, then respond with that. Otherwise, \
        fill out the plan. Only add steps to the plan that still NEED to be done. Do not return previously done steps as part of the plan.
        """
    )
    logger.info("-------------- replanner_prompt ------------------")
    logger.info(f"\n{replanner_prompt.pretty_repr()}")

    replanner = replanner_prompt | ChatOpenAI(
        model=plan_model["model"],
        api_key=plan_model["api_key"],
        base_url=plan_model["base_url"],
        temperature=0
    ).with_structured_output(Act)

    # 普通对话步骤
    normal_dialogue_prompt = ChatPromptTemplate.from_messages(
        [
            (
                "system",
                """你是一个拥有两个机械臂和一个摄像头的机器人的大脑，由北京具身智能创新中心开发。\
                你所控制的机器人可以执行的技能有：\
                1.检查并描述机器人所处的环境，对图像、视觉信息进行描述。\
                2.可以针对用户的模糊命令进行反问并进一步澄清。\
                3.可以完成操作型指令，例如把苹果放到我手上\
                4.可以进行开放性聊天，解答包括需要知识库检索、联网查询的问题，例如今天天气怎么样。\
                5.在任务结束，获得final answer后，可以语音播报。\
                请尽量提供详尽的答案，并与用户进行自然的对话。""",
            ),
            ("placeholder", "{messages}"),
        ]
    )

    normal_dialogue = normal_dialogue_prompt | ChatOpenAI(
        model="gpt-4o",
        api_key="sk-Wyy6gs0pUYfhmP7RLo6XvupsmujD4odpK3yU1tQbwtLh1mWt",
        base_url="https://api.chatanywhere.tech/v1",
        temperature=0.5,
    )
    
    # 仅用于测试：使用固定的测试图片
    # image_path = "examples/brain_agent/TG_breakfast.jpg"
    # image_data = encode_image(image_path, new_size=None)   
    image_data = None
    
    return TGBrainAgentV3(
        msg_client=msg_client,
        display_client=display_client,
        agent_executor=agent_executor,
        tools=tools,
        planner=planner,
        replanner=replanner,
        normal_dialogue = normal_dialogue,
        image_data=image_data
    )
