import pyaudio
from typing import Callable
from langchain_openai import ChatOpenAI
from kaiwu_agent.configs.config import ROLE_PROMPT
from kaiwu_agent.tools.interact_tools import brain_thinking_wrapper, smile, wavehand
from kaiwu_agent.tools.search_tools import google_search_wrapper, get_time, get_weather_wrapper
from kaiwu_agent.agents.interact_agent import InteractAgent
from kaiwu_agent.agents.interact_agent_asr_llm_tts import InteractAgentV2
from kaiwu_agent.configs.config import LLM_MODELS
from kaiwu_agent.messages.msg_client import VoiceSocketClient
from kaiwu_agent.agents.interact_agent_qwen import InteractAgentV3
from kaiwu_agent.configs.config import QWENOMNI_SERVER_IP, QWENOMNI_SERVER_PORT,LOCAL_IP,LOCAL_PORT

def gpt4o_realtime_tool_to_schema(func: Callable):
    return {
            "type": "function",
            "name": func.name,
            "description": func.description,
            "parameters": {
                "type": "object",
                "properties": func.args,
                "required": list(func.args.keys()),
                "additionalProperties": False,

            },
    }

#目前只是语音
role_definition = f"""
    {ROLE_PROMPT}
    你处理实时交互。你可以交谈，直接播报用户要求播报的内容或调用工具。\
    你可以调用的工具包括快思考工具和深度思考工具：\
    1. 你可以调用的快思考工具包括: 查当前的时间，查天气，google搜索。\
    2. 深度思考工具：如果需要处理交谈以外的动作指令,或者感知环境的指令,或者需要多步动作才能完成的指令，请调用深度思考工具。\
    3. 如果前面几轮调用了深度思考工具，而根据上下文分析没有完成任务，请保持调用深度思考工具。\
    4. 注意：收到用户指令，请立刻回答用户“收到”，然后再调用相关工具完成指令。\
    注意，请在必要时结合历史信息，不要过度依赖交互历史。
"""


role_definition_qwen = f"""
    {ROLE_PROMPT}
    你处理实时交互。你可以交谈，直接播报用户要求播报的内容或调用工具。\
    你可以调用的工具包括快思考工具和深度思考工具：\
    1. 你可以调用的快思考工具包括: 查时间的工具；查天气的工具，参数：待查询区域的中文名称；谷歌搜索，参数：待搜索的内容。\
    2. 深度思考工具：如果需要处理交谈以外的动作指令,或者感知环境的指令,或者需要多步动作才能完成的指令，请调用深度思考工具。深度思考工具的参数：用户的指令。\
    3. 如果前面几轮调用了深度思考工具，而根据上下文分析没有完成任务，请保持调用深度思考工具。\
    当调用工具的时候，请返回：调用工具+工具名称+工具必须的参数。格式：'''调用<调用工具的名称>，调用参数：<工具的参数>'''，如果调用多个工具，每个遵循前面的格式，请用';'分隔不同的工具。\
    当你无法完成某项任务时，请返回无法完成和无法完成的任务，格式：'''目前无法完成:<无法完成的任务>'''。
    请用语音跟用户交流。
"""
# 微笑，挥手。
# 微笑，挥手，


# 以下是挂梯子的api_key和ws_url
# API_KEY = "********************************************************************************************************************************************************************"
#gpt4o_realtime_url
# WS_URL = 'wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview-2024-12-17'
# 以下是走香港服务器的api_key和url
# API_KEY = 'sk-hQZBRE0osxsf6UGyVe2AppfRmqJRPtLxeG0nBFPpogg12Oj8'
API_KEY = 'sk-tfi7t1lHRe8lBXZPD9zuCPknVZazFH6v2axWfWb0qVxtHRmc'
# WS_URL = 'ws://154.85.43.94:3000/v1/realtime?model=gpt-4o-realtime-preview-2024-12-17'
WS_URL = 'wss://azure-openai-test-xh.openai.azure.com/openai/realtime?api-version=2024-10-01-preview&deployment=gpt-4o-realtime-preview&api-key=BhX2ri6NfP9pVsscgta0MhQo69hwMT5rCO4rlvMpCi6qbom8Jh5XJQQJ99BDACHYHv6XJ3w3AAABACOGV61A'


# gpt 4o realtime
def get_interact_agent(brain_agent, use_xunfei=True):
    audio_config = {
        'CHUNK_SIZE' : 1024,
        'RATE' : 24000,
        'FORMAT' : pyaudio.paInt16,
        'REENGAGE_DELAY_MS' : 500,
        }
    

    google_search = google_search_wrapper()
    get_weather = get_weather_wrapper()

    # the following tools are defined in tools/interact_tools.py
    brain_thinking = brain_thinking_wrapper(brain_agent)

    # tools = [brain_thinking, wavehand, smile, google_search, get_time, get_weather]
    tools = [brain_thinking, google_search, get_time, get_weather]
    tools_available = [
        gpt4o_realtime_tool_to_schema(tool) for tool in tools
    ]

    update_event_session = {
        "modalities": ["text", "audio"],
        "instructions": role_definition,
        "voice": "echo",
        "input_audio_format": "pcm16",
        "output_audio_format": "pcm16",
        "input_audio_transcription": {
            "model": "whisper-1"
        },
        "turn_detection": {
            "type": "server_vad",
            "threshold": 0.2,
            "prefix_padding_ms": 100,
            "silence_duration_ms": 100,
            "create_response": True
        },
        "tools": tools_available,
        "tool_choice": "auto",#required/auto
        "temperature": 0.6,
        "max_response_output_tokens": "inf"
    }
    return InteractAgent(
        audio_config=audio_config,
        api_key=API_KEY,
        ws_url=WS_URL,
        brain_agent=brain_agent,
        update_event_session=update_event_session,
        tools=tools,
        available_tools=tools_available,
        name="interact",
        xfei=use_xunfei)

# asr llm tts替代方案
def get_interact_agent_v2(brain_agent):
    google_search = google_search_wrapper()
    get_weather = get_weather_wrapper()

    # the following tools are defined in tools/interact_tools.py
    brain_thinking = brain_thinking_wrapper(brain_agent)

    # tools = [brain_thinking, wavehand, smile, google_search, get_time, get_weather]
    tools = [brain_thinking, google_search, get_time, get_weather]

    model_name = "gpt-4o"
    # model_name = "Qwen2_5-72B-Instruct"
    model = LLM_MODELS[model_name]

    msg_client = VoiceSocketClient(
            uri="ws://localhost:8788",
            voice_path="/voice",
            tts_path="/ttsplay",
            # TODO: 目前只接受一个指令！
            maxsize=1,
            connect_timeout=3.0
        )

    llm = ChatOpenAI(model=model["model"],
                     api_key=model["api_key"],
                     base_url=model["base_url"],
                     temperature=0.1
    )

    llm = llm.bind_tools(tools, parallel_tool_calls=None)

    history = [{'role':'system','content':role_definition}]

    return InteractAgentV2(brain_agent, msg_client,llm=llm, history=history, all_tools=tools)

# qwen-omni
def get_interact_agent_v3(brain_agent, use_xunfei=True):
    audio_config = {
        'CHUNK_SIZE' : 1024,
        'RATE' : 24000,
        'FORMAT' : pyaudio.paInt16,
        'REENGAGE_DELAY_MS' : 500,
        }

    google_search = google_search_wrapper()
    get_weather = get_weather_wrapper()

    # the following tools are defined in tools/interact_tools.py
    brain_thinking = brain_thinking_wrapper(brain_agent)

    # tools = [brain_thinking, wavehand, smile, google_search, get_time, get_weather]
    tools = [brain_thinking, google_search, get_time, get_weather]

    return InteractAgentV3(
        role_definition=role_definition_qwen,
        audio_config=audio_config,
        api_key=API_KEY,
        ws_url=WS_URL,
        brain_agent=brain_agent,
        tools=tools,
        name="interact",
        xfei=use_xunfei,
        server_ip='***********',
        port=9080,
        url=f"http://{QWENOMNI_SERVER_IP}:{QWENOMNI_SERVER_PORT}/qwen_output",
        url_recv=f"http://{LOCAL_IP}:{LOCAL_PORT}/recv_orin",
        url_post=f"http://{QWENOMNI_SERVER_IP}:{QWENOMNI_SERVER_PORT}/monitor_interruption")