# 控制模式
class BRAIN_VERSIONS:
    # 无大模型，仅NLP
    V1_XF_SKILL = "讯飞技能"
    V2_CUSTOM_INTENT = "自研意图"
    V3_LANG_GRAPH = "基于langgraph"
    V3_LANG_GRAPH_ReAct = "基于langgraph的ReAct"
    V4_VISUAL_AGENT = "VisualBrainAgent"
    ACTOR_CRITIC_SWARM = "Swarm_Framework_For_Actor_Critic"
    ORCHESTRATOR = 'Orchestrator'
    MCTS = 'MCTS'
    DREAMER = 'DREAMER'
    CHAT_BRAIN_AGENT = 'CHAT_BRAIN_AGENT'


# 交互模式
class CONTROL_VERSIONS:
    V1_WEBSOCKETS = "websockets"
    V2_FASTAPI = "fastapi"
    V3_LEGO = "lego"
    V4_CHAT = "open_chat"
    V5_LEGO_UR = "lego_ur"
    TABLE_CLEANING = "clean"
    BOX_MOVE = "box_move"
    LIGHTBULB_CHECK = "lightbulb_check"
    GIFT_PACKAGE = "gift_package"
    ELECTRICIAN = "electrician"
    BOX_MOVE_WRC = "box_move_wrc"
    SORTER_WRC = "sorter_wrc"
    ELECTRICIAN_WRC = "electrician_wrc"
    LIGHTBULB_CHECK_WRC = "lightbulb_check_wrc"
    GIFT_PACKAGE_WRC = "gift_package_wrc"



# 感知模式
class PERCEPTION_VERSIONS:
    V1_SCENE_DECS = "scene_decs"
    V2_PERCETION = "perception"


# 用户访问模式
class USER_INTERFACE_VERSIONS:
    V1_VOICE_CLIENT = "websocket"
    V2_UI = "fastapi"
    COMPOSE = "compose"  # 混合模式, voice + ui
    COMPOSE_WRC = "compose_wrc"