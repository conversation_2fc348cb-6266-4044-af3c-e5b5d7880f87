from kaiwu_agent.configs.base import  CONTROL_VERSIONS, PERCEPTION_VERSIONS
from kaiwu_agent.configs.brain_agent_react_lego import get_tg_brain_agent_react_lego
# from kaiwu_agent.configs.brain_agent_react_lego_ur import get_tg_brain_agent_react_lego_ur
from kaiwu_agent.configs.brain_agent_react_chat import get_tg_brain_agent_react_chat
from kaiwu_agent.configs.brain_agent_react_v1_demo3 import get_agent_react_v1_demo3
# from kaiwu_agent.configs.brain_agent_react_v2_franka import get_agent_react_v2_franka
from kaiwu_agent.configs.brain_agent_react_cleaning import get_brain_agent_react_cleaning
from kaiwu_agent.configs.brain_agent_react_electrician import get_brain_agent_react_electrician
from kaiwu_agent.configs.brain_agent_react_gift_package import get_brain_agent_react_gift_package
from kaiwu_agent.configs.brain_agent_react_box_moving import get_brain_agent_react_box_moving
from kaiwu_agent.configs.brain_agent_react_lightbulb_check import get_brain_agent_react_lightbulb_check
from kaiwu_agent.configs.config_wrc.box_moving import get_brain_agent_react_agent_box_moving_wrc
from kaiwu_agent.configs.config_wrc.electrician import get_brain_agent_react_electrician_wrc
from kaiwu_agent.configs.config_wrc.gift_package import get_brain_agent_react_gift_package_wrc
from kaiwu_agent.configs.config_wrc.lightbulb_check import get_brain_agent_react_lightbulb_check_wrc
from kaiwu_agent.configs.config_wrc.sorter import get_brain_agent_react_sorter_wrc
def get_tg_brain_agent_react(msg_client,
                             model,
                             vlm_model,
                             simulation=False,
                             control_agent_version=CONTROL_VERSIONS.V1_WEBSOCKETS,
                             perception_version=PERCEPTION_VERSIONS.V1_SCENE_DECS,
                             stream_send=False):
    assert isinstance(model, dict), model
    assert isinstance(vlm_model, dict), vlm_model
    
    if control_agent_version == CONTROL_VERSIONS.V1_WEBSOCKETS:
        return get_agent_react_v1_demo3(
            msg_client,
            model=model,
            simulation=simulation
        )
    # elif control_agent_version == CONTROL_VERSIONS.V2_FASTAPI:
    #     return get_agent_react_v2_franka(
    #         msg_client,
    #         model=model,
    #         simulation=simulation,
    #         perception_version=perception_version
    #     )
    elif control_agent_version == CONTROL_VERSIONS.V3_LEGO:
        return get_tg_brain_agent_react_lego(
            msg_client,
            llm_model=model,
            vlm_model=vlm_model,
            simulation=simulation,
            stream_send=stream_send
        )
    elif control_agent_version == CONTROL_VERSIONS.V4_CHAT:
        return get_tg_brain_agent_react_chat(
            msg_client,
            model=model,
            stream_send=stream_send
        )
    # elif control_agent_version == CONTROL_VERSIONS.V5_LEGO_UR:
    #     return get_tg_brain_agent_react_lego_ur(
    #         msg_client,
    #         llm_model=model,
    #         vlm_model=vlm_model,
    #         simulation=simulation,
    #         stream_send=stream_send
    #     )
    elif control_agent_version == CONTROL_VERSIONS.ELECTRICIAN:
        return get_brain_agent_react_electrician(
            msg_client,
            model=model,
        )
    elif control_agent_version == CONTROL_VERSIONS.GIFT_PACKAGE:
        return get_brain_agent_react_gift_package(
            msg_client,
            model=model,
        )
    elif control_agent_version == CONTROL_VERSIONS.BOX_MOVE:
        return get_brain_agent_react_box_moving(
            msg_client,
            model=model,
        )
    elif control_agent_version == CONTROL_VERSIONS.LIGHTBULB_CHECK:
        return get_brain_agent_react_lightbulb_check(
            msg_client,
            model=model,
        )
    elif control_agent_version == CONTROL_VERSIONS.TABLE_CLEANING:
        return get_brain_agent_react_cleaning(
            msg_client,
            model=model
        )
    elif control_agent_version == CONTROL_VERSIONS.BOX_MOVE_WRC:
        return get_brain_agent_react_agent_box_moving_wrc(
            msg_client,
            model=model
        )
    elif control_agent_version == CONTROL_VERSIONS.ELECTRICIAN_WRC:
        return get_brain_agent_react_electrician_wrc(
            msg_client,
            model=model
        )
    elif control_agent_version == CONTROL_VERSIONS.GIFT_PACKAGE_WRC:
        return get_brain_agent_react_gift_package_wrc(
            msg_client,
            model=model
        )
    elif control_agent_version == CONTROL_VERSIONS.LIGHTBULB_CHECK_WRC:
        return get_brain_agent_react_lightbulb_check_wrc(
            msg_client,
            model=model
        )
    elif control_agent_version == CONTROL_VERSIONS.SORTER_WRC:
        return get_brain_agent_react_sorter_wrc(
            msg_client,
            model=model
        )
    else:
        raise NotImplementedError(f"unsupported version: {control_agent_version}")
