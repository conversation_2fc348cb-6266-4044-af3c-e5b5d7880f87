from kaiwu_agent.configs.config import PROD_ROLE_PROMPT, WORLD_PROMPT, \
    SCENE_PROMPT_V3
from kaiwu_agent.configs.commons import get_react_brain_agent, get_llm, get_common_tools
import logging
logger = logging.getLogger(__name__)


# 期望效果
# 1. 请谨慎触发start，指令得是开始整理（清理）桌面（餐桌）
# 2. 请快速暂停pause：说暂停、停下来、别做了，语音结束、动作暂停，brain退出
# 3. 请快速结束退出stop：说退出、结束、终止，语音结束、动作结束并恢复原位，brain退出，一般在演示结束后才用到

# 任务描述
TASK_DESC_PROMPT = """\n\n###任务描述###\
\n你的具体任务是和用户进行开放性聊天，以及给用户演示桌面整理（桌面清理 table cleaning）能力，具体：\
\n1. 在你面前桌面上放置了一些杯子、纸巾、盘子等物品，桌面整理会把物品分类放置。\
\n2. 你支持如下两个桌面整理指令，这两个动作是异步执行的，即你下发指令后会立刻收到回复，但不表示动作已完成： \
\n- start: 机器人开始整理桌面。你也可以通过这个指令在暂停后继续整理。\
\n- pause: 机器人暂停整理桌面，这会停下手上动作，但不会恢复到初始位置。\
\n3. 使用这两个指令时请注意：\
\n- 两个指令可以多次频繁触发，可放心调用，即使桌面整理已经开始或暂停或结束，也要坚决执行用户指令去重新下发操作。\
\n- 请谨慎触发start，指令得是开始整理（清理）桌面（餐桌）。\
\n- 请快速暂停pause：用户说暂停、停下来、停止、结束、退出、别做了或类似，请立刻下发pause让机器人动作暂停，不要直接退出。\
\n4. 双臂操作方面，你目前只支持桌面整理任务，其它操作指令如“帮我那个杯子”等请委婉拒绝。\
"""

# 支持停止的版本：
# TASK_DESC_PROMPT = """\n\n###任务描述###\
# \n你的具体任务是和用户进行开放性聊天，以及给用户演示桌面整理（桌面清理 table cleaning）能力，具体：\
# \n1. 在你面前桌面上放置了一些杯子、至今、盘子等杂物。\
# \n2. 你支持如下三个桌面整理指令，这三个动作是异步执行的，即你下发指令后会立刻收到回复，但不表示动作已完成： \
# \n- start: 机器人开始整理桌面。你也可以通过这个指令在暂停后继续整理。\
# \n- pause: 机器人暂停整理桌面，这会停下手上动作，但不会恢复到初始位置。\
# \n- stop: 机器人结束整理桌面，全身恢复到初始位置。\
# \n3. 使用这三个指令时请注意：\
# \n- 三个指令可以多次频繁触发，可放心调用，即使桌面整理已经开始或暂停或结束，也要坚决执行用户指令去重新下发操作。\
# \n- 请谨慎触发start，指令得是开始整理（清理）桌面（餐桌）。\
# \n- 请快速暂停pause：用户说暂停、停下来、别做了或类似，请立刻下发pause让机器人动作暂停，不要直接退出。\
# \n- 请快速结束退出stop：用户说退出、结束、终止或类似，请立刻下发stop让机器人动作动作结束并恢复原位，不要直接退出。\
# \n4. 你目前无法移动，也无法实现任意桌面物品的抓放等操作，这些功能还在开发中，操作方面目前只支持桌面清理任务。\
# """


def get_custom_mcp_cfg():
    return {
        "table_cleaning": {
            # debug
            # "url": "http://127.0.0.1:8000/sse",
            "url": "http://192.168.41.55:8123/sse",
            "transport": "sse",
        },
    }

# 指令中包含这些关键词就会让brain停下来，但目前不能保证立刻停止所有动作
EXIT_KEYWORDS = [
    # "立刻退出", "退出", "退出吧", 
    # "立刻停止", "停下", "停下来", "停下了", "暂停", "停止",
    # "立刻结束", "结束吧",
    # "立刻终止",
    # "别做了", 
    # "暂停一下",
    # "stop" 
    "闭嘴", "别说了", 
    # "等一下", "请等等", "请等一下",  # "稍等", "等等", 
    "不要说了", "不要说话了", "不要说话",
    "shut up",
]


def get_brain_agent_react_cleaning(msg_client,
                                  model: dict,
                                  stream_send=False):
    
    tools = get_common_tools()
    # 源自mcp服务器的工具，和本地定义的工具混合在一起，通过function call调用
    mcp_cfg = get_custom_mcp_cfg()
    
    prompt = PROD_ROLE_PROMPT + TASK_DESC_PROMPT + SCENE_PROMPT_V3 + WORLD_PROMPT
    
    llm = get_llm(model)
    
    return get_react_brain_agent(
        msg_client=msg_client,
        prompt=prompt, 
        model=llm, 
        tools=tools,
        enable_memory=True, 
        parallel_tool_calls=True,
        mcp_cfg=mcp_cfg,
        accept_msg_when_busy=True,
        stream_send=stream_send   # 打开流式生成、流式发送去做TTS，提高首响
    )
