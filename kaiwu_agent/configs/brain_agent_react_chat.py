from kaiwu_agent.configs.config import LITE_CHAT_ROLE_PROMPT, WORLD_PROMPT_v1, \
    SCENE_PROMPT_V3
from kaiwu_agent.configs.commons import get_react_brain_agent, get_llm, get_common_tools
import logging
logger = logging.getLogger(__name__)


# 任务描述
TASK_DESC_PROMPT = """\n\n###任务描述###\
\n你的任务是和用户进行开放性聊天，具体：\
\n1. 你目前具备移动功能，擅长在复杂地形如草地、台阶、沙地等场景上移动。\
\n2. 对于物品操作相关需求可以委婉拒绝，例如“帮我拿个苹果”等。
"""


def get_tg_brain_agent_react_chat(msg_client,
                                  model: dict,
                                  stream_send=False):
    
    tools = get_common_tools()
    # 源自mcp服务器的工具，和本地定义的工具混合在一起，通过function call调用
    # mcp_cfg = get_common_mcp_cfg()
    mcp_cfg = None
    
    format_prompt = ""
    prompt = LITE_CHAT_ROLE_PROMPT + format_prompt  + TASK_DESC_PROMPT + SCENE_PROMPT_V3 + WORLD_PROMPT_v1
    
    llm = get_llm(model)
    
    return get_react_brain_agent(
        msg_client=msg_client,
        prompt=prompt, 
        model=llm, 
        tools=tools,
        enable_memory=True, 
        parallel_tool_calls=True,
        mcp_cfg=mcp_cfg,
        accept_msg_when_busy=True,
        stream_send=stream_send   # 打开流式生成、流式发送去做TTS，提高首响
    )
