from kaiwu_agent.tools.langgraph_tools import interact_with_user_wrapper, look_up_wrapper
from kaiwu_agent.tools.block_tools import lego_control_wrapper, lego_desc_wrapper, lego_desc_wrapper_v1, lego_examiner_wrapper
from kaiwu_agent.tools.search_tools import google_search_wrapper, get_time, get_weather_wrapper
from kaiwu_agent.agents import LegoControlAgent
from kaiwu_agent.configs.config import ROLE_PROMPT, WORLD_PROMPT_v1, \
    SCENE_PROMPT_V3, VLM_MODELS, LOCAL_DOC_FILE, LLM_MODELS
from kaiwu_agent.configs.commons import get_chat_agent, get_react_brain_agent, get_local_doc_lookup_tool
from kaiwu_agent.utils.orbbec_camera import OrbbecCameraClient
from kaiwu_agent.utils.lego.lego_descriptor_v2 import BlockImageProcessor
from kaiwu_agent.configs.config import REAL_IMAGE_FOLDER_UR
from kaiwu_agent.configs.brain_agent_react_lego import DESC_PROMPT, COLOR_MAP, BL<PERSON><PERSON>_MAP, DET_SERVER_IP
import logging
logger = logging.getLogger(__name__)


CAMERA_SERVER_IP = "*************"   
DESCRIPTION_CAMERA_PORT = '4242'
EXAMINER_CAMERA_PORT = '4243'

# lego_processor visual relative parameters, copied from test_block_tools_v2.py & assets/max1/parameters.json
DESCRIPTION_CAMERA_PARAMS = {
   "crop_size": [880, 480, 520, 500],
    "angle":  1,# rotate angle
    "grid_distance":  80, # 假设积木一格的大小为 grid_distance 个像素值
    "layer_threshold":  30, # 判定积木层数的阈值，两块积木之间的差值如果小于该值则认为是同一层积木
    "head_height": 67.0,
}

# lego_processor visual relative parameters, copied from block_tools.py
EXAMINER_CAMERA_PARAMS = {
    "crop_size": [520, 320, 620, 600],
    "angle":  0.5,# rotate angle
    "grid_distance":  80, # 假设积木一格的大小为 grid_distance 个像素值
    "layer_threshold":  35, # 判定积木层数的阈值，两块积木之间的差值如果小于该值则认为是同一层积木
    "head_height": 45,
}

# 任务描述
TASK_DESC_PROMPT = """\n\n###任务描述###\
\n你的具体任务是和用户进行开放性聊天和搭建乐高积木。用户会在你面前搭建一份多层乐高积木样例，当用户让你把积木样例搭建复现出来时，你可以按照以下流程进行搭建：\
\n1. 先给用户描述积木样例的信息，要求描述简单一些，描述层数和每层有哪些颜色的积木即可，位置和尺寸不用说。\
\n2. 然后通过调用积木操作工具，逐层搭建积木，且每层搭建完成后，调用积木检查工具，检查当前搭建的积木符合要求，并告知用户检查结果。\
\n3. 最后告知用户最终的搭建结果，成功或者失败，以及失败的原因。\
\n4. 当用户要求重新描述、重新感知、重新搭建时，你要重头开始重新调用工具，不要使用历史记忆。"""

# \n1. 先给用户描述积木样例的信息，然后询问用户你所描述的积木信息是否准确，在获得用户的多轮反馈和纠正后，拿到正确的积木样例信息。\
# \n2. 先通知用户把积木样例移走，在用户确认移走后，你在原位置上开始搭建积木。\

def get_tools(msg_client, llm_model: dict, vlm_model: dict, simulation=False):
    # 不传msg_client，中间结果暂不播报
    control_agent = LegoControlAgent(
        port=9529,
        # 乐高ControlAgent服务
        # server_url = "http://*************:9528/v1/command",
        server_url = "http://************:9528/v1/command",
        # 测试用：用tools/fake_control_server_v2.py部署的
        # server_url = "http://localhost:9528/v1/command",
        msg_client=msg_client,
        log_recv=True,
        result_timeout=240.0,
        connect_timeout=5.0
    )
    
    lego_commons = dict(
        prompt_text=DESC_PROMPT, 
        det_url=f"http://{DET_SERVER_IP}:8091/segment_objects", 
        api_key=vlm_model["api_key"], 
        gpt_url=vlm_model["base_url"], 
        model=vlm_model["model"], 
    )
    lego_desc = BlockImageProcessor(
        camera_params=DESCRIPTION_CAMERA_PARAMS,
        color_map=COLOR_MAP,
        block_map=BLOCK_MAP,
        **lego_commons
    )
    lego_exam = BlockImageProcessor(
        camera_params=EXAMINER_CAMERA_PARAMS,
        color_map=COLOR_MAP,
        block_map=BLOCK_MAP,
        **lego_commons
    )
    
    # 用本地文档做RAG
    look_up = get_local_doc_lookup_tool(model=llm_model, doc_file=LOCAL_DOC_FILE)
    google_search = google_search_wrapper()
    get_weather = get_weather_wrapper()
    lego_control = lego_control_wrapper(control_agent)
    
    # 固定的图仿真，还是取实时图像
    camera_commons = dict(
        simulation=simulation, 
        server_ip=CAMERA_SERVER_IP, 
        test_img_path="tests/block_tools/lego_input/000492_0000_5436816374_1733817934479988_Color_1280x800.png", 
        default_view='front', 
        width=1920,
        height=1080,
        save_folder=REAL_IMAGE_FOLDER_UR
    )
    camera_client = OrbbecCameraClient(
        default_port=DESCRIPTION_CAMERA_PORT,  
        **camera_commons
    )

    examiner_camera_client = OrbbecCameraClient(
        default_port=EXAMINER_CAMERA_PORT,  
        **camera_commons
    )
    
    lego_desc = lego_desc_wrapper_v1(
        lego_processor=lego_desc,
        camera_client=camera_client,
        result_root="./assets/lego_ur/desc",
        add_time=False
    )

    lego_examiner = lego_examiner_wrapper(
        lego_desc=lego_desc,
        lego_exam=lego_exam,
        camera_client=examiner_camera_client,
        result_root="./assets/lego_ur/examiner",
        add_time=False
    )
    
    tools = [lego_desc, lego_examiner, lego_control, look_up,
             google_search, get_time, get_weather]
    return tools


def get_tg_brain_agent_react_lego_ur(msg_client,
                                     llm_model: dict,
                                     vlm_model: dict,
                                     simulation=False,
                                     stream_send=False):
    tools = get_tools(msg_client=msg_client, 
                      llm_model=llm_model,
                      vlm_model=vlm_model,
                      simulation=simulation)
    
    format_prompt = ""
    prompt = ROLE_PROMPT + format_prompt  + TASK_DESC_PROMPT + SCENE_PROMPT_V3 + WORLD_PROMPT_v1
    
    return get_react_brain_agent(
        msg_client=msg_client,
        prompt=prompt, 
        model=llm_model, 
        tools=tools,
        stream_send=stream_send   # 打开流式生成、流式发送去做TTS，提高首响
    )
