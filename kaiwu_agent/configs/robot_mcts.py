import os
import json
from kaiwu_agent.tools.langgraph_tools import manipulation_wrapper, look_up_wrapper, \
    interact_with_user_wrapper, robotic_arm_operation_wrapper, \
    robotic_arm_operation_plan_wrapper, release_hand_wrapper, \
    robotic_arm_operation_dreamer_wrapper
from kaiwu_agent.tools.search_tools import google_search_wrapper, get_time, \
    get_weather_wrapper
from kaiwu_agent.configs.base import CONTROL_VERSIONS
from kaiwu_agent.configs.config import *
from kaiwu_agent.configs.control_agent import get_control_agent
from kaiwu_agent.configs.commons import get_chat_agent, get_orbbec_client
from kaiwu_agent.utils.common import langraph_tool_to_schema
from kaiwu_agent.utils.openai_support import function_to_schema
from kaiwu_agent.agents.models.models import *
from kaiwu_agent.agents.mcts.robot_mcts import RobotMCTSWrapper
from kaiwu_agent.agents import ChatAgent, FunctionCallAgent, Dreamer, MCTSBrain


def perception():
    """获取图像以感知和理解环境。
    parameters:
    """
    return GET_REAL_IMAGE_RESPONSE

def get_robot_mcts_brain(msg_client,
                     model,
                     simulation=False,
                     control_agent_version=CONTROL_VERSIONS.V1_WEBSOCKETS,
                     save_folder="assets/data"):
    robot_mcts = get_robot_mcts(msg_client, model, simulation, control_agent_version, save_folder)
    return MCTSBrain(msg_client, robot_mcts)

def get_robot_mcts(msg_client,
                   model,
                   simulation=False,
                   control_agent_version=CONTROL_VERSIONS.V1_WEBSOCKETS,
                   save_folder="assets/data",
                   visual_world=True,
                   text_world=False):
    save_folder = os.path.join(save_folder, "robot_mcts") if save_folder else save_folder
    # 不传msg_client，中间结果暂不播报
    control_agent = get_control_agent(msg_client=msg_client, log_recv=False, version=control_agent_version)
    chat_agent = get_chat_agent()
    camera_client = get_orbbec_client(simulation=simulation)
    visual_dreamer = Dreamer()

    robotic_arm_operation_dreamer = robotic_arm_operation_dreamer_wrapper(visual_dreamer)
    manipulation = manipulation_wrapper(control_agent) if control_agent_version == CONTROL_VERSIONS.V1_WEBSOCKETS \
        else robotic_arm_operation_wrapper(control_agent)
    robotic_arm_operation_plan = robotic_arm_operation_plan_wrapper(control_agent)
    release_hand = release_hand_wrapper(control_agent)
    look_up = look_up_wrapper(chat_agent)
    # input_from_terminal: 从命令行输入指令，还是从麦克风获取指令
    interact_with_user = interact_with_user_wrapper(msg_client, input_from_terminal=False , seg_len=80)
    google_search = google_search_wrapper(msg_client=msg_client)
    get_weather = get_weather_wrapper(msg_client=msg_client)

    scene_prompt = SCENE_PROMPT
    tools = [interact_with_user, look_up, google_search, get_time, get_weather]
    if visual_world:
        tools.append(robotic_arm_operation_dreamer)
    elif control_agent_version == CONTROL_VERSIONS.V1_WEBSOCKETS: # demo3
        tools.extend([manipulation, release_hand])
    elif control_agent_version == CONTROL_VERSIONS.V2_FASTAPI:
        tools.append(robotic_arm_operation_plan)
        scene_prompt += FRANKA_KITCHEN_SCENE_PROMPT
    else:
        raise NotImplementedError

    agent_executor = FunctionCallAgent(
        name='Executor',
        system_prompt=ROLE_PROMPT,
        model=model["model"],
        api_key=model["api_key"],
        api_base=model["base_url"],
        image_size=[448, 448],
        tool_choice='auto' if model["model"] in ["gpt-4o", 'o1', 'o1-mini'] else None,
        tools=tools,
        paser=["Action:"] if model["model"] in ["InternVL2_5-78B"] else ["<tool_call>", "</tool_call>"],
        camera_client=camera_client,
        stop=["\nObservation:", "Observation:"],
        keep_message_history=False,
        recursive=False,
        save_folder=save_folder,
        function_to_schema=langraph_tool_to_schema,
    )

    agent_executor.add_tool(perception, function_to_schema(perception))
    tools_list = agent_executor.get_tools_list()
    tools_decs = "\n\n###可用工具###\n{}".format(json.dumps(tools_list, ensure_ascii=False))
    TOOL_DESC = tools_decs if model["model"] not in ["gpt-4o", 'o1', 'o1-mini'] else ""
    format_prompt = FUNCTION_CALL_PROMPT  if model["model"] not in ["gpt-4o", 'o1', 'o1-mini'] else ""
    basic_prompt = TOOLS_PROMPT_V2 + scene_prompt + WORLD_PROMPT
    agent_executor.set_system_prompt(ROLE_PROMPT + format_prompt + TOOL_DESC + basic_prompt)

    dreamer_model = VLM_MODELS["Qwen2-VL-72B-Instruct"]
    agent_dreamer = FunctionCallAgent(
        name="Dreamer",
        system_prompt=ROLE_PROMPT + FUNCTION_CALL_PROMPT + TOOL_DESC + basic_prompt,
        text_world=text_world,
        model=dreamer_model["model"],
        api_key=dreamer_model["api_key"],
        api_base=dreamer_model["base_url"],
        paser=["Action:"] if model["model"] in ["InternVL2_5-78B"] else ["<tool_call>", "</tool_call>"],
        stop=["\nThought:", "Thought:"],
        keep_message_history=False,
        recursive=False,
        save_folder=save_folder,
        function_to_schema=langraph_tool_to_schema,
    )

    agent_actor = ChatAgent(
        name='Actor',
        system_prompt=ROLE_PROMPT + ACTOR_PROMPT + tools_decs + basic_prompt,
        model=model["model"],
        api_key=model["api_key"],
        api_base=model["base_url"],
        keep_message_history=False,
        save_folder=save_folder,
    )

    agent_critic = ChatAgent(
        name='Critic',
        system_prompt=ROLE_PROMPT + CRITIC_PROMPT + basic_prompt,
        model=model["model"],
        api_key=model["api_key"],
        api_base=model["base_url"],
        keep_message_history=False,
        save_folder=save_folder,
    )

    agent_score = ChatAgent(
        name='Score',
        system_prompt=SCORE_PROMPT,
        model=model["model"],
        api_key=model["api_key"],
        api_base=model["base_url"],
        keep_message_history=False,
        save_folder=save_folder,
        stop=["\nProposed", "\nProposed actions:", "Proposed actions:"]
    )
    
    robot_mcts = RobotMCTSWrapper(
        actor=agent_actor,
        critic=agent_critic,
        executor=agent_executor if not text_world else agent_dreamer,
        score=agent_score,
        n_iterations=10,
        depth_limit=6,
        exploration_weight=1.0,
    )
    return robot_mcts