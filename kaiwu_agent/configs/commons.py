import pprint
from dotenv import load_dotenv
from kaiwu_agent.agents import DifyChatAgent
from kaiwu_agent.utils.orbbec_camera import OrbbecCameraClient
from kaiwu_agent.configs.config import CONTROL_AGENT_IP, TEST_IMAGE_PATH, ORBBEC_DEFAULT_VIEW_PORT, REAL_IMAGE_FOLDER
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent
from kaiwu_agent.agents import GraphBrainAgent, GraphBrainAgentStream, ChatBrainAgent
from kaiwu_agent.configs.config import \
    RECV_MUSIC_PATH, FINISH_MUSIC_PATH, LOCAL_DOC_FILE
from kaiwu_agent.agents.chat_agent import OpenAIStyleChatAgent
from kaiwu_agent.tools.langgraph_tools import look_up_wrapper_v1
from kaiwu_agent.tools.search_tools import BoChaSearch, GetTime, HeFengWeather, KnowledgeSearchV1
from kaiwu_agent.graphs.react_agent import ReactAgentCreator
import logging
logger = logging.getLogger(__name__)


def get_common_tools():
    load_dotenv()
    tool_creators = [
        BoChaSearch(),
        GetTime(),
        HeFengWeather(),
        KnowledgeSearchV1(doc_file=LOCAL_DOC_FILE, 
                          tool_desc="""知识库检索工具: 可回答和公司相关的问题，例如公司的人员、产品、组织架构等，例如"介绍一下你们公司"。"""),
    ]
    return [tool_creator.make_tool() for tool_creator in tool_creators]


def get_common_mcp_cfg():
    return {
        # "math": {
        #     "command": "python",
        #     # Replace with absolute path to your math_server.py file
        #     "args": ["/path/to/math_server.py"],
        #     "transport": "stdio",
        # },
        # "weather": {
        #     # Ensure your start your weather server on port 8000
        #     "url": "http://localhost:8000/sse",
        #     "transport": "sse",
        # },
        # "ipinfo": {
        #     # 提前安装uvx: https://docs.astral.sh/uv/getting-started/installation/
        #     "command": "uvx",
        #     "args": [
        #         "--from",
        #         "git+https://github.com/briandconnelly/mcp-server-ipinfo.git",
        #         "mcp-server-ipinfo"
        #     ],
        #     "transport": "stdio",
        #     "env": {
        #         # https://ipinfo.io/dashboard/token
        #         "IPINFO_API_TOKEN": "dd3e05ee70250c"
        #     }
        # }
    }


def get_chat_agent():
    return DifyChatAgent(
        token="app-YvNWFJzQyX9x6LyBqDtui6NW",
        url='http://*************/v1/chat-messages'
    )


def get_orbbec_client(simulation):
    return OrbbecCameraClient(
        simulation=simulation, 
        server_ip=CONTROL_AGENT_IP, 
        test_img_path=TEST_IMAGE_PATH, 
        default_view='front', 
        default_port=ORBBEC_DEFAULT_VIEW_PORT['front'], 
        save_folder=REAL_IMAGE_FOLDER
    )


# def get_react_agent_creator(llm :ChatOpenAI, tools, memory, prompt, mcp_cfg=None):
#     from langchain_mcp_adapters.client import MultiServerMCPClient
    
#     async def _make_react_agent():
#         try:
#             if mcp_cfg is not None:
#                 mcp_client = MultiServerMCPClient(mcp_cfg)
#                 mcp_tools = await mcp_client.get_tools()
#                 _tools = mcp_tools + tools
#             else:
#                 _tools = tools
#             logger.info("可用的工具如下：")
#             logger.info(pprint.pformat(_tools))
#             # 禁止并行调用多个tool（manipulation不支持并行），后续要支持同时聊天和操作再去掉
#             # _llm = llm.bind_tools(_tools, parallel_tool_calls=False)
            
#             # 允许工具并行，即一次推理出多个工具，但为了安全目前执行是串行，因为设置了max_concurrency=1
#             _llm = llm.bind_tools(_tools, parallel_tool_calls=True)
            
#             agent = create_react_agent(_llm, _tools, prompt=prompt, checkpointer=memory)
#             return agent
#         except Exception as err:
#             logger.error(f"{err}\nReact Agent初始化失败，请检查")
#             return None
        
#     return _make_react_agent


def get_llm(model: dict, class_name="ChatOpenAI"):
    if class_name == "ChatOpenAI":
        llm = ChatOpenAI(model=model["model"],
                        api_key=model["api_key"],
                        base_url=model["base_url"],
                        temperature=0)
    else:
        raise NotImplementedError(class_name)
    return llm

def get_react_brain_agent(msg_client, prompt, model, tools, 
                          enable_memory=True,
                          parallel_tool_calls=True,
                          name=None,
                          kaiwu_msg_client=None, 
                          mcp_cfg=None,
                          exit_keywords=None,
                          out_dir="output/data/",
                          accept_msg_when_busy=False,
                          stream_send=False,
                          recv_music_path=RECV_MUSIC_PATH,
                          finish_music_path=FINISH_MUSIC_PATH,
                          agent_name: str = None,
                          agent_uuid: str = None):
    logger.info(f"system prompt: {prompt}")
    agent_creator = ReactAgentCreator(
        model=model, 
        tools=tools, 
        system_prompt=prompt, 
        mcp_cfg=mcp_cfg, 
        parallel_tool_calls=parallel_tool_calls,
        enable_memory=enable_memory
    )
    
    commons = dict(
        name=name,
        msg_client=msg_client,
        graph_creator=agent_creator,
        system_prompt=prompt,
        kaiwu_msg_client=kaiwu_msg_client,
        max_concurrency=1,   # tool call并行数量，默认不开启并行执行
        exit_keywords=exit_keywords,
        out_dir=out_dir,
        accept_msg_when_busy=accept_msg_when_busy,
        recv_music_path=recv_music_path,
        finish_music_path=finish_music_path,
        agent_name=agent_name,
        agent_uuid=agent_uuid
    )
    if stream_send:
        return GraphBrainAgentStream(
            **commons,
            stream_send=stream_send  # 打开流式生成、流式发送去做TTS，提高首响
        )
    else:
        return GraphBrainAgent(
            **commons
        )


def get_local_doc_lookup_tool(model: dict, doc_file: str):
    # 用本地的文档查询总结
    chat_agent = OpenAIStyleChatAgent(
        model=model["model"],
        api_key=model["api_key"],
        base_url=model["base_url"]
    )
    with open(doc_file, 'r') as file:
        knowledge_base = file.read()
    look_up = look_up_wrapper_v1(chat_agent=chat_agent, 
                                 knowledge_base=knowledge_base)
    return look_up    


def get_chat_brain_agent(msg_client, prompt, model):
    logger.info(f"system prompt: {prompt}")
    llm = ChatOpenAI(model=model["model"],
                     api_key=model["api_key"],
                     base_url=model["base_url"],
                     temperature=0,
                    #  default_headers={"x-stainless-skip-status-check": "true"}
    )
    return ChatBrainAgent(
        msg_client=msg_client,
        chat_model=llm,
        system_prompt=prompt,
        history_size=21,   # 历史记忆条数
        msg_buffer_size=5
    )
