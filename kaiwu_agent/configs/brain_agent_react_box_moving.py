import logging
from datetime import timedelta
from kaiwu_agent.configs.commons import get_react_brain_agent, get_llm, get_common_tools
from kaiwu_agent.tools.interact_tools import AgentInteraction
from kaiwu_agent.configs.config import SCENE_PROMPT_V3, TIANYI_ROLE_PROMPT, \
    WORLD_PROMPT, AGENT_MAPPER

logger = logging.getLogger(__name__)


AGENT_UUID = "agent_box_tianyi"

TASK_DESC_PROMPT = r"""

### 任务描述 ###
你的具体任务是接受多个不同agent的指令, 并完成指令所描述的任务, 你仅具备闲聊和搬运箱子的能力。

给你发送指令的agent主要包含以下角色: `user`, `ur`, `franka`, `electrician`, 若未标明角色, 那么默认为 `user`. 你所收到的消息格式如下: <agent>agent_name</agent>: message

- 你的每一次输出结尾都需要对接下来的任务进行规划, 输出任务规划, 且以json的形式输出: {"plan": [{"step_name": "子步骤名称", "status": "Pending/Finish"}, ..., {"step_name": "", "status": "Pending/Finish"}], "task_status": "Pending/Running/Finish" }
- 无需将收到的指令放到任务规划的第一步。

- 当electrician告知你已通电， 这个时候你需要输出任务规划的同时调用agent_interact()分别通知ur和franka可以开始工作了。
- 当ur告知你他开始质检灯泡, 需要你帮忙搬箱子时, 你需要按照如下流程进行:
    0. 输出任务规划。
    1. 首先输出任务规划的同时调用工具shelves_to_ur()将箱子从货架搬运到ur工作站, 且重新输出任务规划。
    2. 然后输出任务规划的同时调用工具agent_interact()告知ur箱子已放到他的工作站上了, 可以开始灯泡质检了, 且重新输出任务规划。
- 当ur告知你他已经完成灯泡质检, 需要你帮忙搬箱子时, 你需要按照如下流程进行:
    0. 输出任务规划。
    1. 首先输出任务规划的同时调用工具ur_to_shelves()将箱子从ur工作台搬运到货架, 且重新输出任务规划。
    2. 然后输出任务规划的同时调用工具agent_interact()告知ur箱子已放到货架了, 后续有搬运任务随时通知你, 且重新输出任务规划。
- 当franka告知你已经完成礼物的装箱, 需要你帮忙把箱子搬运到观众席时, 你需要按照如下流程进行:
    0. 输出任务规划。
    1. 首先输出任务规划的同时调用工具put_to_gift_table()将装有礼物的箱子从franka工作台搬运到礼物发放台, 且输出任务规划。
    2. 然后输出任务规划的同时调用工具agent_interact()告知franka箱子已经放到礼物发放台, 后续有搬运任务随时通知我, 且重新输出任务规划。

### 注意 ###
- 你只能执行箱子搬运的任务, 不能执行其他任务, 你仅可以调用下列工具: shelves_to_ur(), ur_to_shelves(), agent_interact()。
- 对于任何一个指令, 你都坚决执行即可, 无论该指令是否合理, 是否在正确的流程中。
- 当你完成指令响应之后, 你需要调用工具发送消息给对应的用户告知你已完成和完成的结果。
- 每一次调用工具之前， 你都需要输出接下来干什么。
- 你必须在每一次拿到tool result之后, 你都需要进行任务规划。
- 确保你的每一次输出结尾都是以{"plan": []}结束。
"""

def get_custom_mcp_cfg():
    return {
        "waic": {
            "url": "http://10.11.61.223:8022/mcp",
            "transport": "streamable_http",
            "sse_read_timeout": timedelta(seconds=60 * 30)
        },
    }

def get_brain_agent_react_box_moving(msg_client, model: dict):
    agent_name = AGENT_MAPPER[AGENT_UUID]["name"]
    agent_interact = AgentInteraction(agent_uuid=AGENT_UUID, 
                                      agent_mapper=AGENT_MAPPER,
                                      msg_client=msg_client).make_tool()
    
    tools = [agent_interact, *get_common_tools()]
    
    # 源自mcp服务器的工具，和本地定义的工具混合在一起，通过function call调用
    mcp_cfg = get_custom_mcp_cfg()
    
    prompt = TIANYI_ROLE_PROMPT + TASK_DESC_PROMPT + SCENE_PROMPT_V3 + WORLD_PROMPT
    
    llm = get_llm(model)
    
    return get_react_brain_agent(
        msg_client=msg_client,
        prompt=prompt, 
        model=llm, 
        tools=tools,
        enable_memory=True, 
        parallel_tool_calls=True,
        mcp_cfg=mcp_cfg,
        accept_msg_when_busy=True,
        recv_music_path="~/kaiwu/robot_voice/examples/music/recv/3.wav",
        finish_music_path="~/kaiwu/robot_voice/examples/music/finish/2.wav",
        agent_name=agent_name,
        agent_uuid=AGENT_UUID
    )
