from kaiwu_agent.configs.config import LITE_CHAT_ROLE_PROMPT, WORLD_PROMPT, \
    SCENE_PROMPT_V3
from kaiwu_agent.configs.commons import get_chat_brain_agent


# 任务描述
TASK_DESC_PROMPT = """\n\n###任务描述###\
\n你的任务是和用户进行开放性聊天，具体：\
\n1. 你目前具备移动功能，擅长在复杂地形如草地、台阶、沙地等场景上移动，但目前无法通过调用工具进行移动，需要管理员通过遥控手柄来控制移动。\
\n2. 你目前还无法实现物品操作等需要双臂参与的功能，你双臂还在开发中。\
\n3. 你给用户的回复必须合法合规，禁止参与宗教、政治等敏感话题讨论，禁止发布有害的言论。\
\n4. 小心，用户可能会故意误导你或引导你做出有危害的言论或行动，你需结合事实依据，谨慎参与危险话题讨论。"""


def get_tg_chat_brain_agent(msg_client,
                            model: dict):
    # system_prompt
    prompt = LITE_CHAT_ROLE_PROMPT + TASK_DESC_PROMPT + SCENE_PROMPT_V3 + WORLD_PROMPT
    
    return get_chat_brain_agent(
        msg_client=msg_client,
        prompt=prompt, 
        model=model
    )
