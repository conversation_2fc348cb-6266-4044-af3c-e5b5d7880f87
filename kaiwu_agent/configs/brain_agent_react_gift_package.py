import logging
from datetime import timedelta
from kaiwu_agent.configs.commons import get_react_brain_agent, get_llm
from kaiwu_agent.tools.interact_tools import AgentInteraction
from kaiwu_agent.configs.config import SCENE_PROMPT_V3, FRANKA_ROLE_PROMPT,  WORLD_PROMPT, AGENT_MAPPER

logger = logging.getLogger(__name__)


AGENT_UUID = "gift_package"

TASK_DESC_PROMPT = r"""

### 任务描述 ###
你的具体任务是接受多个不同agent的指令, 并完成指令所描述的任务, 你仅具备礼物装箱的能力。 

给你发送指令的agent主要包含以下角色: `user`, `tianyi`, `ur`, `electrician`, 若未标明角色, 那么默认为 `user`. 你所收到的消息格式如下: <agent>agent_name</agent>: message

- 你的每一次输出结尾都需要对接下来的任务进行规划, 输出任务规划, 且以json的形式输出: {"plan": [{"step_name": "子步骤名称", "status": "Pending/Finish"}, ..., {"step_name": "", "status": "Pending/Finish"}], "task_status": "Pending/Running/Finish" }
- 无需将收到的指令放到任务规划的第一步。

- 当你收到已经通电, 可以开始工作的指令时, 调用工具start()完成礼品装箱的工作, 且重新输出任务规划。
- 当礼品装箱完成之后, 你需要调用工具agent_interact()通知天轶(tianyi), 将礼品箱从franka工作台搬运到礼品发放区, 且重新输出任务规划。

#### 注意 ####
- 你只能执行礼品装箱相关的任务, 不能执行其他任务, 你仅可以调用下列工具: agent_interact(), start()。
- 当没有明确指定需要装多少件礼品时, 无需询问用户， 直接装2件礼品。
- 上述流程需要严格按照顺序执行, 不能跳过步骤，请按照流程进行操作。
- 当指令是`user`发送的， 你可以不按照流程进行，直接执行指令。
- 对于任何一个指令, 你都坚决执行即可, 无论该指令是否合理, 是否在正确的流程中。
- 当你完成指令响应之后, 你需要调用工具发送消息给对应的用户告知你已完成和完成的结果。
- 你必须在每一次拿到tool result之后, 你都需要进行任务规划。
- 确保你的每一次输出结尾都是以{"plan": []}结束。
"""

def get_custom_mcp_cfg():
    return {
        "waic": {
            "url": "http://10.10.249.180:8000/mcp",
            "transport": "streamable_http",
            "sse_read_timeout": timedelta(seconds=60 * 30)
        }
    }

def get_brain_agent_react_gift_package(msg_client, model: dict):
    agent_name = AGENT_MAPPER[AGENT_UUID]["name"]
    agent_interact = AgentInteraction(agent_uuid=AGENT_UUID, 
                                      agent_mapper=AGENT_MAPPER,
                                      msg_client=msg_client).make_tool()
    
    # 源自mcp服务器的工具，和本地定义的工具混合在一起，通过function call调用
    mcp_cfg = get_custom_mcp_cfg()
    
    prompt = FRANKA_ROLE_PROMPT + TASK_DESC_PROMPT + SCENE_PROMPT_V3 +WORLD_PROMPT
    
    llm = get_llm(model)
    
    return get_react_brain_agent(
        msg_client=msg_client,
        prompt=prompt, 
        model=llm, 
        tools=[agent_interact],
        enable_memory=True, 
        parallel_tool_calls=True,
        mcp_cfg=mcp_cfg,
        accept_msg_when_busy=True,
        recv_music_path="~/kaiwu/robot_voice/examples/music/recv/3.wav",
        finish_music_path="~/kaiwu/robot_voice/examples/music/finish/2.wav",
        agent_name=agent_name,
        agent_uuid=AGENT_UUID
    )
