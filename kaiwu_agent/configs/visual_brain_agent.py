import os
import json
from kaiwu_agent.agents import FunctionCallAgent, VisualBrainAgent
from kaiwu_agent.tools.langgraph_tools import interact_with_user_wrapper, \
    manipulation_wrapper, look_up_wrapper, release_hand_wrapper, scene_desc_wrapper, \
    robotic_arm_operation_wrapper, robotic_arm_operation_plan_wrapper, perception_wrapper
from kaiwu_agent.configs.base import CONTROL_VERSIONS
from kaiwu_agent.configs.config import ROLE_PROMPT, WORLD_PROMPT, SCENE_PROMPT, SCENE_PROMPT_V2, \
    REPLY_AND_ACTION_FUNCTION_CALL_PROMPT, FRANKA_KITCHEN_SCENE_PROMPT, INTERACT_FUNC_NAME, \
    ACTION_ONLY_FUNCTION_CALL_PROMPT, UR_INDUSTRY_SCENE_PROMPT, GET_REAL_IMAGE_RESPONSE
from kaiwu_agent.configs.control_agent import get_control_agent
from kaiwu_agent.utils.common import langraph_tool_to_schema
from kaiwu_agent.configs.commons import get_common_tools, get_orbbec_client


def get_visual_brain_agent(msg_client,
                           model,
                           simulation=False,
                           control_agent_version=CONTROL_VERSIONS.V1_WEBSOCKETS,
                           save_folder="assets/data",
                           message_appending=False,
                           stream=True):
    agent_executor = get_visual_agent(msg_client, model, simulation, control_agent_version, save_folder, stream)
    return VisualBrainAgent(
        msg_client=msg_client,
        agent_executor=agent_executor,
        accept_msg_when_busy=message_appending,
    )

def get_visual_agent(msg_client,
                     model,
                     simulation=False,
                     control_agent_version=CONTROL_VERSIONS.V1_WEBSOCKETS,
                     save_folder="assets/data",
                     stream=True):
    # 不传msg_client，中间结果暂不播报
    control_agent = get_control_agent(msg_client=msg_client, log_recv=False, version=control_agent_version)

    # chat_agent = get_chat_agent()

    scene_desc = scene_desc_wrapper(control_agent) 
    manipulation = manipulation_wrapper(control_agent) if control_agent_version == CONTROL_VERSIONS.V1_WEBSOCKETS \
        else robotic_arm_operation_wrapper(control_agent)
    robotic_arm_operation_plan = robotic_arm_operation_plan_wrapper(control_agent)
    release_hand = release_hand_wrapper(control_agent)
    # look_up = look_up_wrapper(chat_agent)
    # input_from_terminal: 从命令行输入指令，还是从麦克风获取指令
    interact_with_user = interact_with_user_wrapper(msg_client, input_from_terminal=False , seg_len=80)

    def response():
        return GET_REAL_IMAGE_RESPONSE
    perception = perception_wrapper(response)
    scene_prompt = SCENE_PROMPT if not stream else SCENE_PROMPT_V2
    camera_client = None
    tools = [interact_with_user] if not stream else []
    if control_agent_version == CONTROL_VERSIONS.V1_WEBSOCKETS: # demo3
        tools.extend([scene_desc, manipulation, release_hand,
                *get_common_tools()])
    elif control_agent_version == CONTROL_VERSIONS.V2_FASTAPI: # franka
        camera_client = get_orbbec_client(simulation=simulation)
        tools.extend([robotic_arm_operation_plan, perception,
                 *get_common_tools()])
        scene_prompt += UR_INDUSTRY_SCENE_PROMPT
    else:
        raise NotImplementedError

    save_folder = os.path.join(save_folder, "visual_brain_agent") if save_folder else save_folder
    agent_executor = FunctionCallAgent(
        system_prompt=ROLE_PROMPT,
        model=model["model"],
        api_key=model["api_key"],
        api_base=model["base_url"],
        tool_choice='auto' if model["model"] in ["gpt-4o", 'o1', 'o1-mini'] else None,
        tools=tools,
        paser=["Action:"],
        camera_client=camera_client,
        stop=["\nObservation:", "Observation:"],
        save_folder=save_folder,
        function_to_schema=langraph_tool_to_schema,
        stream=stream,
    )
    if stream:
        agent_executor.add_reply_function(interact_with_user, INTERACT_FUNC_NAME)

    tools_decs = agent_executor.get_tools_list()
    TOOL_DESC = ""
    format_prompt = ""
    if model["model"] not in ["gpt-4o", 'o1', 'o1-mini']:
        TOOL_DESC = "\n\n###可用工具###\n{}".format(json.dumps(tools_decs, ensure_ascii=False))
        format_prompt = ACTION_ONLY_FUNCTION_CALL_PROMPT if not stream else REPLY_AND_ACTION_FUNCTION_CALL_PROMPT
    system_prompt = ROLE_PROMPT + TOOL_DESC + format_prompt + scene_prompt + WORLD_PROMPT
    agent_executor.set_system_prompt(system_prompt)
    return agent_executor
