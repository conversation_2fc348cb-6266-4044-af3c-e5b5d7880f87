from kaiwu_agent.tools.block_tools import lego_control_wrapper
from kaiwu_agent.agents import LegoControlAgent
from kaiwu_agent.configs.config import ROLE_PROMPT, WORLD_PROMPT_v1, \
    SCENE_PROMPT_V3
from kaiwu_agent.tools.block_tools import lego_desc_wrapper_v1
from kaiwu_agent.configs.commons import get_react_brain_agent, get_llm, get_common_tools
from kaiwu_agent.utils.orbbec_camera import OrbbecCameraClient
from kaiwu_agent.utils.lego.lego_descriptor_v2 import BlockImageProcessor
from kaiwu_agent.configs.config import REAL_IMAGE_FOLDER
from kaiwu_agent.utils.kaiwu_message import KaiwuMessageClient
from kaiwu_agent.utils.common import read_parameters
import logging
logger = logging.getLogger(__name__)

# xiaomi_4115
# CAMERA_SERVER_IP = "**************" 
# 人形机器人
# DET_SERVER_IP = "*************"

# niuman
# CAMERA_SERVER_IP = "***********"    
# DET_SERVER_IP = "*************"

# 局域网
CAMERA_SERVER_IP = "************"    
# DET_SERVER_IP = "*************"
DESCRIPTION_CAMERA_PORT = '4242'

# 百度云   
DET_SERVER_IP = "************5"

# 开物云积木app后端地址
# KAIWU_JIMU_SERVER = "http://**********:8080/kaiwu-admin/v1/api/chat/doSimpleResult"
# KAIWU_JIMU_SERVER = "http://**************:9090/v1/api/chat/doSimpleResult"
# KAIWU_JIMU_SERVER = "http://*************:9090/v1/api/chat/doSimpleResult"
# KAIWU_JIMU_SERVER = "http://************:9090/v1/api/chat/doSimpleResult"  # 开物现场
KAIWU_JIMU_SERVER = None

PARAMETER_FILE = "assets/lego/max1/parameters.json"
LOG_FILE = "assets/lego/max1/lego_desc_perf.txt"
# 提取颜色所用的字典列表
COLOR_MAP = ["blue", "red", "green", "yellow"]
BLOCK_MAP = {
        "small size blue block": "blue_1x1",
        "small size red block": "red_1x1",
        "medium size green block": "green_1x2",
        "medium size yellow block": "yellow_1x2",
        "large size blue block": "blue_1x3",
}
DESC_PROMPT = """
Each layer has 1 to 3 blocks.  
The blocks on each layer are described in the order from the left to the right of the picture.  
Only use the given blocks [one small size blue block, one small size red block, one medium size green block,  
two medium size yellow blocks, one large size blue block], and describe the blocks and their colors from bottom to top in the following format:  
eg:  
small size blue block. small size red block. medium size green block. large size blue block.  
Requirement: Only answer the content in the format, do not answer anything else.
"""

# 任务描述
TASK_DESC_PROMPT = """\n\n###任务描述###\
\n你的具体任务是和用户进行开放性聊天和搭建乐高积木。用户会在你面前搭建一份多层乐高积木样例，当用户让你把积木样例搭建复现出来时，你可以按照以下流程进行搭建：\
\n1. 先调用积木描述工具获取积木样例信息`block_info`，然后给用户描述总层数（根据`layer`计算），以及每层有几个积木（`layer`相同为同一层），以及积木分别是什么颜色`color`，注意积木位置`pos`和尺寸`shape`不用描述。\
\n2. 然后调用积木操作工具，逐层搭建`block_info`里的积木，注意严格按照`block_info`里的积木顺序逐层搭建，且每层搭建完成后立刻告知用户进度，不要等所有积木搭建完成才交互。\
\n3. 最后告知用户最终的搭建结果，成功或者失败，以及失败的原因，并邀请用户对结果进行评价。\
\n4. 要求：当用户要求重来，例如重新描述、重新感知、重新搭建时，你要重头开始重新调用工具，不要使用历史记忆。\
\n5. 要求：只能用你看到的积木样例中的乐高进行搭建，不支持搭建任意形状、颜色、插入位置的乐高积木。\
\n6. 要求：在得到用户明确的积木搭建或积木描述指令后再开始积木任务，可以意会，但如果是特别模糊的指令可以让用户确认。\
\n7. 要求：当用户要求`继续搭建`时，意味着要连续多次调用搭建工具搭建`block_info`里剩余的积木，不要只搭部分就结束退出，搭建过程中不需要用户确认。\
"""


def get_tools(msg_client, llm_model: dict, vlm_model: dict, simulation=False):
    # 不传msg_client，中间结果暂不播报
    control_agent = LegoControlAgent(
        port=9529,
        # 乐高ControlAgent服务
        # server_url = "http://*************:9528/v1/command",
        server_url = "http://************5:9528/v1/command",
        # 测试用：用tools/fake_control_server_v2.py部署的
        # server_url = "http://localhost:9528/v1/command",
        msg_client=msg_client,
        log_recv=True,
        result_timeout=240.0,
        connect_timeout=5.0
    )
    # 读取参数
    camera_params = read_parameters(PARAMETER_FILE)
    lego_processor = BlockImageProcessor(
        prompt_text=DESC_PROMPT, 
        det_url=f"http://{DET_SERVER_IP}:8091/segment_objects", 
        api_key=vlm_model["api_key"], 
        camera_params=camera_params,
        color_map=COLOR_MAP,
        block_map=BLOCK_MAP,
        gpt_url=vlm_model["base_url"], 
        model=vlm_model["model"], 
    )
    
    lego_control = lego_control_wrapper(control_agent)
    
    # 固定的图仿真，还是取实时图像
    camera_client = OrbbecCameraClient(
        simulation=simulation, 
        server_ip=CAMERA_SERVER_IP, 
        test_img_path="tests/block_tools/lego_input/000492_0000_5436816374_1733817934479988_Color_1280x800.png", 
        default_view='front', 
        default_port=DESCRIPTION_CAMERA_PORT,  
        width=1920,
        height=1080,
        save_folder=REAL_IMAGE_FOLDER,
        timeout=5  # 体外相机超时
    )
    
    lego_desc = lego_desc_wrapper_v1(
        lego_processor=lego_processor,
        camera_client=camera_client,
        control_agent=control_agent,
        result_root="./assets/lego/desc",
        add_time=True,  # 小心硬盘爆满
        log_file=LOG_FILE
    )
    
    tools = [lego_desc, lego_control, *get_common_tools()]
    return tools


def get_tg_brain_agent_react_lego(msg_client,
                                  llm_model: dict,
                                  vlm_model: dict,
                                  simulation=False,
                                  stream_send=False):
    kaiwu_msg_client = KaiwuMessageClient(url=KAIWU_JIMU_SERVER) \
        if KAIWU_JIMU_SERVER is not None else None
    
    # 源自mcp服务器的工具，和本地定义的工具混合在一起，通过function call调用
    # mcp_cfg = get_common_mcp_cfg()
    mcp_cfg = None
    
    tools = get_tools(msg_client=msg_client, 
                      llm_model=llm_model,
                      vlm_model=vlm_model,
                      simulation=simulation)
    
    format_prompt = ""
    prompt = ROLE_PROMPT + format_prompt  + TASK_DESC_PROMPT + SCENE_PROMPT_V3 + WORLD_PROMPT_v1
        
    llm = get_llm(llm_model)
    
    return get_react_brain_agent(
        msg_client=msg_client,
        prompt=prompt, 
        model=llm, 
        tools=tools,
        enable_memory=True, 
        parallel_tool_calls=True,
        kaiwu_msg_client=kaiwu_msg_client,
        mcp_cfg=mcp_cfg,
        accept_msg_when_busy=True,
        stream_send=stream_send   # 打开流式生成、流式发送去做TTS，提高首响
    )
