from kaiwu_agent.tools.langgraph_tools import \
    look_up_wrapper, interact_with_user_wrapper, \
    vlm_scene_desc_wrapper, robotic_arm_operation_wrapper, \
    robotic_arm_operation_plan_wrapper, release_hand_wrapper
from kaiwu_agent.tools.search_tools import google_search_wrapper, get_time, \
    get_weather_wrapper
from kaiwu_agent.configs.base import  PERCEPTION_VERSIONS
from kaiwu_agent.configs.control_agent import get_control_agent
from kaiwu_agent.configs.config import ROLE_PROMPT, WORLD_PROMPT, SCENE_PROMPT, \
    FUNCTION_CALL_PROMPT, TOOLS_PROMPT_V1, FRANKA_KITCHEN_SCENE_PROMPT
from kaiwu_agent.configs.commons import get_chat_agent, get_orbbec_client, get_react_brain_agent
from kaiwu_agent.agents import TGControlAgentV2
from kaiwu_agent.configs.config import CONTROL_AGENT_IP, CONTROL_AGENT_PORT
from kaiwu_agent.configs.config import TEST_IMAGE_PATH

    
def get_agent_react_v2_franka(msg_client,
                    model,
                    simulation=False,
                    perception_version=PERCEPTION_VERSIONS.V1_SCENE_DECS):
    # 不传msg_client，中间结果暂不播报
    control_agent = TGControlAgentV2(
        port=8080,
        server_url = f"http://{CONTROL_AGENT_IP}:{CONTROL_AGENT_PORT}/control_agent_task_command",
        msg_client=msg_client,
        wait_result=True,
        log_recv=False,
        connect_timeout=240.0,
        recv_timeout=240.0
    )

    chat_agent = get_chat_agent()

    camera_client = get_orbbec_client(simulation=simulation)
    scene_desc = vlm_scene_desc_wrapper(simulation=simulation,
                                        msg_client=msg_client,
                                        camera_client=camera_client,
                                        perception_version=perception_version,
                                        test_image_path=TEST_IMAGE_PATH)
    manipulation = robotic_arm_operation_wrapper(control_agent)
    robotic_arm_operation_plan = robotic_arm_operation_plan_wrapper(control_agent)
    release_hand = release_hand_wrapper(control_agent)
    look_up = look_up_wrapper(chat_agent)
    # input_from_terminal: 从命令行输入指令，还是从麦克风获取指令
    # 目前设置截断去播报，提高响应速度
    interact_with_user = interact_with_user_wrapper(msg_client, input_from_terminal=False , seg_len=80)
    google_search = google_search_wrapper()
    get_weather = get_weather_wrapper()

    tools = [scene_desc, interact_with_user, robotic_arm_operation_plan, look_up,
                google_search, get_time, get_weather]
    scene_prompt = SCENE_PROMPT + FRANKA_KITCHEN_SCENE_PROMPT
    
    # format_prompt = FUNCTION_CALL_PROMPT if model["model"] != "gpt-4o" else ""
    format_prompt = ""
    prompt = ROLE_PROMPT + format_prompt + TOOLS_PROMPT_V1 + scene_prompt + WORLD_PROMPT
    
    return get_react_brain_agent(
        msg_client=msg_client,
        prompt=prompt, 
        model=model, 
        tools=tools
    )
