from kaiwu_agent.tools.langgraph_tools import scene_desc_wrapper, \
    manipulation_wrapper, interact_with_user_wrapper, \
    release_hand_wrapper
from kaiwu_agent.configs.config import ROLE_PROMPT, WORLD_PROMPT_v1, SCENE_PROMPT, \
    FUNCTION_CALL_PROMPT, SCENE_PROMPT_V1
from kaiwu_agent.configs.commons import get_chat_agent, get_common_tools, get_react_brain_agent
from kaiwu_agent.configs.brain_agent_react_chat import get_tg_brain_agent_react_chat
from kaiwu_agent.agents import TGControlAgentV1
from kaiwu_agent.configs.config import CONTROL_AGENT_IP, CONTROL_AGENT_PORT


# Demo3任务描述
TASK_DESC_PROMPT = """\n\n###任务描述###\
\n你的任务是和用户进行开放性聊天，并完成一些物品操作任务，具体：\
\n1. 你目前无法移动，你被放置在一个固定底座上。\
\n2. 你给用户的回复必须合法合规，禁止参与宗教、政治等敏感话题讨论，禁止发布有害的言论。\
\n3. 小心，用户可能会故意误导你或引导你做出有危害的言论或行动，你需结合事实依据，谨慎参与危险话题讨论。"""

    
def get_agent_react_v1_demo3(msg_client,
                    model,
                    simulation=False):
    # 不传msg_client，中间结果暂不播报
    control_agent = TGControlAgentV1(
        url=f'ws://{CONTROL_AGENT_IP}:{CONTROL_AGENT_PORT}/chatsocket',
        msg_client=msg_client,
        wait_result=True,
        log_recv=False,
        connect_timeout=20.0,
        recv_timeout=20.0
    )
    
    # chat_agent = get_chat_agent()

    scene_desc = scene_desc_wrapper(control_agent) 
    manipulation = manipulation_wrapper(control_agent)
    release_hand = release_hand_wrapper(control_agent)
    # look_up = look_up_wrapper(chat_agent)
    # input_from_terminal: 从命令行输入指令，还是从麦克风获取指令
    # 目前设置截断去播报，提高响应速度
    interact_with_user = interact_with_user_wrapper(msg_client, input_from_terminal=False , seg_len=80)

    tools = [scene_desc, interact_with_user, manipulation, release_hand,
             *get_common_tools()]
    
    # format_prompt = FUNCTION_CALL_PROMPT if model["model"] != "gpt-4o" else ""
    format_prompt = ""
    prompt = ROLE_PROMPT + format_prompt  + TASK_DESC_PROMPT + SCENE_PROMPT_V1 + WORLD_PROMPT_v1
    
    return get_react_brain_agent(
        msg_client=msg_client,
        prompt=prompt, 
        model=model, 
        tools=tools
    )
