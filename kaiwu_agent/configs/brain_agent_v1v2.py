import pprint
from kaiwu_agent.agents import DifyChatAgent, ChatAgent, \
    TGBrainAgentV1, TGBrainAgentV2
from kaiwu_agent.configs.control_agent import get_control_agent


def get_tg_brain_agent_v1(msg_client):
    control_agent = get_control_agent(msg_client)
    return TGBrainAgentV1(msg_client=msg_client,
                          control_agent=control_agent)


def get_tg_brain_agent_v2(msg_client):
    control_agent = get_control_agent(msg_client)
    # 意图识别
    # intent2desc = {
    #     "greeting": "用户和机器人打招呼，机器人名字叫天工，发现天空、天宫也算，例如：你好天工、你好天空、你好天宫、天工天工、天空天空、天宫天宫",
    #     "scene_desc": "用户需要机器人对它看到东西进行描述，包括场景中的人、事物及发生的事件等。例如：你看到了什么？桌面上有什么？我手里拿的是什么？我穿的衣服是什么颜色？你面前有几个人？这个猫正在干什么？",
    #     "manipulation": "用户需要机器人对物品进行操作，例如：把苹果和西瓜放到篮子里、把桃子放到我手上、帮我拿一个苹果、给我递一个橙子、把网球抓起来、把它放到蓝色的盒子里",
    #     "release_hand": "用户需要机器人执行松手动作，例如：我抱稳了、我抓稳了、我已经抱稳了、拿稳了、已经抱紧了、你可以松手了、你可以松开了、你可以放开了、你可以放手了、放手吧、松手吧",
    #     "self_introduce": "用户需要机器人做自我介绍，包括机器人的名字、功能等，例如：请介绍一下你自己、做个自我介绍吧、你是谁、你的名字叫什么",
    #     "introduce_company": "用户需要机器人介绍公司，公司全称是北京人形机器人创新中心，例如：介绍一下公司、介绍一下创新中心、介绍一下北京人形机器人创新中心",
    #     "introduce_qiaohong": "用户需要介绍中科院乔红老师，例如：介绍一下乔红、介绍乔红院士",
    #     "other": "其它情况，即除以上意图以外的其它情况，例如：天气真好啊、介绍一下亦庄、你长得真好看、我长大了、123456"
    # }
    
    intent2desc = {
        # V1, 天工交互抓取demo
        # "greeting": "用户和机器人打招呼，机器人名字叫天工，发现天空、天宫也算，例如：你好天工、你好天空、你好天宫、天工天工、天空天空、天宫天宫",
        "scene_desc": "用户需要机器人描述当前它所看到的场景，需要调用机器人的眼睛去完成的任务，例如：你看到了什么、桌子上有什么、我手里拿的是什么？我穿的衣服是什么颜色？你面前有几个人？这个猫正在干什么？",
        # "scene_desc": "用户需要机器人对它看到东西进行描述，包括场景中的人、事物及发生的事件等。例如：你看到了什么？桌面上有什么？我手里拿的是什么？我穿的衣服是什么颜色？你面前有几个人？这个猫正在干什么？",
        "manipulation": "用户需要机器人对物品进行操作，例如：把苹果和西瓜放到篮子里、把桃子放到我手上、帮我拿一个苹果、给我递一个橙子、把网球抓起来、把它放到蓝色的盒子里",
        "release_hand": "用户需要机器人执行松手动作，例如：我抱稳了、我已经抱稳了、拿稳了、你可以松手了、你可以松开了、你可以放手了、把手松开",
        # "self_introduce": "用户需要机器人做自我介绍，包括机器人的名字、功能等，例如：请介绍一下你自己、做个自我介绍吧、你是谁、你的名字叫什么",
        # "introduce_company": "用户需要机器人介绍公司，公司全称是北京人形机器人创新中心，例如：介绍一下公司、介绍一下创新中心、介绍一下北京人形机器人创新中心",
        # "introduce_qiaohong": "用户需要介绍中科院乔红老师，例如：介绍一下乔红、介绍乔红院士",
        # "other": "其它情况，即除以上意图以外的其它情况，例如：天气真好啊、介绍一下亦庄、你长得真好看、我长大了、123456"
        
        "other": "其它情况，即除以上意图以外的其它情况，不需要用到机器人的身体去完成的任务，例如：天气真好啊、介绍一下北京、介绍一下亦庄、你长得真好看、我长大了、123456"
        
        # TODO: 待调，chat触发很少
        # "chat": "用户需要和机器人进行开放性聊天，不需要用到机器人的身体去完成的任务，例如：你好天工、介绍下自己、介绍下创新中心、现在几点、今天的NBA新闻、天气真好啊、你真厉害",
        # "other": "除以上意图以外的其它情况，用户不是在和机器人交互，机器人无需做出反应，通常是一些背景音或者是一些无规律的非自然语音，例如：阿萨扩大阿迪娜扩大、asasa发发发37阿达82asd"
        
        # V2，天轶移动抓取demo
        # "control": "用户需要机器人调用身体技能去完成控制型任务，例如需要调动机器人的眼睛、耳朵、手脚等，例如：你看到了什么、桌子上有什么、把苹果和西瓜放到篮子里、把苹果放到我手上、帮我拿一个苹果、把手松开、往前走一步",
        # "chat": "用户需要和机器人进行开放性聊天，此时不需要调动机器人的身体技能，例如：你好天工、介绍下自己、现在几点、、天气真好啊、你真厉害",
        # "other": "除以上意图以外的其它情况，此时机器人无需做出回复，通常是一些例如无规律的非自然语音，例如：阿萨扩大阿迪娜扩大、asasa发发发37阿达82asd"
    }
    control_intents = ["scene_desc", "manipulation", "release_hand"]
    chat_intents = ["other"]
    other_intents = []
    # chat_intents = ["chat"]
    # other_intents = ["other"]
    assert len(intent2desc) == len(control_intents) + len(chat_intents) + len(other_intents)
    
    prompt_template = """
    假设你是一个人形机器人，你的任务是在给定的意图列表中，判断用户指令所属意图。

    意图列表: {intent_str}

    要求：
    1. 输出意图的英文名称，即意图列表里的key
    2. 意图必须是列表中的一个
    3. 不要输出 "assistant: " 的前缀
    
    这是一些示例：
    user: 帮我拿苹果
    assistant: manipulation

    user: 你看到了什么呀天工
    assistant: scene_desc

    user: 今天天气什么样啊
    assistant: other

    现在开始!
    """
    # 注意：必须用pprint去换行，格式会影响识别准确率！！！！
    intent_str = pprint.pformat(intent2desc, indent=4)
    system_prompt = prompt_template.format(intent_str=intent_str)
    
    # VLLM provides OpenAI style chat service
    intent_agent = ChatAgent(
        system_prompt=system_prompt,
        model="Qwen2-7B-Instruct-GPTQ-Int8", 
        api_key="EMPTY", 
        api_base="http://*************:8000/v1",
        # 温度越小，随机性越小
        temperature=0.0,
        name="IntentAgent"
    )
    
    chat_agent = DifyChatAgent(
        # agent
        # token="app-tvRzN7ni8kcRLUKg51c8XA3M", 
        # rag
        token="app-YvNWFJzQyX9x6LyBqDtui6NW",
        url='http://*************/v1/chat-messages'
    )
    
    return TGBrainAgentV2(
        msg_client=msg_client,
        intent_agent=intent_agent,
        control_agent=control_agent,
        control_intents=control_intents,
        chat_agent=chat_agent,
        chat_intents=chat_intents,
        other_intents=other_intents
    )
