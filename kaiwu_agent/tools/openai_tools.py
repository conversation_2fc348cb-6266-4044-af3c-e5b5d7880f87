import re
import json
import asyncio
import requests
from datetime import datetime
from kaiwu_agent.configs.config import TEST_IMAGE_PATH
from kaiwu_agent.configs.base import PERCEPTION_VERSIONS
from algo_tools.messages.msg_client import BaseMsgClient
from kaiwu_agent.utils.orbbec_camera import OrbbecCameraClient
from kaiwu_agent.utils.common import image_caption, perception


def scene_desc_wrapper(control_agent):
    """环境描述"""
    def scene_desc(instruction: str):
        """环境描述工具：通过头部摄像头识别当前环境以及与任务相关所有物体。
        
        parameters:
        - instruction (str): 用户指令或执行目标。
        """
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result_dict = control_agent.infer(instruction, skill="scene_desc")
        
        return {"tool_succeed": result_dict["succeed"], "answer": result_dict["answer"]}

    return scene_desc


def vlm_scene_desc_wrapper(simulation=False,
                           msg_client: BaseMsgClient = None,
                           camera_client: OrbbecCameraClient = None,
                           perception_version=PERCEPTION_VERSIONS.V1_SCENE_DECS):
    """环境描述"""
    def scene_desc(instruction: str):
        """环境描述工具：通过头部摄像头识别当前环境以及与任务相关所有物体。
        
        parameters:
        - instruction (str): 用户指令或执行目标。
        """
        if msg_client:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            msg_client.send_message("正在进行环境感知。" )
        imagepath = camera_client.get_realtime_imagepath(view='front') if not simulation else TEST_IMAGE_PATH
        print("realtime image in path:", imagepath)
        result = image_caption(instruction, imagepath)   \
            if perception_version == PERCEPTION_VERSIONS.V1_SCENE_DECS else \
            perception(instruction, imagepath)
        return {"tool_name": "scene_desc", "tool_succeed": True, "answer": result}
    
    return scene_desc



def ask_user_wrapper(msg_client: BaseMsgClient, input_from_terminal=False):
    """询问用户"""
    def ask_user(question: str):
        """
        用户询问工具：通过语音向用户提出问题，然后等待并接收用户的回复。
        
        parameters:
        - question (str): 询问用户的具体问题。
        """
        # 调用 msg_client 获取最新信息
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        msg_client.send_message(question, task_status='AskUser')
        if input_from_terminal:
            response= input(question)
        else:
            message = msg_client.get_latest_msg()
            response = message["instruction"]

        return {"tool_succeed": True, "answer": response}

    return ask_user


def voice_play_wrapper(msg_client: BaseMsgClient, seg_len=100):
    """语音播报"""
    def voice_play(response: str):
        """
        语音播报工具：将内容通过语音喇叭播报给用户。
        
        parameter:
        - response (str): 向用户播报的自然语言内容。
        """
        # # 调用 msg_client 获取最新信息
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        msg_client.send_message(response, seg_len=seg_len)
        return {"tool_succeed": True, "answer": "语音播报成功"}

    return voice_play


def manipulation_wrapper(control_agent):
    """TGControlAgentV1对应的操作工具"""
    def manipulation(instruction: str):
        """
        使用机器人的机械臂执行物品抓取和放置任务。当抓取成功之后，需要调用ask_user工具询问用户如何放置。注意，在没有抓取成功之前不能放置。
        
        parameter
        - instruction(str): Json format English operation instruction like {"pick": <object>, "place": <target>}. \
                            <object> and <target> must be clear, explicitly detectable targets. \
                            The output should prioritize both pick and place. Example: {"pick": "red apple", "place": "user's hand"} {"pick": "banana"} place": "blue plate"}
        """
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result_dict = control_agent.infer(instruction, skill='manipulation')
        return {"tool_succeed": result_dict["succeed"], "answer": result_dict["answer"]}
    
    return manipulation


def release_hand_wrapper(control_agent):
    def release_hand():
        """
        让机器人把手松开。前面的操作可能导致手指出现故障，这个指令让手指打开并恢复到正常状态，例如'把手松开'。
        """
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result_dict = control_agent.infer('把手松开', skill='release_hand')
        return {"tool_succeed": result_dict["succeed"], "answer": result_dict["answer"]}
    
    return release_hand


def robotic_arm_operation_wrapper(control_agent):
    """TGControlAgentV2对应的操作工具"""
    def robotic_arm_operation(instruction: str):
        """
        使用机器人本体的机械臂执行操作型任务。

        parameters:
        - instruction(str): Generate only one simple operation instruction in English for a robotic arm to complete a specific task.\
                            A instruction should consist of:\
                            1. Action:\
                            Use action verbs like wipe and open to describe each step.\
                            Instructions must be clear, specific, and operable.\
                            2. Object Interaction:\
                            Clearly identify objects involved, such as bottle or door.\
                            Ensure interactions are logical and resemble typical human actions.\
                            3. Scene Environment: Optionally, provide a simple description of the environment (e.g., "in a room with a table in the center").\
                            4. Action Mode: Optionally specify if a particular action requires a certain orientation, such as using the left arm or both arms.\

                            Examples 1: Grip the bottle and twist the cap to open.\
                            Examples 2: Wipe the bottle with a cloth, moving from top to bottom.\
                            Examples 3: Place the bottle in the center of the table.
        """
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result_dict = control_agent.infer(instruction)
        return result_dict
    return robotic_arm_operation


def robotic_arm_operation_plan_wrapper(control_agent):
    """TGControlAgentV2对应的操作规划工具"""
    def robotic_arm_operation(task: str, plan:str):
        """
        根据环境感知信息，对操作臂任务进行任务拆解和规划。
        
        parameters:
        -task(str): A specific task for robotic arms to perform, such as toast, pour milk.
        -plan(str): Generate a complete plan in English for the task.\
                    Each step in the plan should include the following:\
                    1. Action: Use action verbs like wipe and open to describe each step， including pick up, place x on/near y, press, open, close, push x to x and pour x.\
                    2. Object Interaction: Clearly identify objects involved, such as bottle or door. Ensure interactions are logical and resemble typical human actions.\
                    3. Action Mode: Using the left arm or right arm, not supporting both arms.\
                    4. Location: Location or Destination of the object.\
                    Conditional constraints:\
                    1. When the task is to pour something, directly output 'pour something' as the plan.\
                    2. The picking up action must be followed by the placing action, because other actions require that there is nothing in the arm.\
                    Example plan for roasting corn with plate from plate rack using oven rack:\
                    Step 1: Pick up the gray plate from plate rack and place it on the left of the table using the left arm.\
                    Step 2: Pick up the corn from the basket and place it on the gary plate using the left arm.\
                    Step 3: Push the gary plate to the right of the table using the left arm.\
                    Step 4: Using the right arm to open the oven, and put the gray plate in the oven rack, then close the oven.\
                    Step 5: Using the right arm to open the oven, then pick up the gray plate from the oven rack and place it on the right side of the table, and close the oven.\
                    Example plan for getting tissue from drawer:\
                    Step 1: Using the right arm to open the drawer.\
                    Step 2: Pick up the tissue from the drawer using the right arm and place it near the gray plate.\
                    Step 3: Close the drawer using the right arm.\
                    Example plan for pouring water:\
                    Step 1: Pour water using both arms.
        """
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result_dict = control_agent.infer(plan, task)
        return result_dict
    return robotic_arm_operation


def look_up_wrapper(chat_agent):
    """开放聊天"""
    def look_up(instruction: str):
        """
        知识库检索工具, 可回答和公司相关的问题，例如公司的人员、产品、组织架构等，例如"介绍一下你们公司"。
        
        parameter:
        - instruction (str): 用户提出的问题或话题，工具将基于此在知识库中检索相关信息。
        """
        response = chat_agent.infer(instruction)
        return {"result": 'succeed', "answer": response["answer"]}
    
    return look_up


def google_search(instruction: str):
    """
    联网查询工具：用于进行联网查询，例如“上一任美国总统是谁”。

    parameter:
    - instruction (str): 在搜索引擎中输入的文本内容。
    """
    from langchain_community.utilities import SerpAPIWrapper

    # serpapi_api_key = "b76f1530f6c57dd07cd4fe5fd86b9ac9535b79137511265c4c5fa29897c87931"
    serpapi_api_key = "00484c51d4a94dde462a6e5eb971d797a0019a7a415901d9500a7231a61b6d51" # 备用api_key, 100 searches
    search = SerpAPIWrapper(serpapi_api_key=serpapi_api_key)
    answer = search.run(instruction)
    return {
        "tool_name": "google_search", 
        "tool_succeed": True,
        "answer": answer
    }


def get_weather(instruction: str):
    """
    即时天气查询工具,可以获取当前天气的详细信息, 如温度和湿度。
    
    parameters:
    - instruction (str): 城市英文名称和国家简写，如 London,GB。
    """
    from langchain_community.utilities import OpenWeatherMapAPIWrapper
    weather = OpenWeatherMapAPIWrapper()
    answer = weather.run(instruction)

    return {
        "tool_name": "get_weather", 
        "tool_succeed": True,
        "answer": answer
    }


def get_time():
    """
    用于获取当前时间
    """
    
    return {"tool_name": "get_time", "tool_succeed": True, "answer": datetime.now().strftime("%Y-%m-%d %H:%M:%S")}


def get_location():
    """
    用于通过IP地址获取当前所在位置。
    """
    def get_public_ip():
        """
        用于获取IP地址
        """
        response = requests.get('https://api.ipify.org?format=json')
        if response.status_code == 200:
            return response.json()['ip']
        else:
            return "无法获取公共 IP"

    ip_address = get_public_ip()

    api_key = "f34e3b3c987a4341b90ac68f7817bdd7"
    base_url = "https://api.ipgeolocation.io/ipgeo"
    url = f"{base_url}?apiKey={api_key}"

    if ip_address:
        url += f"&ip={ip_address}"
    
    # 发送请求
    response = requests.get(url)
    if response.status_code == 200:
        data = response.json()
        answer = (
                f"IP 地址: {data.get('ip')}, "
                f"大洲: {data.get('continent_name')}, "
                f"国家: {data.get('country_name')}, "
                f"省/州: {data.get('state_prov')}, "
                f"城市: {data.get('city')}, "
                f"纬度: {data.get('latitude')}, "
                f"经度: {data.get('longitude')}, "
                f"时区: {data.get('time_zone', {}).get('name')}, "
                f"货币: {data.get('currency', {}).get('name')}"
            )

        return {
            "tool_name": "get_response", 
            "tool_succeed": True,
            "answer": answer
        }