import os
import requests
from datetime import datetime
from langchain_core.tools import tool
from pydantic import BaseModel, Field
from kaiwu_agent.utils.env import get_return_if_stop
from kaiwu_agent.utils.registry import TOOLS
from kaiwu_agent.tools.base import ToolCreatorBase

class WebSearchInput(BaseModel):
    query: str = Field(description="用户提出的问题或者搜索关键词。")


class GetWeatherInput(BaseModel):
    query: str = Field(description="中文的区域名称")


@TOOLS.register_module()
class BoChaSearch(ToolCreatorBase):
    """博查AI搜索"""
    def __init__(self,
                 api_key: str = None,
                 api_url: str = None):
        if api_key is None:
            if os.environ.get("BOCHA_API_KEY") is None:
                raise ValueError("api_key is None and BOCHA_API_KEY is not set")
            api_key = os.environ["BOCHA_API_KEY"]
        if api_url is None:
            api_url = os.environ.get("BOCHA_API_URL", "https://api.bochaai.com/v1/web-search")
        
        self.api_key = api_key
        self.api_url = api_url

    def run(self, query: str, freshness="noLimit", summary=False, count=10, page=1) -> str:
        """调用博查AI搜索 API 并返回搜索结果"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        payload = {
            "query": query,
            "freshness": freshness,
            "summary": summary,
            "count": count,
            "page": page
        }

        try:
            response = requests.post(self.api_url, json=payload, headers=headers, timeout=10)
            response.raise_for_status()
            data = response.json()

            if data["code"] == 200 and "data" in data and "webPages" in data["data"]:
                results = data["data"]["webPages"]["value"]
                if results:
                    return "\n".join([f"{i+1}. {res['name']} - {res['summary']}" for i, res in enumerate(results[:count])])
                else:
                    return "❌ 未找到相关搜索结果"
            else:
                return f"🚨 API 返回错误: {data.get('msg', '未知错误')}"
        except requests.exceptions.RequestException as e:
            return f"🚨 API 请求失败: {e}"
    
    def make_tool(self):
    
        @tool("internet_search", args_schema=WebSearchInput, return_direct=False)
        def internet_search(query: str, freshness="noLimit", summary=False, count=10, page=1):
            """联网搜索工具：用于在博查AI平台搜索信息。"""
            # 收到退出指令则结束工具运行
            stop_return = get_return_if_stop()
            if stop_return is not None:
                return stop_return
            
            freshness = "noLimit"  # 可选："oneDay", "oneWeek", "oneMonth", "oneYear", "noLimit"
            summary = True  # 是否显示文本摘要
            count = 3  # 结果条数（1-10）
            page = 1  # 页码，默认 1
            answer = self.run(query, freshness, summary, count, page)
            return {
                "tool_succeed": True,
                "answer": answer
            }
            
        return internet_search


@TOOLS.register_module()
class HeFengWeather(ToolCreatorBase):
    """和风天气查询，支持中文查询"""
    def __init__(self,
                 api_key: str = None,
                 lookup_url: str = None,
                 api_url: str = None):
        
        if api_key is None:
            if os.environ.get("HEFENG_API_KEY") is None:
                raise ValueError("api_key is None and HEFENG_API_KEY is not set")
            api_key = os.environ["HEFENG_API_KEY"]
        if api_url is None:
            api_url = os.environ.get("HEFENG_API_URL", "https://devapi.qweather.com/v7/weather/now")
        if lookup_url is None:
            lookup_url = os.environ.get("HEFENG_LOOKUP_URL", "https://geoapi.qweather.com/v2/city/lookup")
        
        self.api_key = api_key
        self.api_url = api_url
        self.lookup_url = lookup_url
    
    def get_city_id(self, city_name: str) -> str:
        """根据城市名称查询 Location ID"""
        params = {
            "key": self.api_key,
            "location": city_name,
            "lang": "zh"
        }
        try:
            response = requests.get(self.lookup_url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()

            if "location" in data and len(data["location"]) > 0:
                return data["location"][0]["id"]  # 取第一个匹配的城市 ID
            else:
                return None
        except requests.exceptions.RequestException as e:
            return None

    def run(self, query: str) -> str:
        """先查城市 ID，再查天气信息"""
        city_id = self.get_city_id(query)
        if not city_id:
            return f"未找到城市 {query}，请检查输入的城市名称是否正确"
        
        params = {
            "key": self.api_key,
            "location": city_id,
            "lang": "zh",
            "unit": "m"
        }
        try:
            response = requests.get(self.api_url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            if "now" in data:
                weather = data["now"]
                return (
                    f"{query} 当前天气：{weather['text']} 温度 {weather['temp']}°C（体感 {weather['feelsLike']}°C）\n"
                    f"风向：{weather['windDir']}（{weather['windScale']}级） 风速 {weather['windSpeed']} km/h\n"
                    f"湿度：{weather['humidity']}% 降水量：{weather['precip']}mm\n"
                    f"气压：{weather['pressure']} hPa 能见度：{weather['vis']} km\n"
                    f"云量：{weather['cloud']}%  观测时间：{weather['obsTime']}"
                )
            else:
                return "未能获取天气信息，请检查输入的城市名称是否正确"
        except requests.exceptions.RequestException as e:
            return f"API 请求失败: {e}"
    
    def make_tool(self):
        
        @tool("get_weather", args_schema=GetWeatherInput, return_direct=False)
        def get_weather(query: str):
            """即时天气查询工具,可以获取当前天气的详细信息, 如温度和湿度"""
            answer = self.run(query)
            return {
                "tool_succeed": True,
                "answer": answer
            }
            
        return get_weather


class EmptyInput(BaseModel):
    # 用Qwen 72b llm如果没有明确输入参数，会报错（用gpt没问题）：
    # openai.BadRequestError: Error code: 400 - {'object': 'error', 'message': '1 validation error for ValidatorIterator\n0.function.arguments\n  Input should be a valid string [type=string_type, input_value=None, input_type=NoneType]\n
    pass


@TOOLS.register_module()
class GetTime(ToolCreatorBase):
    
    def make_tool(self):
        
        @tool("get_time")
        def get_time(self, input: EmptyInput):
            """用于获取当前时间"""
            return {"tool_succeed": True, "answer": f"现在时间是{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"}

        return get_time


class QueryInput(BaseModel):
    query: str = Field(description="检索语句或者关键词。")


@TOOLS.register_module()
class KnowledgeSearchV1(ToolCreatorBase):
    """知识库文档搜索。"""
    def __init__(self, doc_file: str, tool_desc: str = "知识库查询：从知识库文档中获取资料"):
        self.doc_file = doc_file
        self.tool_desc = tool_desc
        with open(doc_file, 'r') as file:
            self.knowledge_base = file.read()
        
    def make_tool(self):
        # """知识库检索工具: 可回答和公司相关的问题，例如公司的人员、产品、组织架构等，例如"介绍一下你们公司"。"""
        
        @tool("knowledge_search", description=self.tool_desc, args_schema=QueryInput, return_direct=False)
        def knowledge_search(query: str):
            # TODO: 目前把整个文档返回，以加速，后续再启用总结
            response = {"answer": self.knowledge_base} 
            # query_inst = DOC_SUMMARY_PROMPT.format(query=query, 
            #                                        knowledge_base=knowledge_base)
            # response = chat_agent.infer(query_inst)
            return {"tool_succeed": True, "answer": response["answer"]}

        return knowledge_search
