import traceback
import async<PERSON>
import re
import os
from langchain_core.tools import tool
from pydantic import BaseModel, Field
from algo_tools.messages.msg_client import BaseMsgClient, FastAPIInstructListener
from kaiwu_agent.utils.common import image_caption, perception, get_video_frame, correct_instruction
from kaiwu_agent.utils.orbbec_camera import OrbbecCameraClient
from kaiwu_agent.agents.chat_agent import DifyChatAgent, OpenAIStyleChatAgent
from kaiwu_agent.agents.control_agent import TGControlAgentV1, TGControlAgentV2
from kaiwu_agent.configs.config import DOC_SUMMARY_PROMPT, RECV_MUSIC_PATH
from kaiwu_agent.configs.base import PERCEPTION_VERSIONS
from kaiwu_agent.utils.voice import get_latest_instruct
import logging
logger = logging.getLogger()
INTERACT_FUNC_NAME = "interact_with_user"


FRANKA_KITCHEN_OPERATION_PLAN_PROMPT = """Generate a complete plan in English for the task.
Each step in the plan should include the following:
1. Action: Actions including pick up, place x on/near y, press, open, close, push x to x and pour x.
2. Object Interaction: Clearly identify objects involved, such as bottle or door. Ensure interactions are logical and resemble typical human actions.
3. Action Mode: Using the left arm or right arm, not supporting both arms.
4. Location: Location or Destination of the object.
Conditional constraints:
1. When the task is to pour something, directly output 'pour something' as the plan.
2. The picking up action must be followed by the placing action, because other actions require that there is nothing in the arm.
3. All actions can only be used to operate on a single object.
Example plan for roasting corn with plate from plate rack using oven rack:
Step 1: Pick up the gray plate from plate rack and place it on the left of the table using the left arm.
Step 2: Pick up the corn from the basket and place it on the gary plate using the left arm.
Step 3: Push the gary plate to the right of the table using the left arm.
Step 4: Using the right arm to open the oven, and put the gray plate in the oven rack, then close the oven.
Step 5: Using the right arm to open the oven, then pick up the gray plate from the oven rack and place it on the right side of the table, and close the oven.
Example plan for getting tissue from drawer:
Step 1: Using the right arm to open the drawer.
Step 2: Pick up the tissue from the drawer using the right arm and place it near the gray plate.
Step 3: Close the drawer using the right arm.
Example plan for pouring water:
Step 1: Pour water using both arms."""


UR_INDUSTRY_OPERATION_PLAN_PROMPT = """Generate a complete plan in English for the task.
### Each step in the plan should include the following:
1. Action: Actions including pick up, place x on/near y, press and sort.
2. Object Interaction: Clearly identify objects involved, such as bottle or door. Ensure interactions are logical and resemble typical human actions.
3. Action Mode: Using the left arm or right arm or both arms.
4. Location: Location or Destination of the object.
### Conditional constraints:
1. When the task is to sort something, directly output 'sort something using both arms' as the plan.
2. The picking up action must be followed by the placing action, because other actions require that there is nothing in the arm.
3. In addition to the "sort" action, the "pick up", "place" and "press" actions can only be used to operate on a single object.
### Positive Example
#### plan for picking up the corn and place on the gary plate:
Step 1: Pick up the gray plate from plate rack and place it on the left of the table using the left arm.
Step 2: Pick up the corn from the basket and place it on the gary plate using the left arm.
#### plan for sorting the items:
Step 1: sort the items using both arms.
#### Negative Example
#### plan for sorting the items:
Step 1: pick up the remaining items using both arms."""


FRANKA_KITCHEN_OPERATION_PLAN_PROMPT = """Generate a complete plan in English for the task.
Each step in the plan should include the following:
1. Action: Actions including pick up, place x on/near y, press, open, close, push x to x and pour x.
2. Object Interaction: Clearly identify objects involved, such as bottle or door. Ensure interactions are logical and resemble typical human actions.
3. Action Mode: Using the left arm or right arm, not supporting both arms.
4. Location: Location or Destination of the object.
Conditional constraints:
1. When the task is to pour something, directly output 'pour something' as the plan.
2. The picking up action must be followed by the placing action, because other actions require that there is nothing in the arm.
3. All actions can only be used to operate on a single object.
Example plan for roasting corn with plate from plate rack using oven rack:
Step 1: Pick up the gray plate from plate rack and place it on the left of the table using the left arm.
Step 2: Pick up the corn from the basket and place it on the gary plate using the left arm.
Step 3: Push the gary plate to the right of the table using the left arm.
Step 4: Using the right arm to open the oven, and put the gray plate in the oven rack, then close the oven.
Step 5: Using the right arm to open the oven, then pick up the gray plate from the oven rack and place it on the right side of the table, and close the oven.
Example plan for getting tissue from drawer:
Step 1: Using the right arm to open the drawer.
Step 2: Pick up the tissue from the drawer using the right arm and place it near the gray plate.
Step 3: Close the drawer using the right arm.
Example plan for pouring water:
Step 1: Pour water using both arms."""


UR_INDUSTRY_OPERATION_PLAN_PROMPT = """Generate a complete plan in English for the task.
### Each step in the plan should include the following:
1. Action: Actions including pick up, place x on/near y, press and sort.
2. Object Interaction: Clearly identify objects involved, such as bottle or door. Ensure interactions are logical and resemble typical human actions.
3. Action Mode: Using the left arm or right arm or both arms.
4. Location: Location or Destination of the object.
### Conditional constraints:
1. When the task is to sort something, directly output 'sort something using both arms' as the plan.
2. The picking up action must be followed by the placing action, because other actions require that there is nothing in the arm.
3. In addition to the "sort" action, the "pick up", "place" and "press" actions can only be used to operate on a single object.
### Positive Example
#### plan for picking up the corn and place on the gary plate:
Step 1: Pick up the gray plate from plate rack and place it on the left of the table using the left arm.
Step 2: Pick up the corn from the basket and place it on the gary plate using the left arm.
#### plan for sorting the items:
Step 1: sort the items using both arms.
#### Negative Example
#### plan for sorting the items:
Step 1: pick up the remaining items using both arms."""


class SceneDescInput(BaseModel):
    instruction: str = Field(description="需要获取的视觉信息。")


class InteractWithUserInput(BaseModel):
    message: str = Field(description="要传达给用户的消息。")
    wait: bool = Field(description="是否等待用户回复，若需要用户回复，则为True。")


class AskUserInput(BaseModel):
    question: str = Field(description="询问问题。")


class VoicePlayInput(BaseModel):
    response: str = Field(description="播报文本内容。文本内容必须是口语化的句子，必须去掉分段符号例如`\\n`、`-`、`*`、`#`、`1. 2. 3.`等。")


class ManipulationInput(BaseModel):
    instruction: str = Field(description="""Json format English operation instruction like {"pick": <object>, "place": <target>}. \
<object> and <target> must be clear, explicitly detectable targets in English without using directional terms. \
The output should prioritize both pick and place. Example: {"pick": "red apple", "place": "user's hand"} {"pick": "banana"} place": "blue plate"}""")


class RoboticArmOperationInput(BaseModel):
    instruction: str = Field(description="""Generate only one simple operation instruction in the same language as the user for a robotic arm to complete a specific task.
A instruction should consist of:
1. Action:
Use action verbs like wipe and open to describe each step.
Instructions must be clear, specific, and operable.
2. Object Interaction:
Clearly identify objects involved, such as bottle or door.
Ensure interactions are logical and resemble typical human actions.
3. Scene Environment: Optionally, provide a simple description of the environment (e.g., "in a room with a table in the center").
4. Action Mode: Optionally specify if a particular action requires a certain orientation, such as using the left arm or both arms.

Examples 1: Grip the bottle and twist the cap to open.
Examples 2: Wipe the bottle with a cloth, moving from top to bottom.
Examples 3: Place the bottle in the center of the table.""")


class RoboticArmOperationWithActionModeInput(BaseModel):
    instruction: str = Field(description="""Generate only one simple operation instruction in English for robotic arms to complete a specific task.
A instruction should consist of:
1. Action: Use action verbs like wipe and open to describe each step.
2. Object Interaction: Clearly identify objects involved, such as bottle or door. Ensure interactions are logical and resemble typical human actions.
3. Action Mode: using the left arm or right arm, not supporting both arms.
Examples 1: Grip the bottle using the left arm.
Examples 2: Wipe the bottle with a cloth, moving from top to bottom with the left arm.
Examples 3: Place the bottle in the center of the table using the right arm.""")


class RoboticArmOperationPlanInput(BaseModel):
    task: str = Field(description="""A specific task for robotic arms to perform in the same language as the user, such as toast, pour milk, 拿苹果.""")
    # !TODO: 后续增加功能以实现根据开物云的角色定义和技能设置选择工具和填充操作工具Prompt
    plan: str = Field(description=UR_INDUSTRY_OPERATION_PLAN_PROMPT)


class LookUpInput(BaseModel):
    # response: str = Field(description="检索前向用户反馈你将要检索什么信息，输出语言需与用户使用语言一致。")
    query: str = Field(description="检索语句或者关键词。")

def perception_wrapper(func):
    """环境感知"""
    @tool("perception", return_direct=False)
    def perception():
        """环境感知工具：获取图像以感知和理解环境。"""
        return func()
    return perception

def scene_desc_wrapper(control_agent: TGControlAgentV1):
    """环境描述"""
    @tool("scene_desc", args_schema=SceneDescInput, return_direct=False)
    def scene_desc(instruction: str):
        """环境描述工具：通过头部摄像头识别当前环境以及与任务相关所有物体。"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result_dict = control_agent.infer(instruction, skill="scene_desc")
        return {"tool_succeed": result_dict["succeed"], "answer": result_dict["answer"]}

    return scene_desc


def vlm_scene_desc_wrapper(simulation=False,
                           msg_client: BaseMsgClient = None,
                           camera_client: OrbbecCameraClient = None,
                           perception_version=PERCEPTION_VERSIONS.V1_SCENE_DECS,
                           test_image_path=None):
    """环境描述"""
    @tool("scene_desc", args_schema=SceneDescInput, return_direct=False)
    def scene_desc(instruction: str):
        """环境描述工具：通过头部摄像头识别当前环境以及与任务相关所有物体。"""
        if msg_client:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            msg_client.send_message("正在进行环境感知。" )
        imagepath = camera_client.get_realtime_imagepath(view='front') if not simulation else test_image_path
        logger.info(f"image path: {imagepath}")
        result = image_caption(instruction, imagepath)   \
            if perception_version == PERCEPTION_VERSIONS.V1_SCENE_DECS else \
            perception(instruction, imagepath)
        return {"tool_succeed": True, "answer": result}
    
    return scene_desc


def ask_user_wrapper(msg_client: BaseMsgClient, input_from_terminal=False, timeout=120):
    """询问用户"""
    @tool("ask_user", args_schema=AskUserInput, return_direct=False)
    def ask_user(question: str):
        """用户询问工具：通过语音向用户提出问题，然后等待并接收用户的回复。"""
        # 调用 msg_client 获取最新信息
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        msg_client.send_message(question, task_status='AskUser')
        if input_from_terminal:
            response= input(question)
        else:
            response = get_latest_instruct(msg_client, timeout)
        
        return {"tool_succeed": True, "answer": response}

    return ask_user


def interact_with_user_wrapper(msg_client: BaseMsgClient, input_from_terminal=False, seg_len=100, timeout=120, kaiwu_msg_client = None):
    """用户交互"""
    @tool(INTERACT_FUNC_NAME, args_schema=InteractWithUserInput, return_direct=False)
    def interact_with_user(message: str, wait: bool = False):
        """用户交互工具：将消息通过语音喇叭播报给用户，并可选择性地等待用户回复。"""
        try:
            # TODO: 先手动去掉分隔符，让TTS更自然
            message = re.sub(r'[\n\t*#]', ' ', message)
            
            # 给开物云积木app发消息
            if kaiwu_msg_client is not None:
                kaiwu_msg_client.send(msg_dict={"role": "robot", "message": message})
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            msg_client.send_message(message, seg_len=seg_len, task_status='AskUser')
            if wait:
                logger.info(f"******* 正在等待用户回复，请说话: ")
                if input_from_terminal:
                    response= input(message)
                else:
                    response = get_latest_instruct(msg_client, timeout)
                
                # TODO: 临时映射
                response = correct_instruction(response)
                
                # 收到指令的提示音
                if type(msg_client) != FastAPIInstructListener:
                    msg_client.send_message(RECV_MUSIC_PATH, text_type="file", cmd="stop")
                # 给开物云积木app发消息
                if kaiwu_msg_client is not None:
                    kaiwu_msg_client.send(msg_dict={"role": "user", "message": response})

                return {"tool_succeed": True, "answer": response}
            return {"tool_succeed": True, "answer": "语音播报成功"}
        except:
            logger.error(traceback.format_exc())
            return {"tool_succeed": False, "answer": "执行中断"}

    return interact_with_user


def voice_play_wrapper(msg_client: BaseMsgClient, seg_len=100):
    """语音播报"""
    @tool("voice_play", args_schema=VoicePlayInput, return_direct=False)
    def voice_play(response: str):
        """语音播报工具：将内容通过语音喇叭播报给用户。"""
        # # 调用 msg_client 获取最新信息
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        msg_client.send_message(response, seg_len=seg_len)
        return {"tool_succeed": True, "answer": "语音播报成功"}

    return voice_play


def manipulation_wrapper(control_agent: TGControlAgentV1):
    """TGControlAgentV1对应的操作工具"""
    @tool("manipulation", args_schema=ManipulationInput, return_direct=False)
    def manipulation(instruction: str):
        """使用机器人的机械臂执行物品抓取和放置任务。当抓取成功之后，需要调用工具询问用户如何放置。注意，在没有抓取成功之前不能放置。"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result_dict = control_agent.infer(instruction, skill='manipulation')
        return {"tool_succeed": result_dict["succeed"], "answer": result_dict["answer"]}
    
    return manipulation


def release_hand_wrapper(control_agent: TGControlAgentV1):
    @tool("release_hand", return_direct=False)
    def release_hand():
        """让机器人把手松开。前面的操作可能导致手指出现故障，这个指令让手指打开并恢复到正常状态，例如'把手松开'。
        """
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        # if msg_client is not None:
        #     msg_client.send_message("好的，我将把手松开。")
        result_dict = control_agent.infer('把手松开', skill='release_hand')
        return {"tool_succeed": result_dict["succeed"], "answer": result_dict["answer"]}
    
    return release_hand


def robotic_arm_operation_wrapper(control_agent: TGControlAgentV2):
    """TGControlAgentV2对应的操作工具"""
    @tool("robotic_arm_operation", args_schema=RoboticArmOperationInput, return_direct=False)
    def robotic_arm_operation(instruction: str):
        """使用机器人本体的机械臂执行操作型任务。"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result_dict = control_agent.infer(instruction)
        return result_dict
    return robotic_arm_operation


def robotic_arm_operation_plan_wrapper(control_agent: TGControlAgentV2):
    """TGControlAgentV2对应的操作规划工具"""
    @tool("robotic_arm_operation", args_schema=RoboticArmOperationPlanInput, return_direct=False)
    def robotic_arm_operation(task: str, plan:str):
        """根据环境感知信息，对操作臂任务进行任务拆解和规划。"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result_dict = control_agent.infer(plan, task)
        return result_dict
    return robotic_arm_operation


def look_up_wrapper(chat_agent: DifyChatAgent):
    """知识库问答"""
    @tool("look_up", args_schema=LookUpInput, return_direct=False)
    def look_up(query: str):
        """知识库检索工具: 可回答和公司相关的问题，例如公司的人员、产品、组织架构等，例如"介绍一下你们公司"。"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        response = chat_agent.infer(query)
        return {"tool_succeed": True, "answer": response["answer"]}
    
    return look_up


def look_up_wrapper_v1(chat_agent: OpenAIStyleChatAgent, knowledge_base: str):
    """知识库问答V1，直接用LLM从一大段文字中总结出问题所需答案。"""
    @tool("look_up", args_schema=LookUpInput, return_direct=False)
    def look_up(query: str):
        """知识库检索工具: 可回答和公司相关的问题，例如公司的人员、产品、组织架构等，例如"介绍一下你们公司"。"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        # TODO: 目前把整个文档返回，以加速，后续再启用总结
        response = {"answer": knowledge_base} 
        # query_inst = DOC_SUMMARY_PROMPT.format(query=query, 
        #                                        knowledge_base=knowledge_base)
        # response = chat_agent.infer(query_inst)
        return {"tool_succeed": True, "answer": response["answer"]}
    
    return look_up
