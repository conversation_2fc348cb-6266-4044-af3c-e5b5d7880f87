import asyncio
import websockets
import json
import signal
import logging
from kaiwu_agent.utils.websocket.socket_server import shutdown_server

logger = logging.getLogger()
# TODO:Move the repeated part of LocalVoiceSocketServer and robot_voice VoiceSocketServer to algo_tools
class LocalVoiceSocketServer:
    def __init__(self, 
                 host="localhost", 
                 port=8765, 
                 voice_path = "/voice",
                 tts_path = "/ttsplay"):
        self.host = host
        self.port = port
        self.voice_path = voice_path
        self.tts_path = tts_path

        self._voice_clients = set()
        self._running = True
        self.message_out = []

    def reset(self):
        self._voice_clients = set()
        self._running = True

    def get_message_out(self):
        msg_out = self.message_out
        self.message_out = []
        return msg_out

    async def handler(self, websocket):
        try:
            if isinstance(websocket, websockets.WebSocketServerProtocol):
                path = websocket.path
            else:
                path = websocket.request.path
            
            if path == self.voice_path:
                await self.voice_handler(websocket)
            elif path == self.tts_path:
                await self.tts_handler(websocket)
            else:
                logger.info(f"Unknown request path:  {path}, from client: {websocket.remote_address}")
                return
        except Exception as e:
            logger.info(f"处理请求时发生异常: {e}")
            await websocket.close(code=1011, reason=str(e))

    async def voice_handler(self, websocket):
        self._voice_clients.add(websocket)
        logger.info(f"语音客户端接入: {websocket.remote_address}")
        # 保持连接
        # await self.recv_msg()
        # self._voice_clients.remove(websocket)
        
        try:
            while self._running:
                # 保持连接
                response = await websocket.recv()
                logger.info(response)
        except (websockets.ConnectionClosed, websockets.exceptions.ConnectionClosedOK):
            logger.info(f"语音客户端关闭: {websocket.remote_address}")
        except KeyboardInterrupt:
            pass
        finally:
            self._voice_clients.remove(websocket)

    async def tts_handler(self, websocket):
        # assert self.tts_player is not None
        # 注意，此时客户端不要主动关闭连接（如函数退出释放websocket对象），而应该是当前服务端处理完成后主动断开
        message = await self.recv_msg(websocket)

        if message:
            msg_dict = json.loads(message)   
            text = msg_dict["text"]
            cmd = msg_dict.get("cmd", None)
            logger.info(f"tts: {text}, cmd: {cmd}")
            msg_out = {'tts':text,'cmd':cmd}
            self.message_out.append(msg_out)

    async def recv_msg(self, websocket, timeout=None):
        try:
            message = await asyncio.wait_for(websocket.recv(), timeout=timeout)
            return message
        except (asyncio.exceptions.TimeoutError, websockets.exceptions.ConnectionClosedError):
            logger.info(f'recv_msg: 警告, 接收语音客户端消息超时或网络断连: {websocket.remote_address}')
            return None
        except websockets.exceptions.ConnectionClosedOK:
            logger.info(f'recv_msg: 提示, 语音客户端主动断开连接，服务端正常退出: {websocket.remote_address}')
            return None
        # except Exception as e:
        #     printerr(f"recv_msg: Error: {e}")
        #     return None

    async def send_msg(self, websocket, message):
        err_msg = f"send_msg: 语音客户端连接已关闭，无法发送消息: {websocket.remote_address}"
        try:
            await websocket.send(message)
        except websockets.exceptions.ConnectionClosedOK:
            logger.info(err_msg)
        except websockets.exceptions.ConnectionClosedError:
            logger.info(err_msg)
        # except Exception as e:
        #     printerr(f"recv_msg: Error: {e}，无法发送消息: {websocket.remote_address}")
        #     return None

    async def _broadcast_voice(self, message):
        if self._running and self._voice_clients:
            logger.info(f"Broadcasting voice message: {message}")
            logger.info(f"nbr voice client:{len(self._voice_clients)}")
            # await asyncio.wait([
            #     self.send_msg(client, message) for client in self._voice_clients
            # ])
            await asyncio.gather(*[
                self.send_msg(client, message) for client in self._voice_clients
            ])

    def broadcast_voice(self, message):
        # 确保子线程有自己独立的事件，和主线程共用可能会产生阻塞
        loop = asyncio.new_event_loop()
        loop.run_until_complete(self._broadcast_voice(message))

    async def _start_server(self):
        self.reset()
        # voice_server = VoiceServer()
        # 使用 websockets.serve 来创建 WebSocket 服务器
        start_server = websockets.serve(self.handler, 
                                        host=self.host, 
                                        port=self.port)

        # 启动服务器并将其注册到事件循环中
        async with start_server:
            # print("Server is running on ws://localhost:8765")
            logger.info(
                f"语音服务端已启动, Access by: \n1. 语音消息订阅地址: ws://{self.host}:{self.port}{self.voice_path}"\
                f"\n2. TTS语音合成并播报地址: ws://{self.host}:{self.port}{self.tts_path}")
            await asyncio.Future()  # 这行代码让服务器持续运行    

    def start(self):
        # 确保线程有自己独立的事件，和主线程共用可能会产生阻塞
        loop = asyncio.new_event_loop()
        # asyncio.set_event_loop(loop)
        
        # 获取当前事件循环
        # loop = asyncio.get_event_loop()
        
        # 注册 `Ctrl+C` 信号处理
        for signame in ('SIGINT', 'SIGTERM'):
            loop.add_signal_handler(getattr(signal, signame), shutdown_server)

        try:
            # 运行主程序
            loop.run_until_complete(self._start_server())
        except asyncio.CancelledError:
            pass
        finally:
            # 优雅地关闭事件循环
            loop.close()
            logger.info("Server closed.") 

    def stop(self):
        self._running = False
