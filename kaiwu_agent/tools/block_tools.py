import cv2
import asyncio
import os.path as osp
import os
import zerorpc
from typing import List
from langchain_core.tools import tool
from algo_tools.messages.msg_client import BaseMsgClient
from kaiwu_agent.utils.orbbec_camera import request_data
from kaiwu_agent.utils.lego.lego_descriptor import LegoDescriptor
from kaiwu_agent.utils.lego.lego_descriptor_v2 import BlockImageProcessor
from kaiwu_agent.agents.lego_control_agent import LegoControlAgent
from kaiwu_agent.utils.common import get_uniq_datetime_str, print_top_slowest_functions
from kaiwu_agent.agents.lego_control_agent import LegoInfo, LegoExaminerInfo, LegoPickPlace
from kaiwu_agent.utils.orbbec_camera import OrbbecCameraClient
from kaiwu_agent.utils.env import get_return_if_stop
import traceback
import logging
logger = logging.getLogger(__name__)


def get_realtime_image(camera_client, result_root, add_time):
    time_suffix = get_uniq_datetime_str() if add_time else "latest"
    result_dir = osp.join(result_root, time_suffix)
    os.makedirs(result_dir, exist_ok=True)
    
    image_path = camera_client.get_realtime_imagepath(add_time=False)    
    image = cv2.imread(image_path)
    
    # 水平沿宽度翻转，便于从体外相机视角转成头部相机视角坐标
    image = cv2.flip(image, 1)
    _name = osp.basename(image_path)
    image_path = osp.join(result_dir, f"flip_{_name}") 
    cv2.imwrite(image_path, image)
    logger.info(f"filp image: {image_path}")
    return image_path, result_dir


def lego_desc_wrapper(lego_descriptor: LegoDescriptor,
                      camera_client: OrbbecCameraClient,
                      msg_client: BaseMsgClient = None):
    """封装乐高积木描述工具。"""
    
    @tool("lego_desc", return_direct=False)
    def lego_desc():
        """乐高积木描述工具：描述图像中的乐高积木信息，包括有几层积木，每层积木的颜色color、形状shape (height, width)、及坐标(x, y)等。"""
        succeed = False
        try:
            image_path = camera_client.get_realtime_imagepath(add_time=False)
            image = cv2.imread(image_path)
            basename = osp.basename(image_path)
            # TODO: 水平沿宽度翻转，便于从体外相机视角转成头部相机视角坐标
            image = cv2.flip(image, 1)
            
            response = lego_descriptor.process_image(image, basename=basename)
            succeed = True
        except Exception as e:
            logger.error(traceback.format_exc())
            response = str(e)

        return {"tool_succeed": succeed, "answer": response}
    
    return lego_desc


def lego_desc_wrapper_v1(lego_processor: BlockImageProcessor,
                         camera_client: OrbbecCameraClient,
                         control_agent: LegoControlAgent = None,
                         result_root: str = "./assets/lego/desc",
                         add_time: bool = False,
                         log_file: str = None):
    """封装乐高积木描述工具。"""
    
    @tool("lego_desc", return_direct=False)
    def lego_desc():
        """乐高积木描述工具：描述图像中的乐高积木信息，包括有几层积木，每层积木的颜色color、形状shape (height, width)、及坐标(x, y)等。"""
        # 收到退出指令则结束工具运行
        stop_return = get_return_if_stop()
        if stop_return is not None:
            return stop_return
                
        succeed = False
        try:
            if control_agent:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                # 发送转头看示例积木的指令
                try:
                    control_agent.infer(
                        pick_place=None,
                        scan_type="all",   # leone.liu define it
                        wait_result=False  # TODO: 设置True会导致阻塞
                    )
                except Exception as err:
                    logger.warning("转头失败，忽略")
                    pass
            
            # 获取图像
            image_path, result_dir = get_realtime_image(
                camera_client, result_root=result_root, add_time=add_time)

            response = lego_processor.execute(        
                initial_image_path=image_path,
                result_dir=result_dir,
            )
            print_top_slowest_functions(10, save_txt=log_file)
            
            succeed = True
        except (zerorpc.exceptions.TimeoutExpired, zerorpc.exceptions.LostRemote) as e:
            response = "错误：无法获取相机图像，请检查相机服务"
            logger.error(f"{response}: {e}")
        except Exception as e:
            logger.error(traceback.format_exc())
            response = str(e)

        return {"tool_succeed": succeed, "answer": response}
    
    return lego_desc


def lego_examiner_wrapper(lego_desc: BlockImageProcessor,
                          lego_exam: BlockImageProcessor,
                          camera_client: OrbbecCameraClient,
                          result_root: str = "./assets/lego/examiner",
                          add_time: bool = False):
    """封装乐高积木逐块搭建检查工具。"""
    
    @tool("lego_examiner", args_schema=LegoExaminerInfo, return_direct=False)
    def lego_examiner(index):
        """乐高积木逐块搭建检查工具：检查当前搭建的积木，和描述工具给定的积木搭建要求是否匹配，包括每块积木所在的层数layer、颜色color、形状shape (height, width)、及坐标pos(x, y)。"""
        stop_return = get_return_if_stop()
        if stop_return is not None:
            return stop_return
        
        succeed = False
        try:
            # 获取图像
            image_path, result_dir = get_realtime_image(
                camera_client, result_root=result_root, add_time=add_time)

            response = lego_exam.block_examiner(        
                initial_image_path=image_path,
                result_dir=result_dir,
                lego_description=lego_desc.lego_description,
                index = index
            )    
            
            succeed = True
        except Exception as e:
            logger.error(traceback.format_exc())
            response = str(e)

        return {"tool_succeed": succeed, "answer": response}
    
    return lego_examiner


def lego_control_wrapper(control_agent: LegoControlAgent):
    """封装乐高积木操作工具。"""
    
    @tool("lego_control", args_schema=LegoPickPlace, return_direct=False)
    def lego_control(task_list: List[LegoInfo]):
        """乐高积木操作工具：使用机器人的机械臂抓起指定颜色、形状的积木，然后放置到指定位置，可依次操作多块积木。"""
        # 收到退出指令则结束工具运行
        stop_return = get_return_if_stop()
        if stop_return is not None:
            return stop_return
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result_dict = control_agent.infer(
            pick_place=task_list,
            wait_result=True
        )
        return result_dict
    
    return lego_control
