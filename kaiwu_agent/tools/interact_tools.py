from enum import Enum
import logging
import uuid
from algo_tools.messages.msg_client import BaseMsgClient
from langchain_core.tools import tool
from pydantic import BaseModel, Field
from kaiwu_agent.utils.registry import TOOLS
from kaiwu_agent.agents.brain_agent import BaseBrainAgent
from kaiwu_agent.tools.base import ToolCreatorBase

logger = logging.getLogger(__name__)

class BrainInput(BaseModel):
    instruction: str = Field(description="用户需要进行深度思考来解决的问题")
    intent: str = Field(description="用户想要这么做的意图。如果用户的意图是想要打断你正在做的事,请返回STOP_ALL。")


def brain_thinking_wrapper(brain_agent:BaseBrainAgent):
    # """深度思考"""
    @tool("brain_thinking", args_schema=BrainInput, return_direct=False)
    def brain_thinking(instruction:str, intent:str):
        """深度思考工具，除了微笑，挥手，播报，说话，聊天之外的动作和长序列以及感知环境"""
        result = brain_agent.infer(instruction, intent)
        return {"tool_succeed": True, "answer": result["answer"]}
    return brain_thinking

@tool("smile", return_direct=False)
def smile(): 
    """微笑"""
    print("Smiling")
    return {"tool_succeed": True, "answer": "smiling"}

@tool("wavehand", return_direct=False)
def wavehand():
    """挥手，打招呼或引起注意"""
    print("Waving hand")
    return {"tool_succeed": True, "answer": "waving hand"}


@TOOLS.register_module()
class AgentInteraction(ToolCreatorBase):
    """用于agent之间互相发送消息"""
    def __init__(self, 
                 agent_uuid: str, 
                 agent_mapper: dict, 
                 msg_client: BaseMsgClient):
        self.agent_uuid = agent_uuid
        self.agent_mapper = agent_mapper
        self.msg_client = msg_client

    def get_agent_id_by_alias(self, agent_alias: str):
        """通过alias获取agent ID"""
        for k, v in self.agent_mapper.items():
            if v["alias"] == agent_alias:
                return k
        
        return None

    def make_tool(self):
        role_names = [v["alias"] for v in self.agent_mapper.values()]

        # 务必注意 description, docstring, args_schema的优先级，三者是互斥关系，不要同时设置
        # description: Optional description for the tool.
        #     Precedence for the tool description value is as follows:
        #         - `description` argument
        #             (used even if docstring and/or `args_schema` are provided)
        #         - tool function docstring
        #             (used even if `args_schema` is provided)
        #         - `args_schema` description
        #             (used only if `description` / docstring are not provided)
        tool_desc = f"""Send a notification or instruction to a specified agent.
        
        Args:
            message (str): The content of the message to be sent.
            receiver (str): The name of the message receiver agent, should be one of {role_names}.

        Returns:
            dict: Dict of "state" and "msg", where "state" is "succeed" or "failed", and "msg" is the error message.
        """
        @tool(description=tool_desc, return_direct=False)
        async def agent_interact(message: str, receiver: str) -> str:
            agent_name = self.agent_mapper[self.agent_uuid]["name"]
            logger.info(f"sending message from `{agent_name}` to `{receiver}`: {message}")
            
            recv_agent_id = self.get_agent_id_by_alias(receiver)
            if recv_agent_id is None:
                return {"state": "failed", "msg": f"illegal receiver name, should be one of {role_names}"}
            
            try:
                await self.msg_client.send_message_once(
                    agent_name=agent_name,
                    message_id=uuid.uuid4().hex,  # @消息时使用新的message_id
                    agent_uuid=self.agent_uuid,
                    text=message, 
                    receiver=recv_agent_id
                )
            except Exception as e:
                error_msg = f"agent {agent_name} 发送消息失败: {e}"
                logger.error(error_msg, exc_info=True)
                return {"state": "failed", "msg": error_msg}
            
            return {"state": "succeed", "msg": "消息发送成功"}

        return agent_interact
