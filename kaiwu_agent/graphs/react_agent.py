from typing import Union, List
import pprint
from langgraph.prebuilt import create_react_agent
from langchain_core.language_models import BaseLanguageModel
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.checkpoint.memory import MemorySaver
from langchain.tools import BaseTool
from kaiwu_agent.graphs.base import GraphC<PERSON><PERSON><PERSON>
from kaiwu_agent.utils.registry import GRAPH_CREATORS, TOOLS, MODELS
from kaiwu_agent.tools.base import ToolCreatorBase
import logging
logger = logging.getLogger(__name__)


@GRAPH_CREATORS.register_module()
class ReactAgentCreator(GraphCreatorBase):
    def __init__(self, 
                 model: Union[dict, BaseLanguageModel], 
                 tools: List[Union[dict, ToolCreatorBase]], 
                 system_prompt: str, 
                 mcp_cfg: dict = None, 
                 parallel_tool_calls: bool = True,
                 enable_memory: bool = True):
        """
        Args:
            model: The language model to use
            tools: The tools to use
            system_prompt: The system prompt to use
            mcp_cfg: The configuration of the mcp client
            parallel_tool_calls: Whether to call multiple tools in parallel
            enable_memory: Whether to use memory saver to save the memory state
        """
        if not isinstance(model, BaseLanguageModel):
            model = MODELS.build_from_cfg(model)
        
        _lc_tools = []
        for t in tools:
            if isinstance(t, ToolCreatorBase):
                lct = t.make_tool()
            elif isinstance(t, BaseTool):
                lct = t
            elif isinstance(t, dict):
                tool_creator = TOOLS.build_from_cfg(t)
                lct = tool_creator.make_tool()
            else:
                raise ValueError(f"tool {t} is not a ToolCreatorBase or BaseTool or a dict, got {type(t)}")
            _lc_tools.append(lct)
        
        self.model = model
        self.tools = tools
        self.system_prompt = system_prompt
        self.mcp_cfg = mcp_cfg
        self.parallel_tool_calls = parallel_tool_calls
        self.enable_memory = enable_memory
        
        # crete langchain tools
        self._lc_tools = _lc_tools
    
    async def async_make_graph(self):
        memory = MemorySaver() if self.enable_memory else None
        
        if self.mcp_cfg is not None:
            try:
                mcp_client = MultiServerMCPClient(self.mcp_cfg)
                mcp_tools = await mcp_client.get_tools()
                _lc_tools = mcp_tools + self._lc_tools
            except Exception as err:
                raise RuntimeError(f"{err}\nMCP Client初始化失败，请确定MCP Server端已启动，并检查mcp_cfg参数: {self.mcp_cfg}")
        else:
            _lc_tools = self._lc_tools
        logger.info("可用工具如下：")
        logger.info(pprint.pformat(_lc_tools))
        
        # 如允许工具并行，则允许模型一次推理出多个工具，但为了安全目前执行是串行，因为设置了max_concurrency=1
        # 如禁止并行调用多个tool，有些工具如操作不支持并行，后续要支持同时聊天和操作再去掉
        _model = self.model.bind_tools(_lc_tools, parallel_tool_calls=self.parallel_tool_calls)
        
        agent = create_react_agent(model=_model, tools=_lc_tools, prompt=self.system_prompt, checkpointer=memory)
        return agent
