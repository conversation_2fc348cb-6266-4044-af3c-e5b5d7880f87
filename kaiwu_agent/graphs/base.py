from abc import abstractmethod
from langgraph.graph.graph import CompiledGraph


class GraphCreatorBase(object):
    """Base class for graph creator."""
    def __init__(self):
        pass
    
    def make_graph(self) -> CompiledGraph:
        """Create a langgraph CompiledGraph instance.
        
        Returns:
            A compiled LangChain runnable that can be used for chat interactions.
        """
        raise NotImplementedError

    def async_make_graph(self) -> CompiledGraph:
        """Create a langgraph CompiledGraph instance asynchronously.
        
        Returns:
            A compiled LangChain runnable that can be used for chat interactions.
        """
        raise NotImplementedError
