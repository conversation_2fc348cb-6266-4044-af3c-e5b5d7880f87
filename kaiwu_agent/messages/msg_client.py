from typing import List, Union
from algo_tools.messages.msg_client import (
    BaseMsgClient, VoiceSocketClient, FastAPIInstructListener, ComposeMsgClient)
from kaiwu_agent.utils.registry import MSG_CLIENTS

MSG_CLIENTS.register_module(VoiceSocketClient)
MSG_CLIENTS.register_module(FastAPIInstructListener)


@MSG_CLIENTS.register_module()
class ComposeClient(ComposeMsgClient):
    """复合消息处理客户端，支持多个客户端实例，通过传入的客户端列表进行控制。
    """
    def __init__(self,
                 client_list: List[Union[dict, BaseMsgClient]],
                 # 基础配置
                 maxsize: int = 1000,
                 # 客户端实例列表
                 recv_callback=None, 
                 msg_preprocessor=None,
                 enable_input: bool = True,
                 enable_output: bool = True):
        # 实例化
        inst_clients = []
        for client in client_list:
            if not isinstance(client, BaseMsgClient):
                client = MSG_CLIENTS.build_from_cfg(client)
            inst_clients.append(client)

        super().__init__(client_list=inst_clients, 
                         maxsize=maxsize, 
                         recv_callback=recv_callback,
                         msg_preprocessor=msg_preprocessor,
                         enable_input=enable_input,
                         enable_output=enable_output)
