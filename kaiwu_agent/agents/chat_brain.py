import asyncio
import queue
import time
import pprint
import threading
from langchain_core.language_models.chat_models import BaseChatModel 
from algo_tools.utils import CYAN, RESET
from kaiwu_agent.utils.env import env_manager
from algo_tools.messages.msg_client import BaseMsgClient
from kaiwu_agent.configs.config import EXIT_INSTRUCTION
from kaiwu_agent.utils.kaiwu_message import KaiwuMessageClient
from kaiwu_agent.utils.env import env_manager
from kaiwu_agent.agents.brain_agent import BaseBrainAgent
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage, trim_messages
import logging
logger = logging.getLogger(__name__)


class ChatBrainAgent(BaseBrainAgent):
    """直接用聊天大模型回答用户指令，不调用任何工具，用聊天大模型作为Brain。"""
    def __init__(self,
                 msg_client: BaseMsgClient,
                 chat_model: BaseChatModel,
                 system_prompt: str,
                 history_size: int = 21,
                 msg_buffer_size: int = 5,
                 accept_msg_when_busy = False,
                 kaiwu_msg_client: KaiwuMessageClient = None,
                 max_concurrency: int = 1,
                 name: str = None):
        super().__init__(msg_client=msg_client, 
                         name=name, 
                         accept_msg_when_busy=accept_msg_when_busy,
                         kaiwu_msg_client=kaiwu_msg_client)
        self.chat_model = chat_model
        self.system_prompt = system_prompt
        self.history_size = history_size
        self.max_concurrency = max_concurrency
        self.msg_buffer_size = msg_buffer_size
        
        self._instruct_queue = queue.Queue()
        self._answer_queue = queue.Queue()
        self._process_thread = None
        self.time = None
        
        # 初始化对话历史，包含系统消息
        self.messages = [SystemMessage(content=system_prompt)]

    def infer(self, instruction, *args, **kwargs):
        self._instruct_queue.put(instruction)
        # TODO: 阻塞等待，之后可考虑非阻塞
        answer = self._answer_queue.get()
        return answer
    
    async def print_stream(self, instruction):
        # 修剪对话历史以保留最近的 K 条消息
        # logger.info(pprint.pformat(self.messages))
        
        # 添加用户消息到对话历史
        self.messages.append(HumanMessage(content=instruction))
        logger.info(f"{self.name}: instruction: {instruction}")
        # logger.info(pprint.pformat(self.messages))

        # 使用 astream 方法进行流式输出
        answer = ""
        # buffer = ""
        self.time = time.time()
        async for chunk in self.chat_model.astream(self.messages):
            # logger.info(chunk.content)
            answer += chunk.content
            # buffer += chunk.content
            # if len(buffer) >= self.msg_buffer_size:
            #     answer += buffer
            #     # logger.info(buffer) 
            #     buffer = ""  
                
            # if env_manager.is_set("STOP"):
            #     return True  
        
        logger.info(f"{self.name}: answer: {answer}")
        # TODO: 换成流式发送合成TTS
        await self.async_send_robot_message(message=answer, cmd="append")
        
        cost = (time.time() - self.time) * 1000
        logger.info(CYAN + f"cost={cost:.1f}ms" + RESET)
        
        # 添加 AI 回复到对话历史
        self.messages.append(AIMessage(content=answer))
        
        self.messages = trim_messages(
            self.messages,
            # Keep the last <= n_count tokens of the messages.
            strategy="last",
            token_counter=len,
            # When token_counter=len, each message
            # will be counted as a single token.
            # Remember to adjust for your use case
            max_tokens=self.history_size,
            # Most chat models expect that chat history starts with either:
            # (1) a HumanMessage or
            # (2) a SystemMessage followed by a HumanMessage
            start_on="human",
            # Usually, we want to keep the SystemMessage
            # if it's present in the original history.
            # The SystemMessage has special instructions for the model.
            include_system=True,
            allow_partial=False,
        )
        # logger.info("after trim:")
        # logger.info(pprint.pformat(self.messages))
        
        return False
    
    async def run_exit(self):
        try:
            # TODO: 设置超时
            output = await self.chat_model.ainvoke(HumanMessage(content=EXIT_INSTRUCTION))
            logger.info(f"brain agent执行退出指令成功：`{EXIT_INSTRUCTION}`")
        except Exception as err:
            logger.error(f"brain agent执行退出指令失败：`{EXIT_INSTRUCTION}`，忽略: {err}")
    
    async def process_instruction(self):
        logger.info(f"start waiting instruction ...")
        while True:
            # instruction = input("请输入指令（输入 'exit' 退出）：")
            instruction = self._instruct_queue.get()
            
            recv_exit = await self.print_stream(instruction)
            if recv_exit:
                logger.info(f"brain agent收到退出指令，正在安全退出")
                self.run_exit()
            
            self._answer_queue.put({"answer": None})
            
        logger.info(f"stop waiting instruction, finish.")
    
    def start_process(self):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(self.process_instruction())
    
    def start(self):
        self._process_thread = threading.Thread(target=self.start_process, daemon=True)
        self._process_thread.start()
        super().start()

    def stop(self):
        super().stop()
        self._process_thread.join()
