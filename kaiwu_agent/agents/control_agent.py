import asyncio
import threading
import websockets
import json
import time
import traceback
import httpx
import queue
import uvicorn
from enum import Enum
from typing import Optional
from fastapi import FastAPI
from pydantic import BaseModel
from kaiwu_agent.agents.base import BaseControlAgent
from algo_tools.messages.msg_client import BaseMsgClient, FastAPIInstructListener
from algo_tools.utils.common import get_time_stamp
from algo_tools.utils import CYAN, RESET
import logging
logger = logging.getLogger(__name__)

class TaskState(str, Enum):
    PENDING = "Pending"
    RUNNING = "Running"
    FINISH = "Finish"
    FAILURE = "Failure"

# 定义消息模型
class Message(BaseModel):
    Task: str = ''  # i.e., user instruction
    Conversation_ID: int = -1
    State: Optional[TaskState]  # task state, including ["Pending", "Running", "Finish", "Failure"]
    Data: str = '' # content that needs to be sent to brain agent, the skill status including ["Pending", "Running", "Finish", "Failure"]
    Additional_Info: str = ''  # Additional information to be sent to brain agent

class Command(BaseModel):
    Task: str  # For example, {"Task": "Prepare milk for breakfast."}
    Conversation_ID: int

FAILED_REPLY = "抱歉，该功能暂不可用，我们聊点其它的吧"


class TGControlAgentV1(BaseControlAgent):
    """TianGong Control Agent, WRC 2024 Version.
    
    注意：
    - 当前是客户端
    - 服务端：不要主动关闭连接，避免发出的消息客户端收不到，发出消息也要设置超时（避免死锁）
    - 客户端: 没收到succeed、failed不关闭连接，且一直处于监听状态，直至超时（避免死锁）
    """
    def __init__(self,
                 url = 'ws://192.168.41.110:9527/chatsocket',
                 msg_client: BaseMsgClient = None, 
                 wait_result = True,
                 log_recv = True,
                 connect_timeout = 3.0, 
                 recv_timeout = 60.0):
        # if wait_result:
        #     assert msg_client is not None, msg_client
        
        self.url = url
        self.msg_client = msg_client
        self.wait_result = wait_result
        self.log_recv = log_recv
        self.connect_timeout = connect_timeout
        self.recv_timeout = recv_timeout
    
    async def _send(self, websocket, instruction, conversation_id, skill):
        instruction_str = instruction
        # 发送消息
        try:
            instruction = json.loads(instruction_str)
        except:
            print("操作指令：", instruction_str)
        news_dict = {
            "body": instruction, 
            "_xsrf": "HUMAN", 
            "conversation_id": conversation_id,
            "skill": skill}
        msg_str = json.dumps(news_dict, ensure_ascii=False)
        await websocket.send(msg_str)
        logger.info(f"TGControlAgentV1.send_message: succeed: {msg_str}")
    
    async def recv_message(self, websocket, conversation_id):
        # 获取执行结果
        request_instruction = False
        succeed = False
        try:
            while True:
                result_msg = await asyncio.wait_for(websocket.recv(), timeout=self.recv_timeout)
                msg_dict = json.loads(result_msg)
                body = msg_dict['body']
                # 注：服务端的回复消息是广播机制，如果存在多个客户端，必须用conversation_id来鉴别
                if "conversation_id" in msg_dict and msg_dict["conversation_id"] == conversation_id \
                    and body.startswith('ROBOT: '):
                    result_str = body[7:]
                    state = msg_dict["state"]
                    if self.log_recv:
                        logger.info(f"TGControlAgentV1.recv_message: state: {state}, result: {result_str}")
                    
                    if state in ("started", "running"):
                        if False and self.msg_client is not None:
                            await self.msg_client.send_message_once(result_str)
                    elif state in ("ask_user",):
                        # 告诉brain agent，下一个最新指令要发给control agent，那边在等着
                        request_instruction = True
                        succeed = True
                        break
                    elif state in ("succeed"):
                        succeed = True
                        break
                    elif state in ("failed"):
                        break
                    else:
                        logger.info(f'TGControlAgentV1.recv_message: ERROR, unknown state: {state} !!!!')
                        break
                elif body.startswith('HUMAN: '):                    
                    # TODO: 会收到我们发过去的HUMAN消息
                    # logger.info(body)  
                    pass
                else:
                    logger.warning(f"recv_message: ERROR, unknown message: {msg_dict} !!!!")
                
        except (asyncio.exceptions.TimeoutError, websockets.exceptions.ConnectionClosedError):
            logger.warning(f'recv_message: 警告, 超过 {self.recv_timeout}s 没有收到执行结果，请检查4090主机上的技能执行情况: {websocket.remote_address}')
            # 不知道技能侧执行成功还是失败，不播报
            result_str = None
        
        return result_str, request_instruction, succeed

    async def send_message(self, instruction, skill):
        # async, await只能在async的函数里使用，所以send_message得用async修饰
        conversation_id = get_time_stamp()
        request_instruction = False
        succeed = False
        try:
            async with websockets.connect(self.url, open_timeout=self.connect_timeout) as websocket:
                # 发送消息
                await self._send(websocket, instruction, conversation_id, skill)
                if self.wait_result:
                    result_str, request_instruction, succeed = await self.recv_message(
                        websocket=websocket, 
                        conversation_id=conversation_id)
                else:
                    # 不播报
                    result_str = None
                    succeed = True
        except (ConnectionRefusedError, asyncio.TimeoutError) as err:
        # except Exception as err:
            logger.error(f"send_message: {err}: 连接4090主机超时，请确保4090主机和Orin在同一网络下: {self.url}")
            result_str = None
        
        return {"answer": result_str, "request_instruction": request_instruction, "succeed": succeed}

    def infer(self, instruction, skill):
        start_time = time.time()
        # 获取当前线程的全局事件，不存在会自动创建
        loop = asyncio.get_event_loop()
        
        try:
            # logger.info(f"send_to_skill_service loop id: {id(loop)}")
            run_result = loop.run_until_complete(self.send_message(instruction, skill))
        except Exception as err:
            logger.error(f'send_to_skill_service: ERROR: {err}')
            # 不知道技能侧执行成功还是失败，不播报
            run_result = {"answer": None} 
        
        cost = (time.time() - start_time) * 1000
        # logger.info(CYAN + f"TGControlAgentV1.infer: result={run_result}, cost={cost:.1f}ms" + RESET)

        return run_result


class TGControlAgentV2(BaseControlAgent):
    """Kaiwu Agent FastAPI Version.
    """
    def __init__(self,
                 name: str = 'TGControlAgentV2',
                 port: int = 8080,
                 server_url = "http://172.168.1.77:8080/control_agent_task_command",
                 msg_client: BaseMsgClient = None,
                 wait_result: bool = True,
                 log_recv: bool = True,
                 connect_timeout: float = 240.0,
                 recv_timeout: float = 240.0,
                 write_timeout: float = 240.0,
                 pool_timeout: float = 240.0):
        """
        初始化 Agent 类，设置服务器地址、名称和服务端口。
        :param name: 代理的名字（例如 Agent A 或 Agent B）
        :param server_url: 对方服务器的 URL 地址
        :param port: 当前服务器的端口
        :param msg_client: 语音客户端对象
        :param wait_result: 是否等待最终结果，默认为 True
        :param log_recv: 是否打印接收到的消息日志，默认为 True
        :param connect_timeout: 连接超时设置，默认为 240 秒
        :param recv_timeout: 接收响应超时设置，默认为 240 秒
        :param write_timeout: 写入超时设置，默认为 240 秒
        :param pool_timeout: 连接池超时设置，默认为 240 秒
        """
        self.name = name
        self.port = port
        self.server_url = server_url
        self.msg_client = msg_client
        self.wait_result = wait_result
        self.log_recv = log_recv
        self.connect_timeout = connect_timeout
        self.recv_timeout = recv_timeout
        self.write_timeout = write_timeout
        self.pool_timeout = pool_timeout
        self.message_queue = queue.Queue()  # 创建一个队列用于消息通信
        self.app = FastAPI()
        self._initialize_routes()
        self.control_state = {}
        self.current_task = ''
        self.pre_step = {}
        # 启动当前 Agent 的服务器
        threading.Thread(target=self._run_server, daemon=True).start()

    def _initialize_routes(self):
        """初始化路由，用于处理接收消息和响应"""
        @self.app.post("/brain_agent_receiver")
        async def recv_message(message: Message):
            # 处理接收到的消息并返回响应"""
            await self._handle_message(message)
            # 将消息放入队列
            self.message_queue.put(message)
            return {"status": "message_received"}

    def _run_server(self):
        """启动 FastAPI 服务器"""
        uvicorn.run(self.app, host="0.0.0.0", port=self.port)

    async def _handle_message(self, message: Message):
        """根据消息内容返回不同的响应"""
        if self.log_recv:
            logger.info(f"{self.name} recv_message: {message}")
        # 获取Control Agent的技能运行计划
        if message.Data:
            try:
                json_data = json.loads(message.Data)
                plan_data = json.loads(json_data.get("Plan", '[]'))
                skill_status_data = json.loads(json_data.get("Skill_Status", '[]'))
                # 更新技能规划
                if plan_data:
                    logger.info(f"skill plan: {plan_data}")
                    for skill in plan_data:
                        for skill_idx, skill_name in skill.items():
                            self.control_state[skill_idx] = {"name": skill_name, "status": TaskState.PENDING}
                    # 向云端报告任务计划
                    if type(self.msg_client) == FastAPIInstructListener:
                        control_step = {"name": self.current_task, "status": TaskState.PENDING, "subSteps": [v for k, v in self.control_state.items()]}
                        # 注意：不要用send_message()，当前已处在一个event loop中，_handle_message已经是async，直接await，否则报错：this event loop is already running.
                        if self.pre_step.get("status") == TaskState.RUNNING:
                            await self.msg_client.send_message_once(
                                text="任务进度如下。",
                                task_status=message.State,
                                steps=[self.pre_step, control_step],
                                new_step=True)
                            self.pre_step["status"] = TaskState.FINISH
                            time.sleep(1)
                        await self.msg_client.send_message_once(
                            text="任务进度如下。",
                            task_status=message.State,
                            steps=[self.pre_step, control_step]
                        )

                # 更新技能状态
                if skill_status_data:
                    logger.info(f"skill status: {skill_status_data}")
                    for skill in skill_status_data:
                        for skill_idx, skill_status in skill.items():
                            self.control_state[skill_idx]["status"] = skill_status
                            # 为消息遗漏问题兜底：将Finish技能前的所有技能的状态置为Finish
                            if skill_status == TaskState.FINISH:
                                for i in range(1, int(skill_idx[-1])):
                                    self.control_state["skill" + str(i)]["status"] = TaskState.FINISH
                    # 向云端报告任务进度
                    if type(self.msg_client) == FastAPIInstructListener:
                        control_step = {"name": self.current_task, "status": TaskState.RUNNING, "subSteps": [v for k, v in self.control_state.items()]}
                        await self.msg_client.send_message_once(
                            text="任务进度如下。",
                            task_status=message.State,
                            steps=[self.pre_step, control_step]
                        )
            except Exception as e:
                logger.error(f"_handle_message: {e}")
                # 向云端报告任务进度
                self.control_state = {}
        elif message.State == TaskState.FINISH or message.State == TaskState.FAILURE:
            if type(self.msg_client) == FastAPIInstructListener:
                control_step = {"name": self.current_task, "status": message.State, "subSteps": [v for k, v in self.control_state.items()]}
                await self.msg_client.send_message_once(
                    text="任务进度如下。",
                    task_status=message.State,
                    steps=[self.pre_step, control_step])
            self.control_state = {}
        logger.info(f"control_state: {self.control_state}")
        return

    async def _send_and_wait_for_response(self, task: str, instruction: str):
        """发送消息并等待响应"""
        conversation_id = get_time_stamp()
        # 发送消息
        command = Command(Task=instruction, Conversation_ID=conversation_id)
        logger.info(f"{self.name} sending message: {command}")
        try:
            # 设置所有四个超时参数
            timeout = httpx.Timeout(
                connect=self.connect_timeout,  # 连接超时
                read=self.recv_timeout,        # 接收超时
                write=self.write_timeout,      # 写入超时
                pool=self.pool_timeout         # 连接池超时
            )
            async with httpx.AsyncClient(timeout=timeout) as client:
                # 发送第一条消息
                response = await client.post(self.server_url, json=command.dict())
                logger.info(f"{self.name} raw response: {response.text}")
                if response.status_code != 200:
                    logger.error(f"{self.name} 请求失败，状态码: {response.status_code}, 内容: {response.text}")
                elif not response.text.strip():
                    logger.error(f"{self.name} 收到空响应！")
                else:
                    try:
                        response_data = response.json()
                        logger.info(f"{self.name} get response: {response_data}")
                    except json.decoder.JSONDecodeError as e:
                        logger.error(f"{self.name} 解析 JSON 失败: {e}, 原始内容: {response.text}")

                # 等待control agent执行任务后的发送的第二条消息
                # 如果需要等待最终结果，阻塞当前线程，直到从队列中收到响应
                if self.wait_result:
                    # !TODO: 实现消息流和工具并行化：因为此处阻塞了主进程，使得机器人不能多任务并行
                    # 实现持续监听，如果返回的消息中状态为Finish则返回，如果是Running则继续监听
                    while True:
                        message = self.message_queue.get(timeout=self.recv_timeout)
                        if message.State == 'Finish' or message.State == 'Failure':
                            break
                    logger.info(f"{self.name} received final message from queue: {message}")
                else:
                    logger.info(f"{self.name} did not wait for final result.")
                    message = Message(Task=instruction,
                                      State=TaskState.FINISH,
                                      Conversation_ID=conversation_id,
                                      Additional_Info='Mission Complete.')
        except httpx.TimeoutException:
            logger.info(f"{self.name} request timed out.")
            message = Message(Task=instruction,
                              State=TaskState.FAILURE,
                              Conversation_ID=conversation_id,
                              Additional_Info='request timed out, failed to send message.')
        except Exception as e:
            logger.info(f"{self.name} encountered an error: {traceback.format_exc()}")
            message = Message(Task=instruction,
                              State=TaskState.FAILURE,
                              Conversation_ID=conversation_id,
                              Additional_Info=f'encountered an error: {e}, failed to send message.')
        return message

    def infer(self, plan, task=''):
        start_time = time.time()
        # 获取当前线程的全局事件，不存在会自动创建
        loop = asyncio.get_event_loop()
        # !TODO: 先使用假的环境感知步骤，后续实现全局的Plan规划上报
        self.pre_step = {"name": "环境感知", "status": TaskState.RUNNING, "subSteps": []}
        try:
            logger.info(f"send_to_control_agent_service loop id: {id(loop)}")
            self.current_task = task
            response = loop.run_until_complete(self._send_and_wait_for_response(task, plan))
        except Exception as err:
            logger.error(f'send_to_control_agent_service: ERROR: {err}')
            response = Message(Task=plan, State=TaskState.FAILURE, Additional_Info='ERROR: {err}')

        cost = (time.time() - start_time) * 1000
        logger.info(CYAN + f"TGControlAgentV2.infer: result={response}, cost={cost:.1f}ms" + RESET)

        return response.dict()