import asyncio

from kaiwu_agent.agents.base import BaseChatAgent
from kaiwu_agent.agents.brain_agent import BaseBrainAgent
from algo_tools.messages.msg_client import BaseMsgClient
from langgraph.graph import StateGraph, START, END
from langchain_core.prompts import <PERSON>t<PERSON><PERSON>pt<PERSON><PERSON>plate
from langchain_openai import <PERSON>t<PERSON><PERSON>A<PERSON>
from pydantic import BaseModel, Field
from typing import Annotated, List, Tuple, Union
from typing_extensions import TypedDict
import operator
import logging
logger = logging.getLogger()


class Plan(BaseModel):
    """Plan to follow in future"""
    steps: List[str] = Field(
        description="different steps to follow, should be in sorted order"
    )

class PlanExecute(TypedDict):
    input: str
    plan: List[str]
    past_steps: Annotated[List[Tuple], operator.add]
    response: str
    
class Response(BaseModel):
    """Response to user."""
    response: str

class Act(BaseModel):
    """Action to perform."""
    action: Union[Response, Plan] = Field(
        description="Action to perform. If you want to respond to user, use Response. "
        "If you need to further use tools to get the answer, use Plan."
    )


class TGBrainAgentV3(BaseBrainAgent):
    """Based on Langgraph

    Args:
        BaseBrainAgent (_type_): _description_
    """
    def __init__(self, 
                msg_client: BaseMsgClient,
                agent_executor:BaseChatAgent,
                tools:list,
                planner: BaseChatAgent,
                replanner: BaseChatAgent,
                normal_dialogue:BaseChatAgent,
                display_client = None,
                image_data = None,
                name = None):
        super().__init__(msg_client=msg_client, display_client=display_client, name=name)
        
        self.agent_executor = agent_executor
        self.tools = tools
        self.planner = planner
        self.replanner = replanner
        self.normal_dialogue = normal_dialogue
        self.image_data = image_data
        self.app = self.build_workflow()
        
    # def terminate(text: str):
    #     """任务结束，获得final answer，然后语音播报。
    #     TODO
    #     """
    #     final_answer="任务完成了"
    #     return final_answer

    async def execute_step(self, state: PlanExecute):
        plan = state["plan"]
        plan_str = "\n".join(f"{i+1}. {step}" for i, step in enumerate(plan))
        task = plan[0]
        await self.display_message(message=f">>> Current Step: {task}")
        
        task_formatted = f"""For the following plan:
                        {plan_str}\n\nYou are tasked with executing step {1}, {task}."""
        
        # 使用stream可以逐步展示LLM调用tool的过程
        for agent_response in self.agent_executor.stream({"messages": [("user", task_formatted)]}, stream_mode="values"):
            message = agent_response["messages"][-1]
            logger.info(f"\n{message.pretty_repr()}")
            await self.display_message(message=message.pretty_repr())
        
        # agent_response = await self.agent_executor.ainvoke(
        #     {"messages": [("user", task_formatted)]}
        # )

        # if len(agent_response["messages"])>2:
        #     for message in agent_response["messages"][1:]:
        #             logger.info(f"\n{message.pretty_repr()}") # 输出所有ToolMessage以及AIMessage
        #             await display_message(message=message.pretty_repr())
                    
        return  {
                "past_steps": [(task, agent_response["messages"][-1].content)],
            }

    async def plan_step(self, state: PlanExecute):
        user_instruction = state["input"]
        await self.display_message(message=f"user instruction:{user_instruction}")
        plan = await self.planner.ainvoke({"messages": [("user", state["input"])]})
        plan_str = "\n".join(f"{i+1}. {step}" for i, step in enumerate(plan.steps))
        logger.info(f"plan_step:\n{plan_str}")
        await self.display_message(message=f"plan:\n{plan_str}")
        return {"plan": plan.steps}

    async def replan_step(self, state: PlanExecute):
        await self.display_message(message=f"<<< Step Done.\n")
        output = await self.replanner.ainvoke(state)
        logger.info(f"replan_step:\n{output.action}")
        
        if isinstance(output.action, Response):
            # final_answer = self.terminate()
            final_answer = output.action.response
            await self.display_message(message=f"replan:\n{final_answer}")
            return {"response": final_answer} 
            # return {"response": output.action.response} 
        elif hasattr(output.action, 'steps'):
            plan_str = "\n".join(f"{i+1}. {step}" for i, step in enumerate(output.action.steps))
            await self.display_message(message=f"replan:\n{plan_str}")
            return {"plan": output.action.steps}
        else:
            raise ValueError(f"Unexpected action type: {type(output.action)}")
        
        # else:
        #     return {"plan": output.action.steps}

    def should_end(self, state: PlanExecute):
        if "response" in state and state["response"]:
            return END
        else:
            return "agent"
    
    def switch_branch(self, state:PlanExecute):
        # 使用用户输入来确定分支
        user_choice = state["input"]
        test_dialogue_prompt = ChatPromptTemplate.from_messages(
        [
            (
                "system",
                "你是一个决策大师，请判断以何种方式回答用户的下面的问题，是否需要逐步的规划，如果需要返回 计划 ，否则返回 对话 。请在 计划 和 对话 之间选择",
            ),
            ("placeholder", "{messages}"),
        ]
        )

        # 设置对话模型
        test_dialogue = test_dialogue_prompt | ChatOpenAI(
            model="gpt-4o",
            api_key="sk-Wyy6gs0pUYfhmP7RLo6XvupsmujD4odpK3yU1tQbwtLh1mWt",
            base_url="https://api.chatanywhere.tech/v1",
            temperature=0.5,
        )
        response = test_dialogue.invoke({"messages": [("user",
                    [
                        {
                            "type": "text",
                            "text": user_choice,
                        },
                        {
                            "type": "text",
                            "text": "回答请在 计划 和 对话 之间选择",
                        }
                    ])]})
        if "计划" in response.content:
            return "planner"
        else:
            return "dialogue"  # 如果没有匹配，则返回到选择模式
    
    async def normal_dialogue_step(self, state: PlanExecute) -> dict:
        user_query = state["input"]
        response = await self.normal_dialogue.ainvoke({"messages": [("user",
                    [
                        {
                            "type": "text",
                            "text": user_query
                        },
                        {
                            "type": "text",
                            "text": "请以符合设定的口吻进行回答",
                        }
                    ])]})
        response_str = response.content
        return {"response": response_str, "steps": []}  # 或者返回默认步骤
    
    # 选择模式步骤
    async def choose_mode_step(self, state: PlanExecute) -> dict:
        # user_choice = state["input"][0]["text"]
        return state  # 添加 'steps'
    
    def build_workflow(self):
        workflow = StateGraph(PlanExecute)
        workflow.add_node("planner", self.plan_step)
        workflow.add_node("agent", self.execute_step)
        workflow.add_node("replan", self.replan_step)
        workflow.add_edge(START, "planner")
        workflow.add_edge("planner", "agent")
        workflow.add_edge("agent", "replan")
        workflow.add_conditional_edges(
            "replan",
            # Next, we pass in the function that will determine which node is called next.
            self.should_end,
            ["agent", END],
        )

        app = workflow.compile()
        return app
    
    # # Add choose_mode and dialogue
    # def build_workflow(self):
    #     # memory = MemorySaver()
    #     workflow = StateGraph(PlanExecute)
    #     # 添加计划节点
    #     workflow.add_node("planner", self.plan_step)

    #     # 添加执行步骤
    #     workflow.add_node("agent", self.execute_step)

    #     # 添加重新计划节点
    #     workflow.add_node("replan", self.replan_step)

    #     # 添加普通对话节点
    #     workflow.add_node("dialogue", self.normal_dialogue_step)

    #     # 添加选择节点
    #     workflow.add_node("choose_mode", self.choose_mode_step)

    #     # # 定义起始节点
    #     workflow.add_edge(START, "choose_mode")

    #     # 从选择模式节点到计划和对话
    #     # workflow.add_edge("choose_mode", "planner")
    #     # workflow.add_edge("choose_mode", "dialogue")
    #     workflow.add_conditional_edges(
    #         "choose_mode",
    #         self.switch_branch,
    #         ["planner", "dialogue"],
    #     )

    #     # 从计划到执行
    #     workflow.add_edge("planner", "agent")

    #     # 从执行到重新计划
    #     workflow.add_edge("agent", "replan")

    #     workflow.add_edge("dialogue", END)

    #     # 从重新计划回到执行或结束
    #     workflow.add_conditional_edges(
    #         "replan",
    #         self.should_end,
    #         ["agent", END],
    #     )

    #     # 编译工作流
    #     app = workflow.compile()
    #     return app
    
    
    def infer(self, instruction, intent, answer=None, **kwargs):
        if answer is not None:
            # TODO: 这应该加到system prompt里，临时加一下
            instruction += f" | 已知信息：{answer}"
            logger.info(f"TGBrainAgentV3.infer: new instruction: {instruction}")
            # return {"answer": answer}
        config = {"recursion_limit": 50}
        if self.image_data is None:
            inputs = {"input": instruction}
        else:
            inputs = {
                "input": [
                    {
                        "type": "text",
                        "text": instruction,
                    },
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/jpeg;base64,{self.image_data}"},
                    }
                ]
            }
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        results = loop.run_until_complete(self.app.ainvoke(input=inputs, config=config))
        # TODO: 在执行过程已经播报，最后的结果不用播报了？
        answer = None
        # answer = results['response']
        
        return {"answer": answer}
        