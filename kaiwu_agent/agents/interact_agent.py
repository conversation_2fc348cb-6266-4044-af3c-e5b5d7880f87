import threading
import pyaudio
import queue
import base64
import json
import time
from websocket import create_connection, WebSocketConnectionClosedException
from dotenv import load_dotenv
import logging
import copy
from kaiwu_agent.agents.base import BaseAgent
from langchain_core.tools import BaseTool
from kaiwu_agent.tools.interact_agent_server import LocalVoiceSocketServer
from kaiwu_agent.agents.brain_agent import BaseBrainAgent
from kaiwu_agent.tools.get_xunfei_sound_tools import SocketReceive

logger = logging.getLogger()
logger.setLevel(level = logging.INFO)

load_dotenv()

class InteractAgent(BaseAgent):
    def __init__(self,
                 audio_config,
                 api_key,
                 ws_url,
                 brain_agent:BaseBrainAgent,
                 update_event_session=None,
                 tools=None,
                 available_tools=None,
                 name=None,
                 xfei=True,
                 server_ip='***********',
                 port=9080):
        super().__init__(name=name)
        self.audio_config = audio_config
        self.audio_buffer = bytearray()
        self.mic_queue = queue.Queue()

        self.mic_on_at = 0
        self.mic_active = None
        self.stop_event = threading.Event()

        self.bot_backend = BotBackend(available_tools=available_tools,tools=tools)

        self.tools = tools
        self.available_tools = available_tools
        self.update_event_session = update_event_session
        self.api_key = api_key
        self.ws_url = ws_url
        self.cnt_brain_thinking = 0
        self.message_from_brain = []

        self.local_server = LocalVoiceSocketServer()
        self.idx = 0
        self.brain_agent = brain_agent
        self.event = {}
        self.brain_init = True

        self.mic_stream = None
        self.spkr_stream = None
        self.p = None
        self.use_xfei = xfei
        self.last_assistant_item = None

        if self.use_xfei:
            try:
                self.socket = SocketReceive(server_ip=server_ip,port=port)
            except Exception as err:
                logger.error(f"connecting xunfei server failed, failure reason {err}")

    def start(self):
        self.p = pyaudio.PyAudio()
        if not self.use_xfei:
            # use pyaudio mic input
            audio_config_input = copy.deepcopy(self.audio_config)
            self.mic_stream = self.audio_input(audio_config_input, self.p)
            self.mic_stream.start_stream()
        else:
            xfei_thread = threading.Thread(target=self.get_audio_from_xfei_server)
            xfei_thread.start()

        audio_config_output = copy.deepcopy(self.audio_config)
        self.spkr_stream = self.audio_output(audio_config_output, self.p)
        self.spkr_stream.start_stream()

        self.infer()

    def stop(self):
        if self.mic_stream and self.spkr_stream and self.p:
            if not self.use_xfei:
                self.mic_stream.stop_stream()
                self.mic_stream.close()
            else:
                self.socket.close()

            self.spkr_stream.stop_stream()
            self.spkr_stream.close()

            self.p.terminate()
            logger.info('Audio streams stopped and resources released. Exiting.')

    def get_audio_from_xfei_server(self):
        while not self.stop_event.is_set():
            audio_chunk, _ = self.socket.process()
            if audio_chunk:
                # print("audio chunk not none")
                self.mic_queue.put(audio_chunk)
                # print(self.mic_queue.qsize())

    def infer(self):
        try:
            #realtime audio model
            self.connect_to_openai()

            if not self.use_xfei:
                while self.mic_stream.is_active() and self.spkr_stream.is_active():
                    time.sleep(0.1)

        except KeyboardInterrupt:
            logger.info('Gracefully shutting down...')
            self.stop_event.set()

        finally:
            self.stop()

    def connect_to_openai(self):
        ws = None
        try:
            # ws = create_connection(self.ws_url, header=[f'Authorization: Bearer {self.api_key}', 'OpenAI-Beta: realtime=v1'])
            ws = create_connection(self.ws_url)
            logger.info('Connected to OpenAI WebSocket.')

            ws.send(json.dumps({
                'type': 'response.create',
                'response': {
                    'modalities': ['audio', 'text'],
                    'instructions': '请协助用户',
                    # 'instructions':'You can only take the following actions: 获取当前时间',
                }
            }))

            # Start the recv and send threads
            receive_thread = threading.Thread(target=self.receive_audio_from_websocket, args=(ws,))
            receive_thread.start()
            # 不支持异步websockets
            # await self.receive_audio_from_websocket(ws)            

            mic_thread = threading.Thread(target=self.send_mic_audio_to_websocket, args=(ws,))
            mic_thread.start()
            # await self.send_mic_audio_to_websocket(ws)

            self.local_server.start()

            # Wait for stop_event to be set
            while not self.stop_event.is_set():
                time.sleep(0.1)

            # Send a close frame and close the WebSocket gracefully
            logger.info('Sending WebSocket close frame.')
            ws.send_close()

            logger.info('WebSocket closed and threads terminated.')
        except Exception as e:
            logger.error(f'Failed to connect to OpenAI: {e}')
        finally:
            if ws is not None:
                try:
                    ws.close()
                    logger.info('WebSocket connection closed.')
                except Exception as e:
                    logger.error(f'Error closing WebSocket connection: {e}')
        
    def audio_output(self, audio_config, p):
        def _spkr_callback(in_data, frame_count, time_info, status):

            bytes_needed = frame_count * 2
            current_buffer_size = len(self.audio_buffer)

            if current_buffer_size >= bytes_needed:
                audio_chunk = bytes(self.audio_buffer[:bytes_needed])
                self.audio_buffer = self.audio_buffer[bytes_needed:]
                self.mic_on_at = time.time() + audio_config['REENGAGE_DELAY_MS'] / 1000
            else:
                audio_chunk = bytes(self.audio_buffer) + b'\x00' * (bytes_needed - current_buffer_size)
                self.audio_buffer.clear()

            return (audio_chunk, pyaudio.paContinue)

        spkr_stream = p.open(
            format=audio_config['FORMAT'],
            channels=1,
            rate=audio_config['RATE'],
            output=True,
            stream_callback=_spkr_callback,
            frames_per_buffer=audio_config['CHUNK_SIZE']
        )
        return spkr_stream
    
    def audio_input(self, audio_config, p):
        
        def _mic_callback(in_data, frame_count, time_info, status):

            if time.time() > self.mic_on_at:
                if self.mic_active != True:
                    logger.info('🎙️🟢 Mic active')
                    self.mic_active = True
                self.mic_queue.put(in_data)
            else:
                if self.mic_active != False:
                    logger.info('🎙️🔴 Mic suppressed')
                    self.mic_active = False                   

            return (None, pyaudio.paContinue)

        mic_stream = p.open(
            format=audio_config['FORMAT'],
            channels=1,
            rate=audio_config['RATE'],
            input=True,
            stream_callback=_mic_callback,
            frames_per_buffer=audio_config['CHUNK_SIZE']
        )
        
        return mic_stream

    def send_mic_audio_to_websocket(self,ws):
        try:
            while not self.stop_event.is_set():
                if not self.mic_queue.empty():
                    mic_chunk = self.mic_queue.get()
                    logger.info(f'🎤 Sending {len(mic_chunk)} bytes of audio data.')
                    encoded_chunk = base64.b64encode(mic_chunk).decode('utf-8')
                    message = json.dumps({'type': 'input_audio_buffer.append', 'audio': encoded_chunk})
                    try:
                        ws.send(message)
                    except WebSocketConnectionClosedException:
                        logger.error('WebSocket connection closed.')
                        break
                    except Exception as e:
                        logger.error(f'Error sending mic audio: {e}')
        except Exception as e:
            logger.error(f'Exception in send_mic_audio_to_websocket thread: {e}')
        finally:
            logger.info('Exiting send_mic_audio_to_websocket thread.')
    
    def receive_audio_from_websocket(self, ws):        
        try:
            while not self.stop_event.is_set():
                try:
                    message = ws.recv()

                    if self.brain_init:
                        start_thread = threading.Thread(target=self.brain_agent.start)
                        start_thread.start()
                        self.brain_init=False
                    
                    if not message:  # Handle empty message (EOF or connection close)
                        logger.info('🔵 Received empty message (possibly EOF or WebSocket closing).')
                        break

                    # Now handle valid JSON messages only
                    message = json.loads(message)
                    event_type = message['type']
                    logger.info(f'⚡️ Received WebSocket event: {event_type}')

                    if message['type'] != "response.audio.delta":
                        logger.debug("+++++++++++++")
                        logger.debug(message)

                    if event_type == "session.created":
                        logger.info("Connected: say something to GPT-4o")
                        message['type'] = "session.update"
                        message["session"] = self.update_event_session
                        logger.info(json.dumps(message,ensure_ascii=False))
                        ws.send(json.dumps(message,ensure_ascii=False))
                    
                    if message.get('item_id', None):
                        logger.info("attribute values for self.last_assistant_item")
                        self.last_assistant_item = message['item_id']

                    if event_type == 'input_audio_buffer.speech_started':
                        logger.info("Speech started detected.")
                        if self.last_assistant_item:
                            logger.info(f"Interrupting response with id: {self.last_assistant_item}")
                            handle = threading.Thread(target=self.handle_speech_started_event,args=(ws,))
                            handle.start()
                    
                    if event_type == 'response.audio.delta':
                        audio_content = base64.b64decode(message['delta'])
                        self.audio_buffer.extend(audio_content)
                        logger.info(f'🔵 Received {len(audio_content)} bytes, total buffer size: {len(self.audio_buffer)}')
                    
                    elif event_type == "response.audio_transcript.delta":
                        logger.info(message["delta"])
                        self.bot_backend.update_content(message["delta"])
                    
                    elif event_type == "response.output_item.done":
                        output = message["item"]
                        output_type = output["type"]
                        if output_type == "function_call":
                            brain_or_not = False
                            if output["name"] == "brain_thinking":
                                thread_brain = threading.Thread(target=self.brain_function_call, args=(output,ws,))
                                thread_brain.start()
                            else:
                                thread_func_call = threading.Thread(target=self.function_call_callback, args=(output,ws,))
                                thread_func_call.start()

                    elif event_type == "response.done":
                        if message["response"]["output"]:
                            func_complete_name = message["response"]["output"][0].get('name',None)
                            self.create_conversation_and_send(func_complete_name,ws)

                    elif event_type == "response.function_call_arguments.delta":
                        args = message["delta"]
                        self.bot_backend.update_args(args)

                    elif event_type == 'response.audio.done':
                        logger.info('🔵 AI finished speaking.')

                except WebSocketConnectionClosedException:
                    logger.error('WebSocket connection closed.')
                    break
                except Exception as e:
                    logger.error(f'Error receiving audio: {e}')
        except Exception as e:
            logger.error(f'Exception in receive_audio_from_websocket thread: {e}')
        finally:
            logger.info('Exiting receive_audio_from_websocket thread.')

    def handle_speech_started_event(self,ws):
        """Handle interruption when the caller's speech starts."""
        logger.info("Handling speech started event.")
        elapsed_time = time.time()
        if self.last_assistant_item:
            logger.info(f"Truncating item with ID: {self.last_assistant_item}, Truncated at now")
            truncate_event = {
                "type": "conversation.item.truncate",
                "item_id": self.last_assistant_item,
                "content_index": 0,
                "audio_end_ms": int(elapsed_time)
            }
            ws.send(json.dumps(truncate_event))

            self.audio_buffer.clear()
            self.last_assistant_item = None

    def create_conversation(self,ws):
        self.idx = self.idx+1

        msg_list = copy.deepcopy(self.message_from_brain)
        if msg_list:
            msg = msg_list[-1]
            if '.wav' not in msg["tts"]:
                text = f"为了解决之前的复杂任务，你通过调用慢思考工具进行了仔细思考，收到慢思考工具返回的信息为：{msg['tts']}，请播报这个返回信息。"
            if '3.wav' in msg["tts"]:
                text = "收到任务指令，请决定是否告知用户。注意如果上文已经告知用户收到指令，则不重复告知。",
            if '2.wav' in msg["tts"]:
                text = "任务指令完成，请在合适的时候告知用户"

            data_to_broadcast = {
                    "event_id":f"event_{self.idx}",
                    "type": "conversation.item.create",
                    "previous_item_id":None,
                    "item": {
                        "id": f"msg_from_brain_{self.idx}",
                        "type": "message",
                        "role":"user",
                        "content":[{
                            "type":"input_text",
                            "text":f"{text}",
                        }]
                    }}

            logger.info("send brain output back to gpt4o realtime")
            ws.send(json.dumps(data_to_broadcast, ensure_ascii=False))
            #需要再send一个response.create
            # self.message_from_brain.remove(msg)
            self.message_from_brain = []
            response_created = {
                                "type":"response.create",
                                "response": {
                                "modalities":["text","audio"],
                                }
                            }
            ws.send(json.dumps(response_created))

    def create_conversation_and_send(self, func_complete_name, ws):
        if func_complete_name and func_complete_name == "brain_thinking":
            self.create_conversation(ws)
        elif func_complete_name:
            logger.info("send other tool results")
            while not self.event:
                pass
            # ws.send(json.dumps(self.event,ensure_ascii=False))
            # self.event = {}
            # response_created = {
            #     "type":"response.create",
            #     "response": {
            #     "modalities":["text","audio"],
            #     # "instruction":"请满足用户的请求并帮助用户。"
            #     }
            # }
            # ws.send(json.dumps(response_created))

    def get_message_from_brain(self):
        return self.message_from_brain

    def create_interact_conversation(self,ws,output):
        self.idx = self.idx+1
        # msg_list = copy.deepcopy(self.message_from_brain)
        self.message_from_brain = self.get_message_from_brain()
        msg_list = copy.deepcopy(self.message_from_brain)
        logger.info(msg_list)
        for msg in msg_list:
            if '.wav' not in msg["tts"]:
                text = f"为了解决之前的复杂任务{json.loads(output['arguments'])['instruction']}，你通过调用慢思考工具进行了仔细思考，收到慢思考工具返回的信息为：{msg['tts']}，请播报返回的信息。"
            if 'recv' in msg["tts"]:
                text = "收到任务指令，请决定是否播报：“收到指令”。注意如果上文已经告知用户收到指令，则不重复告知。",
            if 'finish' in msg["tts"]:
                return
                # text = "任务指令完成，请在合适的时候告知用户：“任务已完成”。"

            data_to_broadcast = {
                "event_id":f"event_{self.idx}",
                "type": "conversation.item.create",
                "previous_item_id":None,
                "item": {
                    "id": f"msg_from_brain_{self.idx}",
                    "type": "message",
                    "role":"user",
                    "content":[{
                        "type":"input_text",
                        "text":f"{text}",
                    }]
                }}
            logger.info("send brain output back to gpt4o realtime")
            ws.send(json.dumps(data_to_broadcast, ensure_ascii=False))
            #需要再send一个response.create
            self.message_from_brain.remove(msg)
            # self.message_from_brain = []
            response_created = {
                                "type":"response.create",
                                "response": {
                                "modalities":["text","audio"],
                                }
                            }
            ws.send(json.dumps(response_created))

    def brain_function_call(self, output, ws):
        thread_func_call = threading.Thread(target=self.function_call_callback, args=(output,ws,))
        thread_func_call.start()
        out = copy.deepcopy(output)
        while True:
            message_back = self.local_server.get_message_out()
            # self.function_call_callback(output)
            if message_back:
                self.message_from_brain.extend(message_back)
                message_back = self.local_server.get_message_out()
                logger.info(f"message from brain:{self.message_from_brain}")
                self.create_interact_conversation(ws,output)

            self.output_for_brain_thinking = out["arguments"]
            if self.output_for_brain_thinking:
                logger.info(f"message sent to brain:{self.output_for_brain_thinking}")
                if self.output_for_brain_thinking and self.output_for_brain_thinking != 'None':
                    self.local_server.broadcast_voice(self.output_for_brain_thinking)
                out["arguments"] = None

    def function_call_callback(self, output, ws):
        item_id = output["id"]
        self.bot_backend.update_item_id(item_id)
        function_name = output["name"]
        self.bot_backend.update_function_name(function_name)
        call_id = output["call_id"]
        self.bot_backend.update_function_id(call_id)
        arguments = output["arguments"]
        self.bot_backend.update_args(arguments)
        self.bot_backend.update_finish_reason("function_call")
        result = self.bot_backend.run_function()
        logger.info(json.dumps(result, ensure_ascii=False))
        self.idx = self.idx + 1
        self.event = {"event_id":f"event_{self.idx}",
                    "type": "conversation.item.create",
                    "previous_item_id":None,
                    "item": {
                        "id": f"msg_from_brain_{self.idx}",
                        "type": "message",
                        "role":"user",
                        "content":[{
                            "type":"input_text",
                            "text":f"为了解决之前的任务,你调用了快思考工具{output['name']}，工具返回的信息是{result['answer']}，请播报返回的信息,请返回audio。",
                        }]
                    }}
        ws.send(json.dumps(self.event,ensure_ascii=False))
        self.event = {}
        response_created = {
            "type":"response.create",
            "response": {
            "modalities":["text","audio"],
            # "instruction":"请满足用户的请求并帮助用户。"
            }
        }
        ws.send(json.dumps(response_created))

    def text_input():
        """transform text input for use"""
        pass
        
    def img_input():
        """transform image input for use"""
        pass
        
    def video_input():
        """use video input"""
        pass

class BotBackend:
    def __init__(self, available_tools=None,tools=None):
        self.function_name = ""
        self.args = ""
        self.function_id = ""
        self.item_id = ""
        self.finish_reason = ""
        self.content = ""
        self.available_tools = available_tools
        self.tools = tools

    def update_function_name(self, function_name):
        self.function_name = function_name

    def update_item_id(self, item_id):
        self.item_id = item_id

    def update_args(self, args):
        self.args = args

    def update_function_id(self, function_id):
        self.function_id = function_id

    def update_finish_reason(self, finish_reason):
        self.finish_reason = finish_reason

    def update_content(self, content):
        self.content += content

    def reset_gpt_response_log_values(self, exclude=None):
        if exclude is None:
            exclude = []

        attributes = {
            "function_name": "",
            "args": "",
            "function_id": "",
            "item_id": "",
            "content": "",
        }

        for attr_name in exclude:
            del attributes[attr_name]
        for attr_name, value in attributes.items():
            setattr(self, attr_name, value)

    def run_function(self):
        args = json.loads(self.args)
        schema = self.available_tools
        cnt = 0
        for s in schema:
            if self.function_name == s['name']:
                idx = cnt
                break
            cnt+=1
        tool_function = self.tools[idx]
        if isinstance(tool_function, BaseTool):
            result = tool_function.invoke(input=args)
        else:
            result = tool_function(**args)

        return result
