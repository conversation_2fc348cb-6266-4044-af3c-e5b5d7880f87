"""Define Base Agent Class.

Topology:
    XunFei's Voice Listener --(instruction)--> 
    BrainAgent --(Execution Workflow)--> 
    ControlAgent/NavigationAgent etc.
"""
import abc


class BaseAgent:
    """BaseAgent 约束了所有类型Agent的推理接口 `infer` 。"""
    def __init__(self, name = None) -> None:
        self.name = self.__class__.__name__ if name is None else name
    
    @abc.abstractmethod    
    def infer(self, *args, **kwargs):
        """推理一次。
        
        Returns:
            result_dict (dict): 是一个dict，包含以下必要字段：
                answer (str): 回复文本
        """
        pass
    
    def start(self):
        pass
    
    def stop(self):
        pass
    
    # def _parse_result(self, result_dict, *args, **kwargs):
    #     """解析子Agent的infer()函数返回的结果，然后给出当前Agent的回复。
        
    #     Args:
    #         result_dict (dict): 是一个dict，包含以下必要字段：
    #             answer (str): 回复文本
        
    #     Returns:
    #         answer (str): 回复文本
    #     """
    #     assert isinstance(result_dict, dict) and "answer" in result_dict, \
    #         f"{self.name}: result_dict应该是一个字典且包含answer字段: {result_dict}"
    #     return result_dict["answer"]


class BaseChatAgent(BaseAgent):
    """BaseAgent 明确了推理输入参数 instruction 。"""
    
    @abc.abstractmethod    
    def infer(self, instruction, *args, **kwargs):
        pass


class BaseControlAgent(BaseAgent):
    """BaseControlAgent 明确了天工控制Agent的推理输入参数 instruction。"""
    
    @abc.abstractmethod
    def infer(self, instruction, *args, **kwargs):
        pass
