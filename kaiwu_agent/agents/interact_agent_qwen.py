import threading
import pyaudio
import queue
import requests
import time
from dotenv import load_dotenv
import logging
import copy
import os
import uvicorn
from fastapi import FastAPI
import numpy as np
from pydantic import BaseModel
from kaiwu_agent.agents.base import BaseAgent
from kaiwu_agent.agents.brain_agent import BaseBrainAgent
from langchain_core.tools import BaseTool
from kaiwu_agent.tools.interact_agent_server import LocalVoiceSocketServer
from kaiwu_agent.agents.brain_agent import BaseBrainAgent
from kaiwu_agent.tools.get_xunfei_sound_tools import SocketReceive
from kaiwu_agent.configs.config import QWENOMNI_SERVER_IP, QWENOMNI_SERVER_PORT,LOCAL_IP,LOCAL_PORT
# from kaiwu_agent.configs.interact_agent import role_definition_qwen

logger = logging.getLogger()
logger.setLevel(level = logging.INFO)

load_dotenv()

class InteractAgentV3(BaseAgent):
# class InteractAgentV3(BaseBrainAgent):
    def __init__(self,
                 role_definition,
                 audio_config,
                 api_key,
                 ws_url,
                 brain_agent:BaseBrainAgent,
                 tools=None,
                 name=None,
                 xfei=False,
                 server_ip='***********',
                 port=9080,
                 url=f"http://{QWENOMNI_SERVER_IP}:{QWENOMNI_SERVER_PORT}/qwen_output",
                 url_recv=f"http://{LOCAL_IP}:{LOCAL_PORT}/recv_orin",
                 url_post=f"http://{QWENOMNI_SERVER_IP}:{QWENOMNI_SERVER_PORT}/monitor_interruption"):
        super().__init__(name=name)

        self.audio_config = audio_config
        self.audio_buffer = bytearray()
        self.mic_queue = queue.Queue()
        self.mic_queue_label = queue.Queue()

        self.mic_on_at = 0
        self.mic_active = None
        self.stop_event = threading.Event()

        self.tools = tools
        self.api_key = api_key
        self.ws_url = ws_url
        self.cnt_brain_thinking = 0
        self.message_from_brain = []

        self.local_server = LocalVoiceSocketServer()
        self.idx = 0
        self.brain_agent = brain_agent
        self.event = {}
        self.brain_init = True

        self.mic_stream = None
        self.spkr_stream = None
        self.p = None
        self.use_xfei = xfei
        self.last_assistant_item = None
        
        self.interrupt_flag = False
        self.url = url
        self.urll = url_post
        self.url_recv = url_recv
        # 交互历史，由服务器端维护，如果用户想获取，单独开一个通信接口
        self.message_queue = queue.Queue()
        self.stop_speaking = False
        # vad = 0 静音/ = 1 开始说话/ =2 保持说话 / =3 停止说话
        self.VAD = 0
        self.app = FastAPI()
        self.id = 0
        self.tool_id = 30000
        self.role_definition = role_definition
        self.request_id = None

        if self.use_xfei:
            try:
                self.socket = SocketReceive(server_ip=server_ip,port=port)
            except Exception as err:
                logger.error(f"connecting xunfei server failed, failure reason {err}")
        
        threading.Thread(target=self.local_recv_server,daemon=True).start()
    
    def local_recv_server(self):
        self.recv_audio_and_text()
        uvicorn.run(self.app,host="0.0.0.0",port=8878)
    
    def start(self):
        self.p = pyaudio.PyAudio()
        if not self.use_xfei:
            # use pyaudio mic input
            audio_config_input = copy.deepcopy(self.audio_config)
            self.mic_stream = self.audio_input(audio_config_input, self.p)
            self.mic_stream.start_stream()
        else:
            xfei_thread = threading.Thread(target=self.get_audio_from_xfei_server)
            xfei_thread.start()

        audio_config_output = copy.deepcopy(self.audio_config)
        self.spkr_stream = self.audio_output(audio_config_output, self.p)
        self.spkr_stream.start_stream()

        self.infer()

    def stop(self):
        if self.mic_stream and self.spkr_stream and self.p:
            if not self.use_xfei:
                self.mic_stream.stop_stream()
                self.mic_stream.close()
            else:
                self.socket.close()
                # super().stop()

            self.spkr_stream.stop_stream()
            self.spkr_stream.close()

            self.p.terminate()
            logger.info('Audio streams stopped and resources released. Exiting.')

    def get_audio_from_xfei_server(self):
        id = 1
        while not self.stop_event.is_set():
            audio_chunk, VAD = self.socket.process()
            self.VAD = VAD
            if VAD == 1:
                self.interrupt_flag = True
                self.stop_speaking = False
                self.id = id
                # urll = f"http://{QWENOMNI_SERVER_IP}:{QWENOMNI_SERVER_PORT}/monitor_interruption"
                if not self.request_id:
                    self.request_id=""
                rsp = requests.post(self.urll,json={"data":"stop","id":self.request_id})
                logger.info(rsp.json())
                # TODO:实际测一下，看下面这个post是否必要。
                rsq_new_dialogue = requests.post(self.urll,json={"data":"start","id":""})
                logger.info(rsq_new_dialogue.json())
            if VAD == 3:
                self.stop_speaking = True
                id += 1
            if audio_chunk:
                self.mic_queue.put(audio_chunk)
                self.mic_queue_label.put(self.id)

    def init_session(self,url):
        messages =[{"role":"system","content":[{"type":"text","text":self.role_definition}]},
                   {"role":"user","content":[{"type":"text","text":"你好，天工。"}]}]
        msg = requests.post(url,json={"start":True, "message":messages,"cmd":"completed","VAD":0,"id":0})
        logger.info(msg.json())
        logger.info("init session success")

    def infer(self):
        try:
            self.init_session(self.url)
                 
            mic_thread = threading.Thread(target=self.send_mic_audio_fastapi)
            mic_thread.start()

            # spkr_thread = threading.Thread(target=self.recv_audio_and_text)
            # spkr_thread.start()
            
            self.local_server.start()

            # Wait for stop_event to be set
            # while not self.stop_event.is_set():
            #     time.sleep(0.1)

            if not self.use_xfei:
                while self.mic_stream.is_active() and self.spkr_stream.is_active():
                    time.sleep(0.1)

        except KeyboardInterrupt:
            logger.info('Gracefully shutting down...')
            self.stop_event.set()

        finally:
            self.stop()

    def audio_output(self, audio_config, p):
        def _spkr_callback(in_data, frame_count, time_info, status):

            if self.interrupt_flag:
                self.audio_buffer.clear()
                self.interrupt_flag = False
    
            bytes_needed = frame_count * 2
            current_buffer_size = len(self.audio_buffer)
            
            if current_buffer_size >= bytes_needed:
                audio_chunk = bytes(self.audio_buffer[:bytes_needed])
                self.audio_buffer = self.audio_buffer[bytes_needed:]
                self.mic_on_at = time.time() + audio_config['REENGAGE_DELAY_MS'] / 1000
            else:
                audio_chunk = bytes(self.audio_buffer) + b'\x00' * (bytes_needed - current_buffer_size)
                self.audio_buffer.clear()

            if self.interrupt_flag:
                self.audio_buffer.clear()
                self.interrupt_flag = False
                audio_chunk = bytes(self.audio_buffer) + b'\x00' * (bytes_needed - current_buffer_size)
            
            return (audio_chunk, pyaudio.paContinue)

        spkr_stream = p.open(
            format=audio_config['FORMAT'],
            channels=1,
            rate=audio_config['RATE'],
            output=True,
            stream_callback=_spkr_callback,
            frames_per_buffer=audio_config['CHUNK_SIZE']
        )
        return spkr_stream
    
    def audio_input(self, audio_config, p):
        
        def _mic_callback(in_data, frame_count, time_info, status):

            if time.time() > self.mic_on_at:
                if self.mic_active != True:
                    logger.info('🎙️🟢 Mic active')
                    self.mic_active = True
                self.mic_queue.put(in_data)
                self.mic_queue_label.put(self.id)
            else:
                if self.mic_active != False:
                    logger.info('🎙️🔴 Mic suppressed')
                    self.mic_active = False                   

            return (None, pyaudio.paContinue)

        mic_stream = p.open(
            format=audio_config['FORMAT'],
            channels=1,
            rate=audio_config['RATE'],
            input=True,
            stream_callback=_mic_callback,
            frames_per_buffer=audio_config['CHUNK_SIZE']
        )
        
        return mic_stream

    def send_mic_audio_fastapi(self):
        next_lbl = 0
        try:
            while not self.stop_event.is_set():
                if not self.mic_queue.empty():
                    mic_chunk = self.mic_queue.get()
                    # initial value next_lbl = 0, or new start for next_lbl
                    if next_lbl == 0:
                        mic_lbl = self.mic_queue_label.get()
                        if not self.mic_queue_label.empty():
                            next_lbl = self.mic_queue_label.get()
                        else:
                            # end of the audio
                            next_lbl = -1
                    logger.info(f'🎤 Sending {len(mic_chunk)} bytes of audio data.')
                    audio_data = np.frombuffer(mic_chunk, dtype=np.int16).astype(np.float64)
                    messages = {"role":"user","content":[{"type":"audio","audio":list(audio_data)}]}
                    msg_to_send = [messages]
                    response = None
                    if mic_lbl == next_lbl:
                        # 开始说话就开始传audio_chunk,服务器端拼接。
                        response = requests.post(self.url,json={"start":False,"message":msg_to_send,"cmd":"append","VAD":self.VAD,"id":mic_lbl})
                    else:
                        # 说话完成，completed，说话片段完整。开始推理。
                        response = requests.post(self.url, json={"start":False, "message":msg_to_send,"cmd":"completed","VAD":self.VAD,"id":mic_lbl})
                    if next_lbl != -1:
                        mic_lbl = copy.deepcopy(next_lbl)
                        if not self.mic_queue_label.empty():
                            next_lbl = self.mic_queue_label.get()
                        else:
                            next_lbl = -1
                    else:
                        next_lbl = 0
        except Exception as e:
            logger.error(f'Exception in send_mic_audio thread: {e}')
        finally:
            logger.info('Exiting send_mic_audio thread.')
    
    def recv_audio_and_text(self):

        def float64_to_int16_bytes(samples: np.ndarray) -> bytes:
            assert samples.dtype == np.float64
            clipped = np.clip(samples, -1.0, 1.0)
            int16_samples = (clipped * 32767).astype(np.int16)
            return int16_samples.tobytes()

        class Item(BaseModel):
            text:str
            audio:list
            id:str
        
        global idx
        idx=0
        @self.app.post("/recv_orin")    
        def recv_orin(item:Item):
            global idx
            text_response = item.text
            if not item.audio:
                logger.info(text_response)
            flag = self.parse_recv_text_and_func_call(text_response,item.id)
            if item.audio and flag == False:
                audio_buffer_used = float64_to_int16_bytes(np.asarray(item.audio))
                self.audio_buffer.extend(audio_buffer_used)
                self.request_id = item.id

    def parse_recv_text_and_func_call(self,text_messages,request_id):
        # msg = text_messages.split(";")
        msg = text_messages.split("\n")
        tool_key = None
        param = None
        flag = False
        for m in msg:
            if "调用查天气" in m:
                flag = True
                # requests.post(self.urll,json={"data":"stop","id":request_id})
                tool_key = "get_weather"
                if '，' in m:
                    param_value = m.split("，")[-1]
                    if "：" in param_value:
                        param_value = param_value.split("：")[-1]
                else:
                    param_value = ''
                param = {'query':param_value}
                print(param)
            elif "调用查时间" in m:
                # requests.post(self.urll,json={"data":"stop","id":request_id})
                flag = True
                tool_key = "get_time"
                param = {}
            elif "调用谷歌搜索" in m:
                # requests.post(self.urll,json={"data":"stop","id":request_id})
                flag = True
                tool_key = "google_search"
                param_value = m.split("，")[-1]
                param_value = param_value.split("：")[-1]
                param = {'query':param_value}
            elif "目前还不能" in m or "目前无法" in m or "目前不能" in m or "调用深度思考工具" in m:
                if "目前还不能" in m or "目前无法" in m or "目前不能" in m:
                    requests.post(self.urll,json={"data":"stop","id":request_id})
                tool_key = "brain_thinking"
                flag = True
                param_value = m.split('：')[-1]
                param = {'instruction':param_value,'intent':''}
                if self.brain_init or os.environ.get("KAIWU_AGENT_STOP") == "1":
                    start_thread = threading.Thread(target=self.brain_agent.start)
                    start_thread.start()
                    self.brain_init=False
                brain_think_thread = threading.Thread(target=self.brain_function_call, args=(tool_key,param,))
                brain_think_thread.start()
                continue

            if tool_key:
                execute_func=threading.Thread(target=self.run_function_and_send_result,args=(tool_key,param,))
                execute_func.start()

        return flag
    
    
    def run_function_and_send_result(self,tool_key,param):
        result = self.run_function(tool_key,param,self.tools)
        if result['answer']:
            answer_str = result['answer'].replace("\n", ',')
            if answer_str and answer_str[-1] == ",":
                answer_str = answer_str[:-1]
            response = self.send_msg(answer_str)

    def run_function(self, func_name, argument, all_tools):
        schema = all_tools
        cnt = 0
        for s in schema:
            if func_name == s.name:
                idx = cnt
                break
            cnt += 1
        tool_function =all_tools[idx]
        if isinstance(tool_function, BaseTool):
            result = tool_function.invoke(input=argument)
        else:
            result = tool_function(**argument)

        logger.info(result)          
        return result

    def brain_function_call(self, fn, arg):
        thread_func_call = threading.Thread(target=self.run_function, args=(fn,arg,self.tools,))
        thread_func_call.start()
        while os.environ.get("KAIWU_AGENT_STOP")=="0" and not self.stop_event.is_set():
            message_back = self.local_server.get_message_out()
            if message_back:
                self.message_from_brain.extend(message_back)
                message_back = self.local_server.get_message_out()
                logger.info(f"message from brain:{self.message_from_brain}")
                for item in self.message_from_brain:
                    self.send_msg(item["tts"])
                self.message_from_brain = []
            self.output_for_brain_thinking = arg
            if self.output_for_brain_thinking:
                logger.info(f"message sent to brain:{self.output_for_brain_thinking}")
                if self.output_for_brain_thinking and self.output_for_brain_thinking != 'None':
                    self.local_server.broadcast_voice(self.output_for_brain_thinking)
                arg = None
    
    def send_msg(self,message):
        idx = copy.deepcopy(self.tool_id)
        self.tool_id += 1
        messages = [{"role":"user","content":[{"type":"text","text":f"你调用了工具去解决用户的问题。请播报以下调用工具返回的结果:{message}"}]}]
        response = requests.post(self.url,json={"start":False,"message":messages,"cmd":"completed", "VAD":0,"id":idx})
        return response
    
    def send_rebroadcast(self,message,id):
        messages = [{"role":"user","content":[{"type":"text","text":f"请你重新播报以下内容：{message}"}]}]
        response = requests.post(self.url,json={"start":False,"message":messages,"cmd":"completed", "VAD":0,"id":id})