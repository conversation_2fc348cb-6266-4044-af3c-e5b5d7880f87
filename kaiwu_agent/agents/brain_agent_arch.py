import random
from typing import Union
from algo_tools.messages.msg_client import BaseMsgClient
from kaiwu_agent.agents.base import BaseChatAgent
from kaiwu_agent.agents.control_agent import TGControlAgentV1
from kaiwu_agent.agents.brain_agent import BaseBrainAgent
import logging
logger = logging.getLogger(__name__)


class TGBrainAgentV1(BaseBrainAgent):
    """天工大脑Agent V1，WRC2024 820展会版本，所有意图都透传到控制侧，Allen的VLM模型负责处理。
    """
    def __init__(self, 
                 msg_client: BaseMsgClient,
                 control_agent: TGControlAgentV1,
                 wake_response: Union[str, list] = ["你好，有什么可以帮您？", "你好，请说"],
                 accept_msg_when_busy = False,
                 name = None,
                 ):
        super().__init__(msg_client=msg_client, name=name, accept_msg_when_busy=accept_msg_when_busy)
        assert isinstance(control_agent, TGControlAgentV1), type(control_agent)
        self.control_agent = control_agent
        self.wake_response = [wake_response] if isinstance(wake_response, str) else wake_response
        
        self._request_instruction = False
       
    def infer(self, instruction, intent, **kwargs):
        # TODO: 少于2个字的不处理
        # if intent == "other" and len(instruction) < 2:
        #     logger.info(f"{self.name}: 用户指令少于2个字不处理：{instruction}")
        #     return None
        
        # 执行并播报结果
        if self._request_instruction or \
            intent in ("prepare_breakfast", "greeting", "introduce_qiaohong", "self_introduce", 
                      "introduce_company", "scene_desc", "grasp_object", 
                      "place_object", "pick_and_place", "release_hand", "other"):
            # 1. 自定义的讯飞技能
            # trick: 模型侧统一称为manipulation
            if intent in ("grasp_object", "place_object", "pick_and_place"):
                intent = "manipulation"
            
            result_dict = self.control_agent.infer(instruction, skill=intent)
            answer = result_dict["answer"]
            self._request_instruction = result_dict.get("request_instruction", False)
        elif intent in ("QUERY", "WHATTIME"):
            # 2. 讯飞官方技能
            answer = kwargs.get("answer", None)
        elif intent == "WAKEUP":
            # 3. 触发唤醒词“天工天工”
            answer = random.choice(self.wake_response)
        else:
            answer = None
            logger.error(f"{self.name}: unknown intent: {intent}")
        
        return {"answer": answer}
    

class TGBrainAgentV2(BaseBrainAgent):
    """天工大脑Agent V2，相比V1，v2多了chat_agent。
    manipulation和scene_desc两个意图透传到control_agent，其余透传到chat_agent。
    """
    def __init__(self,
                 msg_client: BaseMsgClient,
                 intent_agent: BaseChatAgent,
                 control_agent: TGControlAgentV1, 
                 control_intents: list,
                 chat_agent: BaseChatAgent,
                 chat_intents: list,
                 other_intents: list,
                 accept_msg_when_busy = False,
                 name = None):
        super().__init__(msg_client=msg_client, name=name, accept_msg_when_busy=accept_msg_when_busy)
        assert isinstance(intent_agent, BaseChatAgent), type(control_agent)
        assert isinstance(control_agent, TGControlAgentV1), type(control_agent)
        assert isinstance(chat_agent, BaseChatAgent), type(chat_agent)
        
        self.intent_agent = intent_agent
        self.control_agent = control_agent
        self.control_intents = control_intents
        self.chat_agent = chat_agent
        self.chat_intents = chat_intents
        self.other_intents = other_intents
    
    def infer(self, instruction, intent):
        # 意图识别
        if intent is None:
            result_dict = self.intent_agent.infer(instruction)
            intent = result_dict["answer"].strip()
        
        # 执行意图
        if intent in self.control_intents:
            logger.info(f"{self.name}: 意图: {intent}")
            result_dict = self.control_agent.infer(instruction, skill=intent)
            answer = result_dict["answer"]
        elif intent in self.chat_intents:
            logger.info(f"{self.name}: 意图: {intent}")
            result_dict = self.chat_agent.infer(instruction)
            answer = result_dict["answer"]
        elif intent in self.other_intents:
            answer = None
            logger.info(f"{self.name}: 意图: {intent}, 判断为背景音或非自然语言，不做任何回复")
        else:    
            # 意图识别失败，是模型问题
            answer = None
            logger.warning(f"{self.name}: 非法意图: {intent}, 不在已知意图列表中，不做任何回复")
        
        return {"answer": answer}
