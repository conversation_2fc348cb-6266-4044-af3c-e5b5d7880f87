import re
import os
import json
import time
import requests
import traceback
from openai import OpenAI
from datetime import datetime
from typing import Callable, List, Optional, Tuple
from langchain_core.tools import BaseTool
from algo_tools.utils import CYAN, RESET
from algo_tools.utils.common import encode_image
from kaiwu_agent.agents.base import BaseChatAgent
from kaiwu_agent.utils.orbbec_camera import OrbbecCameraClient
import logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class DifyChatAgent(BaseChatAgent):
    """Chat Agent from Dify.
    status_code 200
    ok True
    'data: {"event": "error", "conversation_id": "f48a5cf7-81b2-4a31-b6c5-11e88a6b7abb", 
            "message_id": "7ac33999-3121-476d-983b-43a78bc3a93d", "created_at": 1723113952, 
            "code": "completion_request_error", "status": 400, "message": "[openai] Connection Error, Connection error."}\n\n
    data: {"event": "tts_message_end", "conversation_id": "f48a5cf7-81b2-4a31-b6c5-11e88a6b7abb", 
        "message_id": "7ac33999-3121-476d-983b-43a78bc3a93d", "created_at": 1723113952, 
        "task_id": "92a9642c-53e3-44c1-96f4-a61f59747f5d", "audio": ""}\n\n'

    {"code": "invalid_param", "message": "Agent Chat App does not support blocking mode", "status": 400}
    """
    def __init__(self, token, url = 'http://10.11.15.1/v1/chat-messages', name = None):
        super().__init__(name=name)
        self.token = token
        self.url = url
        self.conversation_id = ""
    
    def infer(self, instruction):
        headers = {
            'Authorization': f'Bearer {self.token}',
            'Content-Type': 'application/json',
        }
        data = {
            "inputs": {},
            "query": instruction,
            # "response_mode": "streaming",
            "conversation_id": self.conversation_id,
            "user": "InteractionAgent"
        }
        
        start_time = time.time()
        response = requests.post(self.url, headers=headers, data=json.dumps(data))
        cost = (time.time() - start_time) * 1000
        
        # logger.info(response.text)
        answer = None
        try:
            resp_json = response.json()
            if 'answer' in resp_json:
                answer = resp_json["answer"]
            else:
                logger.warning(f"{self.name}: 警告，没有返回answer字段: {response.text}")    
        except:
            logger.info(f"response: {response}")
            logger.error(f"{self.name}: {traceback.format_exc()}")
        
        if "conversation_id" in resp_json:
            # 基于之前的聊天记录继续对话
            self.conversation_id = resp_json["conversation_id"]
        
        logger.info(CYAN + f"{self.name}: answer={answer}, cost={cost:.1f}ms" + RESET)    
        return {"answer": answer} 


class OpenAIStyleChatAgent(BaseChatAgent):
    """Chat Agent through `openai.OpenAI.chat.completions`."""
    def __init__(self,
                 model, 
                 api_key,
                 base_url,
                 system_prompt="You are a helpful assistant.",
                 name = None):
        super().__init__(name=name)
        self.sys_prompt = system_prompt
        self.model = model
        self.api_key = api_key
        self.base_url = base_url
        self.client = OpenAI(
            api_key=api_key,
            base_url=base_url
        )
        
    def infer(self, instruction):
        answer = None
        start_time = time.time()
        # TODO: 加上历史记录
        
        try:
            response = self.client.chat.completions.create(
                # model="Qwen/Qwen2-7B-Instruct",
                model=self.model,
                messages=[
                    # {"role": "system", "content": "You are a helpful assistant."},
                    # {"role": "user", "content": "Tell me something about large language models."},
                    {"role": "system", "content": self.sys_prompt},
                    {"role": "user", "content": instruction},
                ]
            )
            # 解析结果
            answer = response.choices[0].message.content
        except ValueError as err:
            answer = f"{err}\n警告，返回内容格式不合法: {response.text}"
            logger.warning(f"{self.name}: {answer}")   
        except Exception as err:
            answer = f"{err}"
            logger.error(f"{self.name}: {answer}")
        
        cost = (time.time() - start_time) * 1000
        
        logger.info(CYAN + f"{self.name}: Answer={answer}, cost={cost:.1f}ms" + RESET)    
        return {"answer": answer} 


class ChatAgent(BaseChatAgent):
    def __init__(self,
                 name: str = None,
                 system_prompt: str = "You are a helpful assistant.",
                 model: str = None,
                 api_key: str = None,
                 api_base: str = None,
                 image_size: List[int] = None,
                 max_size: int = 1024*768,
                 tool_choice="auto",
                 tools: Optional[List[Callable]] = None,
                 function_to_schema: Callable = None,
                 parallel_tool_calls: bool = False,
                 max_tokens: int = 1024,
                 temperature: float = 0.35,
                 top_p: float = 0.9,
                 repetition_penalty: float = 1.05,
                 stop: List[str] = None,
                 keep_message_history: bool = True,
                 save_folder: str = None,
                 stream=True
                 ):
        super().__init__(name=name)
        self.system_prompt = system_prompt
        if self.system_prompt:
            self._initialize_messages()
        logger.info(CYAN + f"{self.name}: system_prompt={self.system_prompt}" + RESET)
        self.model = model
        self.api_key = api_key
        self.api_base = api_base
        self.image_size = image_size
        self.max_size = max_size
        self.temperature = temperature
        self.top_p = top_p
        self.repetition_penalty = repetition_penalty
        self.max_tokens = max_tokens
        self.stop = stop
        self.keep_message_history = keep_message_history
        self.save_folder = save_folder
        self.stream = stream
        if self.save_folder and not os.path.exists(save_folder):
            os.makedirs(save_folder)
        self.client = OpenAI(
            api_key=api_key,
            base_url=api_base
        )
        # Tools
        self.tool_choice = tool_choice
        self.tools_list = []
        self.executable_functions_list = {}
        self.parallel_tool_calls = parallel_tool_calls
        self.function_to_schema = function_to_schema
        if tools and function_to_schema:
            self._initialize_tools(tools)

    def _initialize_messages(self):
        self.messages = [{"role": "system", "content": self.system_prompt}]

    def _initialize_tools(self, tools: List[Callable]):
        for func in tools:
            self.add_tool(func, self.function_to_schema(func))

    def add_tool(self, func: Callable, schema=None):
        self.tools_list.append(schema)
        func_name = schema.get("function").get("name")
        self.executable_functions_list[func_name] = func
    
    def set_system_prompt(self, system_prompt):
        self.system_prompt = system_prompt
        self._initialize_messages()
        logger.info(CYAN + f"{self.name}: system_prompt={self.system_prompt}" + RESET)
    
    def get_system_prompt(self):
        return self.system_prompt

    def get_tools_list(self):
        return self.tools_list

    def run(self, messages):
        try:
            num_init_messages = len(messages)
            # Handle message history.
            if not self.keep_message_history:
                self._initialize_messages()
            self.messages.extend(messages)
            self.infer()
            return self.messages[(num_init_messages + 1):]
        except:
            logger.error(f"{self.name}: {traceback.format_exc()}")
    
    def process(self, text, image_path_list=None):
        if image_path_list:
            base64_image_list = [encode_image(image_path, new_size=self.image_size, max_size=self.max_size) for image_path in image_path_list]
            message = {
                    "role": "user",
                    "content": [{"type": "text", "text": text}]}
            for base64_image in base64_image_list:
                message["content"].append({
                        "type": "image_url",
                        "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}})
        else:
            message = {"role": "user", "content": text}
        self.messages.append(message)
        return message

    def chat(self, text, image_path_list=None):
        try:
            # Handle message history.
            if not self.keep_message_history:
                self._initialize_messages()
            start_time = time.time()
            user_message = self.process(text, image_path_list)
            cost = (time.time() - start_time) * 1000
            user_message = {"role": "user", "content": {"text": text, "image_path_list": image_path_list}}
            logger.info(CYAN + f"{self.name}: user_message={user_message}, cost={cost:.1f}ms" + RESET)
            return self.infer()
        except:
            logger.error(f"{self.name}: {traceback.format_exc()}")

    def infer(self):
        try:
            start_time = time.time()
            if self.tool_choice == 'auto' and len(self.tools_list) > 0:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=self.messages,
                    temperature=self.temperature,
                    top_p=self.top_p,
                    max_tokens=self.max_tokens,
                    extra_body={"repetition_penalty": self.repetition_penalty},
                    tool_choice="auto",
                    tools=self.tools_list,
                    parallel_tool_calls=self.parallel_tool_calls,
                    stop=self.stop or None,
                    stream=self.stream  # 添加流式参数
                    )
            else:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=self.messages,
                    temperature=self.temperature,
                    top_p=self.top_p,
                    max_tokens=self.max_tokens,
                    extra_body={"repetition_penalty": self.repetition_penalty},
                    stop=self.stop or None,
                    stream=self.stream  # 添加流式参数
                    )
            if self.stream:
                for chunk in response:
                    if chunk.choices[0].delta.content:
                        content = chunk.choices[0].delta.content
                        print(content, end='', flush=True)  # 追加打印流式内容
            message = response.choices[0].message
            self.messages.append(message.to_dict())
            cost = (time.time() - start_time) * 1000
            logger.info(CYAN + f"{self.name}: message={message}, cost={cost:.1f}ms" + RESET)
            if self.save_folder:
                self.save_data()
            return message
        except:
            logger.error(f"{self.name}: {traceback.format_exc()}")
        return None
    
    def save_data(self):
        # 获取当前日期和时间，并格式化为 YYYY-MM-DD HH:MM
        current_datetime = datetime.now().strftime("%Y-%m-%d %H-%M")
        # Create a filename with the date suffix
        filename = f"{self.name}_{self.model}_{current_datetime}.jsonl"
        # 打开文件并保存在实例变量中
        save_path = os.path.join(self.save_folder, filename)
        self.stream_file = open(save_path, "a", encoding="utf-8")
        json.dump(self.messages, self.stream_file, ensure_ascii=False, indent=4)
        self.stream_file.write('\n')
        # Immediately flush the data to disk
        self.stream_file.flush()
        self.stream_file.close()
        self.stream_file = None


class FunctionCallAgent(ChatAgent):
    def __init__(self,
                 recursive: bool = True,
                 text_world: bool = False,
                 tool_result_format: str = "\nObservation: {}",
                 paser: List[str] = ["<tool_call>", "</tool_call>"],
                 camera_client: OrbbecCameraClient = None,
                 *args, **kwargs
                 ):
        super().__init__(*args, **kwargs)
        self.recursive = recursive
        self.text_world = text_world
        self.tool_result_format = tool_result_format
        self.paser = paser
        self.camera_client = camera_client
        self.tool_call_id = None
        self.reply_func = None
        self.reply_func_name = None
        assert not self.paser or len(self.paser) < 3, f"The len of paser={paser} must be less than 3."

    def get_tool_call_id(self):
        return self.tool_call_id

    def add_reply_function(self, func, name):
        # 增加回复用户的函数
        self.reply_func = func
        self.reply_func_name = name

    def reply(self, response):
        # 调用回复工具函数以回复用户
        try:
            arguments = {"message": response, "wait": False}
            if isinstance(self.reply_func, BaseTool):
                result = self.reply_func.invoke(input=arguments)
            else:
                result = self.reply_func(**arguments)
            tool_result = {"name": self.reply_func_name, "result": result}
        except:
            logger.error(f"{self.name}: {traceback.format_exc()}")
            tool_result = {"name": self.reply_func_name, "result": traceback.format_exc()}
        tool_message = {
            "role": "tool",
            "content": self.tool_result_format.format(str(tool_result))
        }
        return tool_message
    
    def infer(self): 
        try:
            start_time = time.time()
            if self.tool_choice == 'auto' and len(self.tools_list) > 0:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=self.messages,
                    temperature=self.temperature,
                    top_p=self.top_p,
                    max_tokens=self.max_tokens,
                    extra_body={"repetition_penalty": self.repetition_penalty},
                    tool_choice="auto",
                    tools=self.tools_list,
                    parallel_tool_calls=self.parallel_tool_calls,
                    stop=self.stop or None,
                    stream=self.stream  # 添加流式参数
                    )
            else:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=self.messages,
                    temperature=self.temperature,
                    top_p=self.top_p,
                    max_tokens=self.max_tokens,
                    extra_body={"repetition_penalty": self.repetition_penalty},
                    stop=self.stop or None,
                    stream=self.stream  # 添加流式参数
                    )
            if self.stream:
                content = ""
                tool_calls = []
                tool_messages = []
                for parsed in stream_parser(response):
                    if parsed["type"] == "answer":
                        logger.info("用户回答: %s", parsed["data"])  # 立刻回复用户
                        if self.reply_func and self.reply_func_name:
                            self.reply(parsed["data"])
                    elif parsed["type"] == "tool_call":
                        logger.info("工具调用: %s", parsed["data"])  # 立刻调用工具
                        try:
                            tool_call = json.loads(parsed["data"])
                            tool_function_name = tool_call.get("name", "")
                            arguments = tool_call.get("arguments", {})
                            # 将工具调用函数添加到列表
                            tool_calls.append((tool_function_name, arguments))
                        except:
                            # 若解析失败，在工具消息中记录错误信息
                            logger.error(f"{self.name}: {traceback.format_exc()}")
                            tool_message = {
                                "role": "tool",
                                "content": self.tool_result_format.format(f"{parsed['data']}解析为json格式失败，请检查格式是否正确。")
                            }
                            tool_messages.append(tool_message)
                    elif parsed["type"] == "complete_response":
                        content = parsed["data"]
                cost = (time.time() - start_time) * 1000
                message = {"role": "assistant", "content": content}
                logger.info(CYAN + f"{self.name}: message={message}, cost={cost:.1f}ms" + RESET)
                self.messages.append(message)
                if tool_messages:
                    self.messages.extend(tool_messages)
                if tool_calls:
                    message = self.handle_paralle_tool_call(tool_calls)
            else:
                cost = (time.time() - start_time) * 1000
                message = response.choices[0].message
                logger.info(CYAN + f"{self.name}: message={message}, cost={cost:.1f}ms" + RESET)
                if self.tool_choice == 'auto' and len(self.tools_list) > 0:
                    self.messages.append(message.to_dict())
                else:
                    message_dict = message.to_dict()
                    # TODO: 尝试将tool_calls保留下来
                    if "tool_calls" in message_dict:
                        del message_dict["tool_calls"]
                    self.messages.append(message_dict)
                if message.tool_calls:
                    message = self.handle_tool_call(message)
                elif self.paser and self.paser[0] in message.content:
                    tool_calls = self.parse_tool_call(message.content)
                    if tool_calls:
                        message = self.handle_paralle_tool_call(tool_calls)
            if self.save_folder:
                self.save_data()
            return message
        except:
            logger.error(f"{self.name}: {traceback.format_exc()}")
        return None

    def parse_tool_call(self, content):
        try:
            tool_calls = []
            # 使用正则表达式提取多个工具内容
            if len(self.paser) == 2 and self.paser[0] in content and self.paser[1] in content:
                matches = re.findall(rf"{self.paser[0]}(.*?){self.paser[1]}", content)
            elif self.paser[0] in content:
                splits = content.split(self.paser[0])
                matches = [part.strip() for part in splits[1:] if part.strip()]
            else:
                return None
            # 使用json格式解析工具内容
            if matches:
                for match in matches:
                    try:
                        logger.info(f"提取工具结果：{match}")
                        tool_call = json.loads(match)
                        tool_function_name = tool_call.get("name", "")
                        arguments = tool_call.get("arguments", {})
                        # 将工具调用函数添加到列表
                        tool_calls.append((tool_function_name, arguments))
                    except:
                        # 若解析失败，在工具消息中记录错误信息
                        logger.error(f"{self.name}: {traceback.format_exc()}")
                        tool_result = {"tool_name": tool_function_name, "result": traceback.format_exc()}
                        tool_message = {
                            "role": "tool",
                            "content": self.tool_result_format.format(str(tool_result))
                        }
                        self.messages.append(tool_message)
                return tool_calls
        except:
            logger.error(f"{self.name}: {traceback.format_exc()}")
            tool_message = {
                "role": "tool",
                "content": self.tool_result_format.format(traceback.format_exc())
            }
            self.messages.append(tool_message)
            return tool_calls
            
    def handle_paralle_tool_call(self, tool_calls: List[Tuple[str, str]]):
        try:
            # 执行每个工具
            # 使用 sorted() 并定义自定义 key，将符合条件的字典排序到前面
            tool_calls = sorted(tool_calls, key=lambda item: 0 if item[0] == "interact_with_user" else 1)
            for tool_function_name, arguments in tool_calls:
                tool_message = self.function_call(tool_function_name, arguments)
                if "perception" in tool_function_name:
                    return self.perception()
            # Recur to continue the conversation with the tool result appended when recursive is True.
            if self.recursive:
                return self.infer()
            return tool_message
        except:
            logger.error(f"{self.name}: {traceback.format_exc()}")
            tool_message = {
                "role": "tool",
                "content": self.tool_result_format.format(traceback.format_exc())
            }
            self.messages.append(tool_message)
            return self.infer()

    def handle_tool_call(self, message):
        try:
            # determine if the response from the model includes a tool call.
            tool_calls = message.tool_calls
            # If true the model will return the name of the tool / function to call and the argument(s)
            tool_call_id = tool_calls[0].id
            self.tool_call_id = tool_call_id
            tool_function_name = tool_calls[0].function.name
            arguments = json.loads(tool_calls[0].function.arguments)
            tool_message = self.function_call(tool_function_name, arguments, tool_call_id)
            if "perception" in tool_function_name:
                return self.perception()
            # Recur to continue the conversation with the tool result appended when recursive is True.
            if self.recursive:  
                return self.infer()
            return tool_message
        except:
            logger.error(f"{self.name}: {traceback.format_exc()}")
            tool_message = {
                "role": "tool",
                "content": self.tool_result_format.format(traceback.format_exc())
            }
            self.messages.append(tool_message)
            return self.infer()

    def function_call(self, name, arguments, tool_call_id=None):
        if self.text_world:
            return self.infer()
        start_time = time.time()
        if tool_call_id:
            tool_message = {
                "role": "tool",
                "tool_call_id": tool_call_id,
                "name": name,
            }
        else:
            tool_message = {
                "role": "assistant",
            }
        # Check if the function exists in the tools dictionary
        if name in self.executable_functions_list:
            tool_function = self.executable_functions_list[name]
            # Execute the tool function
            if isinstance(tool_function, BaseTool):
                result = tool_function.invoke(input=arguments)
            else:
                result = tool_function(**arguments)
        elif not name:
            result = f"An error occurred while retrieving the tool name({name}) with arguments({arguments}). "
        else:
            result = f"Error: function {name} not registered with arguments({arguments})."
        
        tool_result = {"tool_name": name, "result": result}
        tool_message["content"] = self.tool_result_format.format(str(tool_result))
        self.messages.append(tool_message)
        cost = (time.time() - start_time) * 1000
        logger.info(CYAN + f"{self.name}: tool_message={tool_message}, cost={cost:.1f}ms" + RESET)
        return tool_message

    def perception(self):
        image_path, predictions = self.camera_client.get_realtime_det_imagepath(
            visualization=False, label=False, bbox=False)
        text = "Here is the image from the environment. Please continue with your task."
        if predictions != "[]":
            text += f"Prediction resuls in the image is: {predictions}"
        logger.info(CYAN + f"{self.name}: text={text}, image_path={image_path}" + RESET)
        if not isinstance(image_path, list):
            image_path = [image_path]
        if self.recursive:
            return self.chat(text, image_path)
        else:
            return self.process(text, image_path)


def extract_action(buffer):
    """
    提取 'Action' 后的完整 JSON 数据，支持嵌套的花括号。
    """
    action_pattern = re.compile(r"Action:\s*(\{.*)", re.DOTALL)  # 初步匹配 Action 和开始的 JSON 部分
    match = action_pattern.search(buffer)

    if match:
        action_data = match.group(1)  # 提取匹配到的 JSON 内容
        brace_count = 0
        start_idx = match.start(1)
        end_idx = match.end(1)

        # 遍历提取到的部分，手动处理括号嵌套
        for i in range(start_idx, len(buffer)):
            char = buffer[i]

            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1

            # 如果括号匹配完成，返回完整的 JSON 内容
            if brace_count == 0:
                end_idx = i + 1
                break
        
        # 返回完整的 JSON 数据
        return buffer[start_idx:end_idx].strip(), end_idx
    return None, None

def stream_parser(response_stream):
    buffer = ""
    complete_raw_response = ""  # 用于存储完整的原始响应字符串
    temp_answer_buffer = ""  # 用于暂时保存遇到的文本，直到遇到 tool_call 才返回

    for chunk in response_stream:
        if chunk.choices[0].delta.content:
            content = chunk.choices[0].delta.content
            buffer += content  # 累积字符流
            complete_raw_response += content  # 记录完整的原始响应
            print(content, end='', flush=True)  # 追加打印流式内容
        else:
            continue

        # 检查 buffer 中是否包含 Action
        if "Action:" in buffer:
            # 提取 Action 之前的文本作为 answer
            index = buffer.index("Action:")
            temp_answer_buffer += buffer[:index]

            # 返回当前的 answer
            if temp_answer_buffer.strip():
                yield {"type": "answer", "data": temp_answer_buffer.strip()}
                temp_answer_buffer = ""  # 清空暂存的 answer
                buffer = buffer[index:]

            # 提取 Action 后的 JSON 内容
            action_data, end_idx = extract_action(buffer)
            if action_data:
                # 解析并返回工具调用
                try:
                    tool_call_data = json.loads(action_data)
                    yield {"type": "tool_call", "data": action_data}  # 立即返回工具调用
                    buffer = buffer[end_idx:]
                except json.JSONDecodeError:
                    pass  # 如果解析失败，跳过
        else:
            # 如果没有 Action，继续积累文本内容
            temp_answer_buffer += buffer
            buffer = ""  # 清空 buffer，防止重复处理

    # 流结束后，返回剩余的 answer
    if temp_answer_buffer.strip():
        yield {"type": "answer", "data": temp_answer_buffer.strip()}

    # 在流结束时返回完整的 response 和原始数据
    yield {
        "type": "complete_response",
        "data": complete_raw_response
    }
