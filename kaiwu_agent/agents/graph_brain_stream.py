import time
from typing import Union
from langchain_core.messages import AIMessageChunk
from algo_tools.utils import CYAN, RESET
from algo_tools.messages.msg_client import BaseMsgClient
from kaiwu_agent.utils.kaiwu_message import KaiwuMessageClient
from kaiwu_agent.agents.graph_brain import GraphBrainAgent
from kaiwu_agent.graphs.base import GraphCreatorBase
from kaiwu_agent.utils.registry import AGENTS
from kaiwu_agent.utils.common import split_by_first_punctuation
import logging
logger = logging.getLogger(__name__)


@AGENTS.register_module()
class GraphBrainAgentStream(GraphBrainAgent):
    """Brain Agent uses CompiledGraph instance as executor and support streaming output."""
    def __init__(self,
                 msg_client: BaseMsgClient,
                 graph_creator: Union[dict, GraphCreatorBase],
                 system_prompt: str,
                 accept_msg_when_busy = False,
                 kaiwu_msg_client: KaiwuMessageClient = None,
                 max_concurrency: int = 1,
                 exit_keywords: list = None,
                 out_dir: str = "output/data/",
                 name = None,
                 recv_music_path="",
                 finish_music_path="",
                 agent_name: str = None, 
                 agent_uuid: str = None,
                 msg_separate_pattern: str = "，。；：？！,!?;: ",
                 msg_buffer_size: int = 15,
                 stream_send: bool = True):
        super().__init__(msg_client=msg_client,
                         graph_creator=graph_creator,
                         system_prompt=system_prompt,
                         accept_msg_when_busy=accept_msg_when_busy,
                         kaiwu_msg_client=kaiwu_msg_client,
                         max_concurrency=max_concurrency,
                         exit_keywords=exit_keywords,
                         out_dir=out_dir,
                         name=name,
                         recv_music_path=recv_music_path,
                         finish_music_path=finish_music_path,
                         agent_name=agent_name,
                         agent_uuid=agent_uuid)
        self.msg_buffer_size = msg_buffer_size
        self.stream_send = stream_send
        self.msg_separate_pattern = msg_separate_pattern
        self._punctuation_set = set(msg_separate_pattern)

    async def astream_output(self, graph_executor, inputs, config):
        msg_buffer = ""
        send_state = None
        
        try:
            async for stream_mode, chunk in graph_executor.astream(
                inputs, config=config, 
                # "values": 给出完整的messages(humanmessage, aimessage, toolmessage, aimessage ...)
                # "updates": 只给最新的部分
                stream_mode=["values", "messages", "custom"]
                # stream_mode=["updates", "messages", "custom"]
            ):
                # print(chunk)
                # print("\n")
                
                if isinstance(chunk, dict) and "messages" in chunk:
                    if msg_buffer != "" or send_state == "started":
                        # 结束上一个发送
                        await self.msg_client.send_message_chunk(text=msg_buffer, cmd="append", finish=True)
                        send_state = "finished"
                        msg_buffer = ""
                    
                    # 仅获取本轮更新的消息并打印
                    all_msgs = chunk["messages"]
                    start_idx = self._msg_idx if self._msg_idx < len(all_msgs) else len(all_msgs) - 1
                    update_msgs = all_msgs[start_idx:]
                    self._msg_idx = len(all_msgs)
                    self.print_messages(update_msgs)
                    
                elif isinstance(chunk, tuple) and isinstance(chunk[0], AIMessageChunk):
                    msg_chunk = chunk[0]
                    try:
                        text_chunk = msg_chunk.content
                        # print(text_chunk)
                        if self.stream_send:
                            res_meta = msg_chunk.response_metadata
                            if "finish_reason" in res_meta and res_meta["finish_reason"] == "stop" and send_state == "started":
                                msg_buffer += text_chunk
                                await self.msg_client.send_message_chunk(text=msg_buffer, cmd="append", finish=True)
                                send_state = "finished"
                                msg_buffer = ""
                            else:
                                # 手动分段发送
                                left, right, exist_punctuation = split_by_first_punctuation(text_chunk, self._punctuation_set)
                                # print(left, right, exist_punctuation)
                                msg_buffer += left
                                if exist_punctuation or len(msg_buffer) >= self.msg_buffer_size:
                                    await self.msg_client.send_message_chunk(text=msg_buffer, cmd="append", finish=False)
                                    send_state = "started"
                                    msg_buffer = ""
                                msg_buffer += right
                        else:
                            msg_buffer += text_chunk
                        
                    except Exception as err:
                        logger.warning(err)

            # 兜底，必须发一次 finish = True
            if msg_buffer != "" or send_state == "started":
                await self.msg_client.send_message_chunk(text=msg_buffer, cmd="append", finish=True)
                msg_buffer = ""
        except Exception as err:
            logger.error(err, exc_info=True)
        
    def print_messages(self, messages):
        for message in messages:
            # message类型：
            # Human Message（用户指令）
            # AI Message（LLM准备调工具，或者最终回答）
            # Tool Meaasge（工具结果）
            logger.info(message.pretty_repr())
            self.memory.append({'role': message.type,
                                'content': message.content,
                                "additional_kwargs": message.additional_kwargs,
                                })
                
            cost = (time.time() - self.time) * 1000
            self.time = time.time()
            logger.info(CYAN + f"cost={cost:.1f}ms" + RESET)
