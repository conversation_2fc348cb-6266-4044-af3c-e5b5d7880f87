from algo_tools.messages.msg_client import BaseMsgClient
from kaiwu_agent.agents.chat_agent import ChatAgent
from kaiwu_agent.agents.brain_agent import BaseBrainAgent
import logging
logger = logging.getLogger(__name__)


class VisualBrainAgent(BaseBrainAgent):
    """集多模态感知、任务规划、工具调用于单个VLM的BrainAgent。"""
    def __init__(self,
                 msg_client: BaseMsgClient,
                 agent_executor: ChatAgent,
                 accept_msg_when_busy = False,
                 name = 'VisualBrainAgent'):
        super().__init__(msg_client=msg_client, name=name, accept_msg_when_busy=accept_msg_when_busy)
        self.agent_executor = agent_executor

    def infer(self, instruction, intent):
        message = self.agent_executor.chat(instruction)
        return {"answer": None}
    
    def stop(self):
        super().stop()
