"""大脑Agent，负责多Agent的编排调度"""
import abc
import os
import asyncio
import json
import re
import traceback
from algo_tools.messages.msg_client import BaseMsgClient, FastAPIInstructListener
from kaiwu_agent.utils.common import contains_keywords, correct_instruction
from kaiwu_agent.utils.env import env_manager
from kaiwu_agent.agents.base import BaseAgent, BaseChatAgent
from kaiwu_agent.utils.kaiwu_message import KaiwuMessageClient
import logging
logger = logging.getLogger(__name__)


# 指令中包含这些关键词就会让brain停下来，但目前不能保证立刻停止所有动作
EXIT_KEYWORDS = [
    "立刻退出", "退出", "退出吧", 
    "立刻停止", "停下", "停下来", "停下了", "暂停", "停止",
    "立刻结束", "结束吧",
    "立刻终止",
    "闭嘴", "别说了", "别做了",
    "等一下", "请等等", "请等一下",  # "稍等", "等等", 
    "暂停一下", "不要说了", "不要说话了", "不要说话",
    "shut up", "stop"
]

def text_refine(instruction):
    system_prompt= "假设你是一个机器人，擅长判断文本逻辑通顺、指令明确的程度，并且擅长进行文本纠错和文本简化。可根据history辅助判断这条文本为背景音的可能程度。"
    instruct = "请判断输入文本的逻辑通顺程度。对于逻辑通顺、指令明确的文本直接输出原文，对存在瑕疵的文本输出文本纠错或文本简化后的文本，忽略可能为背景音的文本:"
    prompt = instruct + instruction
    refine_agent = BaseChatAgent(system_prompt=system_prompt,
                                        model="Small_LLM",
                                        api_key='EMPTY',
                                        api_base="http://10.0.3.31:30010/v1",
                                        temperature=0.1,
                                        top_p=0.8,
                                        name='refine_agent')
    message = refine_agent.chat(prompt)
    return message.content if message else instruction


class BaseBrainAgent(BaseAgent):
    """BrainAgent 跟在InteractionAgent之后，推理输入必须是 instruction 和 intent 。"""
    def __init__(self,
                 msg_client: BaseMsgClient,
                 display_client = None,
                 accept_msg_when_busy = False,
                 kaiwu_msg_client: KaiwuMessageClient = None,
                 exit_keywords: list = None,
                 name: str = None,
                 recv_music_path: str = "",
                 finish_music_path: str = "",
                 agent_name: str = None, 
                 agent_uuid: str = None):
        super().__init__(name=name)
        self.msg_client = msg_client
        self.msg_client.register_msg_preprocessor(self.preprocess_message)
        self.msg_client.register_callback(self.on_instruct_msg)
        self.display_client = display_client
        self.accept_msg_when_busy = accept_msg_when_busy
        self.kaiwu_msg_client = kaiwu_msg_client
        self.exit_keywords = exit_keywords if exit_keywords is not None else EXIT_KEYWORDS
        self.recv_music_path = recv_music_path
        self.finsish_music_path = finish_music_path
        self.agent_name = agent_name
        self.agent_uuid = agent_uuid
        
        self._running = True
    
    async def preprocess_message(self, msg_dict: dict):
        # TODO: 这是一个trick，目的是为了让controlagentv2.send_message()里拿到当前message
        logger.info(f"preprocess_message: msg_dict: {msg_dict}")
        # 判断用户是否要停止brain，用于机器人做错说错等要紧急停下的场景
        instruction = msg_dict["instruction"]
        intent = msg_dict.get("intent", None)
        # 指令中包含这些关键词就会让brain停下来，但目前不能保证立刻停止所有动作
        if contains_keywords(instruction, self.exit_keywords) or intent == "STOP_ALL":
            logger.info(f"触发停止关键词`{instruction}`或意图`{intent}`，正在停止brain agent ...")
            # 打断前面已经在播放的音频，静音
            # 不能用send_message，当前处于一个loop中，否则报错: loop is running
            if type(self.msg_client) != FastAPIInstructListener:
                self.msg_client.resume_send_message()
                await self.msg_client.send_message_once(self.finsish_music_path, text_type="file", cmd="stop")
            
            # 关闭tts发送功能，on_instruct_msg()里重新打开
            self.msg_client.pending_send_message()
            
            # 给开物云积木app发消息
            if self.kaiwu_msg_client is not None:
                self.kaiwu_msg_client.send(msg_dict={"role": "user", "message": instruction})
            
            self.stop_brain()
            # 返回None则代表要跳过这个消息
            msg_dict = None
            
            # 退出后，保证能接受新消息
            self.msg_client.message_monitor.resume()
        
        # 消息还得给回去
        # 只要message不是None，待会会从 on_instruct_msg 那边再次回调拿到。这么做事为了异步处理消息，不阻塞主进程
        return msg_dict

    def on_instruct_msg(self, message):
        logger.info(f"on_instruct_msg: {message}")
        
        self.msg_client.set_current_message(message)
        
        instruction, intent = message.pop("instruction"), message.pop("intent", None)
        
        instruction = correct_instruction(instruction)
        
        agent_id = message.get("agent_id", None)
        if agent_id:
            instruction = f"<agent>{agent_id}</agent>: {instruction}"
        
        # 恢复brain agent
        self.resume_brain()
        
        # 恢复发送消息的功能
        self.msg_client.resume_send_message()
        
        if type(self.msg_client) != FastAPIInstructListener:
            # 播放指令收到提示音
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            self.msg_client.send_message(self.recv_music_path, text_type="file", cmd="stop")
            
            # 给开物云积木app发消息
            if self.kaiwu_msg_client is not None:
                self.kaiwu_msg_client.send(msg_dict={"role": "user", "message": instruction})
            
        answer = None
        try:
            if not self.accept_msg_when_busy:
                # 标志位，brain_agent工作时，如果有新消息进来，丢弃掉，等结束
                self.msg_client.message_monitor.pending()
            # instruction = text_refine(instruction)
            # logger.info(f"refined instruction: {instruction}")
            # result_dict = self.infer(instruction=instruction, intent=intent, **msg_dict)
            result_dict = self.infer(instruction=instruction, intent=intent)
            answer = result_dict["answer"]
        except Exception as err:
            logger.error(f"instruction {instruction} encountered an error: {err}")
            logger.error(traceback.format_exc())
        finally:    
            # 处理结束，接受新的
            # 放finally：保证出现任何异常，标志位都会重置
            self.msg_client.message_monitor.resume()
        
        # 播报
        if answer is not None:
            self.send_robot_message(message=answer, cmd="append")
        
        # 任务完成音效
        if type(self.msg_client) != FastAPIInstructListener:
            self.msg_client.send_message(self.finsish_music_path, text_type="file", cmd="append")
        logger.info(f"instruction {instruction}: done")
        logger.info(f"================================================")
    
    def send_robot_message(self, message: str, cmd: str = None):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        self.msg_client.send_message(text=message, cmd=cmd)
        # 给开物云积木app发消息
        if self.kaiwu_msg_client is not None:
            self.kaiwu_msg_client.send(msg_dict={"role": "robot", "message": message})
    
    async def async_send_robot_message(self, message: str, task_status: str=None, cmd: str = None, steps: list = None, **kwargs):
        await self.msg_client.send_message_once(text=message, steps=steps, task_status=task_status, cmd=cmd, **kwargs)
        # 给开物云积木app发消息
        if self.kaiwu_msg_client is not None:
            self.kaiwu_msg_client.send(msg_dict={"role": "robot", "message": message})
                
    @abc.abstractmethod    
    def infer(self, instruction, intent: str = None, **kwargs):
        pass
    
    def stop_brain(self):
        # TODO: 各个地方监控这个变量以便及时中断，之后可以考虑更好的函数中断方法
        env_manager.set_env("STOP", val="1")
        
    def resume_brain(self):
        env_manager.unset_env("STOP")
    
    def start(self):
        self._running = True
        self.msg_client.start()
    
    def stop(self):
        self.msg_client.send_message("", cmd="stop")
        self._running = False
        self.msg_client.stop()
        self.stop_brain()
    
    async def display_message(self, message):
        if self.display_client is not None:
            await self.display_client.display_message(message)
        else:
            # 返回一个异步的空操作
            # await asyncio.sleep(0)  # 等效于一个异步的 no-op（无操作）
            return
