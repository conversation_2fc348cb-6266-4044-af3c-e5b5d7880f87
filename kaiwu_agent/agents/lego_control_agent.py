import asyncio
import threading
from typing import Tuple, Optional, List
import time
import httpx
import os
import queue
import uvicorn
from fastapi import FastAPI
from pydantic import BaseModel, Field
from kaiwu_agent.agents.base import BaseControlAgent
from kaiwu_agent.utils.env import env_manager
from algo_tools.messages.msg_client import BaseMsgClient, FastAPIInstructListener
from algo_tools.utils.common import get_time_stamp
from algo_tools.utils import CYAN, RESET
import logging
logger = logging.getLogger(__name__)


class LegoInfo(BaseModel):
    color: str = Field(description="""乐高的颜色，用英文表示""")

    shape: Tuple = Field(description="""乐高的形状(height, width)，高度和宽度""")

    position: Tuple = Field(
        description="""积木的坐标(x, y)，x是积木的层数（以最底层积木为第0层，向上为正），\
            y是积木左端点的列数（以最底层积木的左端点为第0列，向右为正）。""")
    
    reset: bool = Field(
        description="""是否重置的标志位。在每次搭建第一块积木时需要设置为True，让下游做相应准备，其余情况为False。""")


class LegoExaminerInfo(BaseModel):
    index: str = Field(description="""搭建乐高块的序列(index)，从0开始计算""")


class LegoPickPlace(BaseModel):
    task_list: List[LegoInfo] = Field(description="""乐高积木搭建任务序列。序列中每个元素是一次积木抓取和放置操作所需的信息，元素的顺序即乐高积木的搭建顺序。""")


class LegoControlCommand(BaseModel):
    Conversation_ID: int
    # Pick_Place: Optional[LegoInfo]
    Pick_Place: Optional[List[LegoInfo]]
    Scan: Optional[str]   # 转头参数，取值 {"example", "components", "all"}


class LegoControlResult(BaseModel):
    Conversation_ID: int
    State: str  # task state, including ["Running", "Succeed", "Failed"]
    Response: str
    

STATE_LIST = ("Succeed", "Failed", "Running")


class LegoControlAgent(BaseControlAgent):
    """Kaiwu lego control Agent, FastAPI version."""
    
    def __init__(self,
                 name: str = 'LegoControlAgent',
                 port: int = 9529,
                 server_url = "http://localhost:9528/v1/pick_place",
                 msg_client: BaseMsgClient = None,
                 log_recv: bool = True,
                 result_timeout: float = 240.0,
                 connect_timeout: float = 5.0,
                 recv_timeout: float = 10.0,
                 write_timeout: float = 10.0,
                 pool_timeout: float = 10.0):
        """
        初始化 Agent 类，设置服务器地址、名称和服务端口。
        :param name: 代理的名字（例如 Agent A 或 Agent B）
        :param port: 当前服务器的端口
        :param server_url: 对方服务器的 URL 地址
        :param msg_client: 语音客户端对象
        :param log_recv: 是否打印接收到的消息日志，默认为 True
        :param result_timeout: 接收结果超时设置，默认为 10 秒
        :param connect_timeout: 连接超时设置，默认为 5 秒
        :param recv_timeout: 接收响应超时设置，默认为 10 秒
        :param write_timeout: 写入超时设置，默认为 10 秒
        :param pool_timeout: 连接池超时设置，默认为 10 秒
        """
        self.name = name
        self.port = port
        self.server_url = server_url
        self.msg_client = msg_client
        self.log_recv = log_recv
        self.result_timeout = result_timeout
        self.connect_timeout = connect_timeout
        self.recv_timeout = recv_timeout
        self.write_timeout = write_timeout
        self.pool_timeout = pool_timeout
        self.message_queue = queue.Queue(maxsize=1)  # 创建一个队列用于消息通信
        self.control_state = {}
        self.current_task = ''
        
        self.app = FastAPI()
        self._initialize_routes()

        # 启动当前 Agent 的服务器
        threading.Thread(target=self._run_server, daemon=True).start()

    def _initialize_routes(self):
        """初始化路由，用于处理接收消息和响应"""
        @self.app.post("/lego/result")
        async def recv_result(message: LegoControlResult):
            # 处理接收到的消息并返回响应"""
            await self._handle_message(message)
            return {"code": 0, "message": "message received."}

    def _run_server(self):
        """启动 FastAPI 服务器"""
        uvicorn.run(self.app, host="0.0.0.0", port=self.port)

    async def _handle_message(self, message: LegoControlResult):
        """根据消息内容返回不同的响应"""
        if self.log_recv:
            logger.info(f"{self.name} recv_message: {message}")
        
        # 播报运行消息
        # !TODO: 实现由Agent自主决定是否播报，因为Agent可能边做动作边跟用户聊天
        # 只有运行的时候才播报，完成或者失败等结束情况由Agent决定播报
        # if message.State in ("Running") and self.msg_client and message.Response:
        #     await self.msg_client.send_message_once(text=message.Response)
        
        if message.State not in STATE_LIST:
            logger.error(f"message.State不合法, {message.State} NOT IN {STATE_LIST}")
        elif message.State in ("Succeed", "Failed"):
            self.message_queue.put(message)
            logger.info("message已放入队列")

    async def _send(self, command: LegoControlCommand):
        """发送消息并等待响应"""
        logger.info(f"{self.name} sending message: {command}")
        conversation_id = command.Conversation_ID
        try:
            # 设置所有四个超时参数
            timeout = httpx.Timeout(
                connect=self.connect_timeout,  # 连接超时
                read=self.recv_timeout,        # 接收超时
                write=self.write_timeout,      # 写入超时
                pool=self.pool_timeout         # 连接池超时
            )
            async with httpx.AsyncClient(timeout=timeout) as client:
                # 发送第一条消息
                response = await client.post(self.server_url, json=command.model_dump())
                recv_msg = f"操作指令发送成功，收到回复：{response.json()}"
                message = LegoControlResult(Conversation_ID=conversation_id,
                                            State="Succeed",
                                            Response=recv_msg)
                # control agent server 收到我方消息后立马向我方发送收到消息
                logger.info(f"{self.name}: {recv_msg}")
        except httpx.TimeoutException:
            err_msg = f"request timed out, failed to send message."
            logger.info(f"{self.name}: {err_msg}")
            message = LegoControlResult(Conversation_ID=conversation_id,
                                        State="Failed",
                                        Response=err_msg)
        except Exception as e:
            err_msg = f'encountered an error: {e}, failed to send message.'
            logger.info(f"{self.name}: {err_msg}")
            message = LegoControlResult(Conversation_ID=conversation_id,
                                        State="Failed",
                                        Response=err_msg)
        
        return message

    def _wait(self, conversation_id):
        start_time = time.time()
        try:
            while True:
                if env_manager.is_set("STOP"):
                    exit_msg = "收到系统退出指令，不再等待结果，退出"
                    message = LegoControlResult(Conversation_ID=conversation_id,
                                                State="Failed",
                                                Response=exit_msg)
                    logger.info(f"{self.name}: {exit_msg}")
                    break
                    
                try:
                    message = self.message_queue.get(timeout=1)
                    if message.Conversation_ID == conversation_id:
                        logger.info(f"{self.name}: received final message: {message}")
                        break
                    else:
                        logger.warning(f"警告，接收到非本次会话的消息，继续等待: receive {message.Conversation_ID}, but require {conversation_id}")
                except queue.Empty:
                    # 如果超过指定时间未收到消息
                    if self.result_timeout is not None and time.time() - start_time > self.result_timeout:
                        err_msg = "wait result from control agent server timeout."
                        message = LegoControlResult(Conversation_ID=conversation_id,
                                                    State="Failed",
                                                    Response=err_msg)
                        logger.warning(f"{self.name}: {err_msg}")
                        break    
        except Exception as err:
            err_msg = f"wait result from control agent server failed, error: {err}"
            message = LegoControlResult(Conversation_ID=conversation_id,
                                        State="Failed",
                                        Response=err_msg)
            logger.warning(f"{self.name}: {err_msg}")
        
        return message

    def infer(self, 
              pick_place: List[LegoInfo] = None, 
              scan_type: str = None,
              wait_result: bool = True):
        assert pick_place is not None or scan_type is not None
        start_time = time.time()
        
        # 获取当前线程的全局事件，不存在会自动创建
        loop = asyncio.get_event_loop()
        conversation_id = get_time_stamp()
        
        command = LegoControlCommand(Conversation_ID=conversation_id,
                                     Pick_Place=pick_place,
                                     Scan=scan_type)

        try:
            response = loop.run_until_complete(self._send(command))
            # 在主进程等待消息，尽量不在event loop里，避免阻塞概率
            # 仅当发送成功才等回复
            if wait_result and response.State == "Succeed":
                response = self._wait(conversation_id)
        except Exception as err:
            err_msg = f'send command to control agent server failed: {err}'
            logger.error(f'{self.name}: {err_msg}')
            response = LegoControlResult(Conversation_ID=conversation_id, 
                                         State="Failed", 
                                         Response=err_msg)

        cost = (time.time() - start_time) * 1000
        logger.info(CYAN + f"{self.name}.infer: result={response}, cost={cost:.1f}ms" + RESET)
        
        return response.model_dump()
