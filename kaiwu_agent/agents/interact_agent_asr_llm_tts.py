import threading
import os
import time
import json
from dotenv import load_dotenv
import logging
from langchain_core.tools import BaseTool
from algo_tools.utils import CYAN, RESET
from kaiwu_agent.tools.interact_agent_server import LocalVoiceSocketServer
from kaiwu_agent.agents.brain_agent import BaseBrainAgent
from kaiwu_agent.tools.get_xunfei_sound_tools import SocketReceive
from algo_tools.messages.msg_client import BaseMsgClient
from kaiwu_agent.utils.env import env_manager

logger = logging.getLogger()
logger.setLevel(level = logging.INFO)

load_dotenv()

class InteractAgentV2(BaseBrainAgent):
    def __init__(self,
                 brain_agent:BaseBrainAgent,
                 msg_client: BaseMsgClient,
                 agent_executor = None,
                 llm = None,
                 history = None,
                 all_tools=[],
                 name=None):
        super().__init__(msg_client=msg_client,name=name)

        self.cnt_brain_thinking = 0
        self.message_from_brain = []

        self.local_server = LocalVoiceSocketServer()
        self.idx = 0
        self.brain_agent = brain_agent
        self.event = {}
        self.brain_init = True
        self.llm = llm
        self.all_tools = all_tools
        
        self.history = history
        
        self.stop_identifier = False
        self.id_start = 123456

        self.time = time.time()
        try:
            self.socket = SocketReceive(server_ip='***********',port=9080)
        except Exception as err:
            logger.error(f"connecting xunfei server failed, failure reason {err}")

    def get_audio_from_xfei_server(self):
        while not self.stop_identifier:
            # VAD = 0静音片段/1开始说话/2持续说话/3结束说话
            _, VAD = self.socket.process()
            if VAD == 1:
                # self.speech_start = True
                self.send_robot_message(message="", cmd="stop")
        
    def start(self):
        # 跟brain agent一样的start，不停的收音频
        # interact_agent跟robot_voice的连接换一个端口 或者 interact_agent联brain_agent的时候换一个端口
        voice_thread = threading.Thread(target=super().start)
        voice_thread.start()
        self.local_server.start()
        supervision_voice = threading.Thread(target=self.get_audio_from_xfei_server)
        supervision_voice.start()

    def stop(self):
        # 跟brain agent一样的stop，停止ctrl+C
        self.stop_identifier = True
        super().stop()
        self.local_server.stop()

    def infer(self,instruction,intent):
        t_infer_start = time.time()
        inputs = [{"role":"user", "content":instruction}]
        self.history = self.history + inputs
        result = self.llm.invoke(self.history)
        result_text = None
        res_tool_call_text = []
        if result.content:
            if "<tool_call>" in result.content:
              response = result.content.replace("</tool_call>","<tool_call>")
              res_list = response.split("<tool_call>")
              logger.info(result.content)
              result_text = res_list[0]
              for res_item in res_list:
                  if "\"name\"" in res_item:
                      res_tool_call_text.append(res_item.strip())

            if not result_text:
                self.history.append({'role':"assistant",'content':result.content})
                self.send_robot_message(message=result.content, cmd="append")
            else:
                self.history.append({'role':"assistant",'content':result_text})
                self.send_robot_message(message=result_text, cmd="append")

        cost = time.time() - t_infer_start
        logger.info(CYAN + f"llm reasoning cost={cost:.1f}ms" + RESET)

        func_name = []
        args = []
        ids = []
        t_extract_function = time.time()
        func_name_item, args_item,id_item = self.extract_function(result)
        if not func_name_item:
            if res_tool_call_text:
                func_name_item, args_item,id_item = self.extract_from_text(res_tool_call_text)

        func_name.extend(func_name_item)
        args.extend(args_item)
        ids.extend(id_item)
        cost_extract = time.time() - t_extract_function
        logger.info(CYAN + f"extract llm return info cost={cost_extract:.1f}ms" + RESET)

        for fn,arg,id in zip(func_name, args,ids):
            logger.info(f"func call: {fn}, args:{arg}")
            # run_function(fn,arg,self.all_tools)
            if fn != "brain_thinking":
                fn_thread = threading.Thread(target=self.run_function,args=(fn,arg,self.all_tools,id,))
                fn_thread.start()
            else:
                if self.brain_init or env_manager.is_set("STOP"):
                    start_thread = threading.Thread(target=self.brain_agent.start)
                    start_thread.start()
                    self.brain_init=False
                brain_think_thread = threading.Thread(target=self.brain_function_call, args=(fn,arg,id,))
                brain_think_thread.start()

        return {'answer':None}

    def brain_function_call(self, fn, arg,id):
        t_brain_tool = time.time()
        thread_func_call = threading.Thread(target=self.run_function, args=(fn,arg,self.all_tools,id,))
        thread_func_call.start()
        while not env_manager.is_set("STOP") and not self.stop_identifier:
            message_back = self.local_server.get_message_out()
            if message_back:
                self.message_from_brain.extend(message_back)
                message_back = self.local_server.get_message_out()
                logger.info(f"message from brain:{self.message_from_brain}")
                for item in self.message_from_brain:
                    self.send_robot_message(message=item["tts"], cmd=item["cmd"])
                    cost_brain_tool = time.time() - t_brain_tool
                    logger.info(CYAN + f"brain agent tool processing cost={cost_brain_tool:.1f}ms" + RESET)
                    self.history.append({'role':"assistant","content":item["tts"]})
                self.message_from_brain = []
            self.output_for_brain_thinking = arg
            if self.output_for_brain_thinking:
                logger.info(f"message sent to brain:{self.output_for_brain_thinking}")
                if self.output_for_brain_thinking and self.output_for_brain_thinking != 'None':
                    self.local_server.broadcast_voice(json.dumps(self.output_for_brain_thinking))
                arg = None

    def run_function(self, func_name, argument, all_tools, ids):
        tool_t = time.time()
        self.history.append({"role": "tool", "tool_call_id": ids, "content": None})
        schema = all_tools
        cnt = 0
        for s in schema:
            if func_name == s.name:
                idx = cnt
                break
            cnt += 1
        tool_function =all_tools[idx]
        if isinstance(tool_function, BaseTool):
            result = tool_function.invoke(input=argument)
        else:
            result = tool_function(**argument)

        logger.info(result)
        if result['answer']:
            answer_str = result['answer'].replace("\n", ',')
            if answer_str and answer_str[-1] == ",":
                answer_str = answer_str[:-1]
            self.send_robot_message(message=answer_str, cmd="append")

            cost_tool = time.time() - tool_t
            logger.info(CYAN + f"tool executing cost={cost_tool:.1f}ms" + RESET)

            idxx = -1
            while (
                not self.history[idxx].get("tool_call_id", None) or
                self.history[idxx]["tool_call_id"] != ids
                ):
                idxx = idxx - 1
            self.history[idxx]["content"]=result["answer"]
        else:
            cost_tool = time.time() - tool_t
            logger.info(CYAN + f"tool executing cost={cost_tool:.1f}ms" + RESET)

        return result

    def extract_function(self, results):
        ai_messages = results
        func_name = []
        args = []
        ids = []
        if ai_messages.tool_calls:
            for tool in ai_messages.tool_calls:
                func_name.append(tool['name'])
                args.append(tool['args'])
                ids.append(tool['id'])
                self.history.append({
                    "role": "assistant", 
                    "content": None,
                    "tool_calls": [
                        {
                            "id": tool['id'],
                            "type": "function",
                            "function": {
                                "name": tool['name'],
                                "arguments": tool['args']
                            }
                        }]})
        return func_name, args,ids

    def extract_from_text(self,text_list):
        func_name = []
        args = []
        ids = []
        for text in text_list:
            text_dict = json.loads(text)
            func_name.append(text_dict["name"])
            args.append(text_dict["arguments"])
            ids.append(self.id_start)
            self.id_start += 1
            self.history.append({
                        "role": "assistant",
                        "content": None,
                        "tool_calls": [
                            {
                                "id": self.id_start-1,
                                "type": "function",
                                "function": {
                                    "name": text_dict["name"],
                                    "arguments": text_dict["arguments"]
                                }
                            }]})
        return func_name, args,ids