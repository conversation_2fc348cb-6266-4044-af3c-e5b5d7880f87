import os
import asyncio
import json
import queue
import time
import sys
import threading
from datetime import datetime
from typing import Union
from langchain_core.messages import ToolMessage, AIMessage
from algo_tools.utils import CYAN, RESET
from algo_tools.messages.msg_client import BaseMsgClient
from kaiwu_agent.configs.config import EXIT_INSTRUCTION
from kaiwu_agent.utils.kaiwu_message import KaiwuMessageClient
from kaiwu_agent.utils.common import extract_plan_and_remaining
from kaiwu_agent.agents.brain_agent import BaseBrainAgent
from kaiwu_agent.graphs.base import GraphCreatorBase
from kaiwu_agent.utils.registry import AGENTS, GRAPH_CREATORS
import logging
logger = logging.getLogger(__name__)


@AGENTS.register_module()
class GraphBrainAgent(BaseBrainAgent):
    """Brain Agent uses CompiledGraph instance as executor."""
    def __init__(self,
                 msg_client: BaseMsgClient,
                 graph_creator: Union[dict, GraphCreatorBase],
                 system_prompt: str,
                 accept_msg_when_busy = False,
                 kaiwu_msg_client: KaiwuMessageClient = None,
                 max_concurrency: int = 1,
                 exit_keywords: list = None,
                 out_dir: str = "output/data/",
                 name = None,
                 recv_music_path="",
                 finish_music_path="",
                 agent_name: str = None, 
                 agent_uuid: str = None):
        super().__init__(msg_client=msg_client, 
                         name=name, 
                         accept_msg_when_busy=accept_msg_when_busy,
                         kaiwu_msg_client=kaiwu_msg_client,
                         exit_keywords=exit_keywords,
                         recv_music_path=recv_music_path,
                         finish_music_path=finish_music_path,
                         agent_name=agent_name,
                         agent_uuid=agent_uuid)
        if not isinstance(graph_creator, GraphCreatorBase):
            graph_creator = GRAPH_CREATORS.build_from_cfg(graph_creator)
        
        self.graph_creator = graph_creator
        self.system_prompt = system_prompt
        self.max_concurrency = max_concurrency
        
        self.__init_memory__()
        # 获取当前日期和时间，并格式化为 YYYY-MM-DD HH:MM
        current_datetime = datetime.now().strftime("%Y-%m-%d %H:%M")
        # Create a filename with the date suffix
        filename = f"BrainReActAgent_{current_datetime}.jsonl"
        # 打开文件并保存在实例变量中
        os.makedirs(out_dir, exist_ok=True)
        self.stream_file = open(out_dir + filename, "a", encoding="utf-8")
        self.time = time.time()
        
        self._instruct_queue = queue.Queue()
        self._answer_queue = queue.Queue()
        self._process_thread = None
        self._msg_idx = 0

    def __init_memory__(self):
        self.memory = [{'role': 'system', 'content': self.system_prompt}]

    def infer(self, instruction, *args, **kwargs):
        self._instruct_queue.put(instruction)
        # TODO: 阻塞等待，之后可考虑非阻塞
        answer = self._answer_queue.get()
        return answer
    
    async def print_stream(self, messages):
        for message in messages:
            # message类型：
            # Human Message（用户指令）
            # AI Message（LLM准备调工具，或者最终回答）
            # Tool Meaasge（工具结果）
            logger.info(message.pretty_repr())
            self.memory.append({'role': message.type,
                                'content': message.content,
                                "additional_kwargs": message.additional_kwargs,
                                })
            if isinstance(message, AIMessage):
                response = message.content
                # 在这做语音播报，去掉用户交互工具
                if response is not None and response.strip() != "":
                    # 判断是否存在sub_step, 如果存在, 则将sub_step提取出来赋值给msg_client, 且将sub_step从response中删除
                    plan, task_status, message_without_plan = extract_plan_and_remaining(response)
                    await self.async_send_robot_message(
                        message=message_without_plan.strip(), task_status=task_status, steps=plan, cmd="append",
                        agent_name=self.agent_name, agent_uuid=self.agent_uuid
                    )
                
            cost = (time.time() - self.time) * 1000
            self.time = time.time()
            logger.info(CYAN + f"cost={cost:.1f}ms" + RESET)

    async def astream_output(self, graph_executor, inputs, config):
        async for s in graph_executor.astream(inputs, config, stream_mode="values"):
            all_msgs = s["messages"]
            # 仅获取本轮更新的消息并打印
            start_idx = self._msg_idx if self._msg_idx < len(all_msgs) else len(all_msgs) - 1
            update_msgs = all_msgs[start_idx:]
            self._msg_idx = len(all_msgs)
            logger.info(f"start_idx: {start_idx}")
            await self.print_stream(update_msgs)

    async def process_instruction(self):
        graph_executor = await self.graph_creator.async_make_graph()

        logger.info(f"start waiting instruction ...")
        while self._running:
            # 为了正常退出，不阻塞
            try:
                instruction = self._instruct_queue.get(timeout=5)
            except queue.Empty:
                continue
        
            try:
                inputs = {"messages": [("user", instruction)]}
                self.time = time.time()
                # TODO
                config = {"configurable": {"thread_id": "1"}, "recursion_limit": 1000,
                        "max_concurrency": self.max_concurrency}
                
                await self.astream_output(graph_executor, inputs, config)

                json.dump(self.memory, self.stream_file, ensure_ascii=False)
                self.stream_file.write('\n')
                # Immediately flush the data to disk
                self.stream_file.flush()
                self.__init_memory__()
                # 不给默认回复
                answer = {"answer": None}
            except TimeoutError:
                logger.info(f'{self.name}: infer: brain agent通过timeout退出')
                answer = {"answer": None}
            
            self._answer_queue.put(answer)

        logger.info(f"stop waiting instruction, finish.")
        
    def start_process(self):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(self.process_instruction())
    
    def start(self):
        self._msg_idx = 0
        self._process_thread = threading.Thread(target=self.start_process, daemon=True)
        self._process_thread.start()
        super().start()

    def stop(self):
        super().stop()
        self._process_thread.join()
        self.stream_file.close()
        self.stream_file = None
