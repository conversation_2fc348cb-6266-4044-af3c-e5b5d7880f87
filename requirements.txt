setuptools
websockets
langgraph==0.2.76
# langgraph==0.4.8   # 要求python3.11及以上，否则安装失败
langchain-community
langchain-openai
fastapi
uvicorn
supervision
opencv-python
zerorpc
openai
pyowm
google-search-results
pyjwt
scikit-learn
langchain-mcp-adapters==0.1.9
pytest

# 公共算法组件，被robot_voice和kaiwu_agent等，复用减少重复造轮子：http://10.0.3.101/kaiwu/agent/algo_tools
git+*************************************************/kaiwu/agent/algo_tools.git@5128ed83d899655cfd26f9d2a8c9e098ce7ed01e
