
import argparse
from kaiwu_agent.configs.visual_brain_agent import *
from kaiwu_agent.configs.config import VLM_MODELS


def main(execute_model="gpt-4o",
         control_agent_version=CONTROL_VERSIONS.V1_WEBSOCKETS,
         simulation=False,
         save_folder='',
         ):
    brain_agent = get_visual_agent(msg_client=None,
                                  model=VLM_MODELS[execute_model],
                                  control_agent_version=control_agent_version,
                                  simulation=simulation,
                                  save_folder=save_folder)
    image_path1 = "assets/images/simple_industry/2p_circuit_breaker.jpg"
    image_path2 = "assets/images/simple_industry/white_breaker.jpg"
    image_path3 = "assets/images/simple_industry/yellow_button.jpg"
    user = "The object in the fist image is 2p circuit breaker, and the objects in the second and third image is white breaker and yellow button. \
Please detect these three types of objects and give their bounding box with [x1, y1, x2, y2] format in the environment. \
Then take two 2p circuit breakers and a yellow button and place them in the material box."
    user = "在第一图片里的是2p断路器，第二张图里的是白色开关，第三张图里的是黄色按钮。请问环境里有这些物体么？"
    user = "识别下图中物体的英文类别可能是什么？"
    brain_agent.chat(user, [image_path1, image_path2, image_path3])
    while True:
        user = input("请输入：")
        brain_agent.chat(user)
    
    

    
if __name__ == '__main__':
    vlm_choices = VLM_MODELS.keys()
    # 创建ArgumentParser对象
    parser = argparse.ArgumentParser(description="Brain Agent输入参数")

    # 添加参数
    parser.add_argument("-m", "--vlm", type=str,
                        choices=vlm_choices,
                        default="gpt-4o",
                        help="选择要使用的模型名称（默认: gpt-4o）,可选{vlm_choices}")
    parser.add_argument("-c", "--control_agent_version", type=int,
                        default=2,
                        help="control_agent版本，默认为2，可选为1或者2")
    parser.add_argument("--simulation",
                        action="store_true",  # 如果提供了该选项，值为 True；否则为 False
                        help="启用仿真模式，使用config.py的image_path作为仿真图片，否则获取实时的图片。")
    parser.add_argument("-s", "--save", type=str,
                        default="assets/data",
                        # !TODO: 支持其他Agent版本
                        help="数据保存路径，目前限定于VisualBrainAgent生效。")
    # 解析参数
    args = parser.parse_args()
    control_agent_version = CONTROL_VERSIONS.V1_WEBSOCKETS if args.control_agent_version == 1 else CONTROL_VERSIONS.V2_FASTAPI
    main(execute_model=args.vlm,
         control_agent_version=control_agent_version,
         simulation=args.simulation,
         save_folder=args.save)