import os
import requests
from kaiwu_agent.agents.dreamer import Dreamer


def test_dreamer(image_path):
    # 配置必要参数
    print(image_path)
    # 检查测试图片是否存在
    if not os.path.exists(image_path):
        print(f"图片 {image_path} 不存在，请提供测试图片！")
        return

    # 用户指令
    instruction = "右侧机械臂拿起篮子里的苹果然后放到蓝色盘子里。"

    # 初始化 Dreamer 类
    dreamer = Dreamer()
    # api_token = dreamer.encode_jwt_token()
    # 调用 run 方法测试
    print("开始测试 Dreamer 类...")
    result = dreamer.infer(image_path=image_path, instruction=instruction)
    
    print("测试结果:")
    print(result)
    return result


def query_task_status(task_id, base_url, api_token):
    """
    查询图生视频任务状态 (单个任务)
    :param task_id: 任务 ID（可为 task_id 或 external_task_id）
    :param base_url: 图生视频接口的 Base URL
    :param api_token: 身份验证的 API Token
    :return: 返回任务状态的 JSON 响应
    """
    # 构造完整的 URL
    url = f"{base_url}/v1/videos/image2video/{task_id}"
    
    headers = {
        "Authorization": f"Bearer {api_token}",
        "Content-Type": "application/json",
    }

    # 发起 GET 请求
    response = requests.get(url, headers=headers)
    
    # 检查返回状态码
    if response.status_code == 200:
        return response.json()  # 返回 JSON 格式的响应数据
    else:
        print("查询任务状态失败:", response.status_code, response.text)
        return None
    
if __name__ == "__main__":
    image_path= r"D:\vscode\kaiwu_agent\assets\images\franka_kitchen\20250110-115554.jpg"
    response = test_dreamer(image_path)
    print(response)