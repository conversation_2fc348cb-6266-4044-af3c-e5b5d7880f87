import requests
import jwt
import time

def encode_jwt_token(ak, sk):
    """生成 API Token (JWT)"""
    # headers = {"alg": "HS256", "typ": "JWT"}
    payload = {
        "iss": ak,  # 签发者
        "exp": int(time.time()) + 1800,  # 过期时间：30分钟后
        "nbf": int(time.time()) - 30,    # 生效时间：当前时间 - 30秒
    }
    token = jwt.encode(payload, sk, algorithm="HS256")
    if isinstance(token, bytes):
        token = token.decode("utf-8")
    return token

def query_task_status(task_id, base_url, api_token):
    """
    查询图生视频任务状态 (单个任务)
    :param task_id: 任务 ID（可为 task_id 或 external_task_id）
    :param base_url: 图生视频接口的 Base URL
    :param api_token: 身份验证的 API Token
    :return: 返回任务状态的 JSON 响应
    """
    # 构造完整的 URL
    url = f"{base_url}/v1/videos/image2video/{task_id}"
    
    headers = {
        "Authorization": f"Bearer {api_token}",
        "Content-Type": "application/json",
    }

    # 发起 GET 请求
    response = requests.get(url, headers=headers)
    
    # 检查返回状态码
    if response.status_code == 200:
        return response.json()  # 返回 JSON 格式的响应数据
    else:
        print("查询任务状态失败:", response.status_code, response.text)
        return None
    
if __name__ == "__main__":
    ak = "9eb863582d3e4fa1b7f6e5566ad7e4bc"
    sk = "0aa06c046afc4dfb90acaf1d6c217a52"
    
    
    base_url = "https://api.klingai.com"
    image_path = "kling\test_image.png"

    # 生成 API Token
    api_token = encode_jwt_token(ak, sk)
    print("Generated API Token:", api_token)

    # 当前时间戳
    print("Current timestamp:", int(time.time()))
 

    task_id = "ChFhU2d8piUAAAAAADSssQ"
     
    # 查询提交任务的状态
    task_response = query_task_status(task_id, base_url, api_token)
    print(task_response)

