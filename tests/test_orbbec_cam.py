from kaiwu_agent.utils.orbbec_camera import fetch_latest_image, save_data, process_images, request_data
from kaiwu_agent.configs.brain_agent_react_lego import CAMERA_SERVER_IP
import cv2
import zerorpc


def test_orbbec_cam():
    # 服务器的地址
    server_url = 'http://*************:5000/get_latest_image'
    image = fetch_latest_image(server_url)

    if image is not None:
        # 显示图像
        cv2.imshow('Latest Color Image', image)
        cv2.waitKey(0)  # 等待用户按键
        cv2.destroyAllWindows()  # 关闭窗口
    else:
        print("Failed to decode image")


def test_rpc_cam():
    head_camera_zerorpc_client = zerorpc.Client()
    head_camera_zerorpc_client.connect(f"tcp://{CAMERA_SERVER_IP}:4242")

    rgb_array, depth_array = request_data(head_camera_zerorpc_client, width=1920, height=1080)
    save_data("assets/lego", rgb_array, depth_array, add_time=False)

    head_camera_zerorpc_client.close()


if __name__ == '__main__':
    # test_orbbec_cam()
    test_rpc_cam()
