import pytest
from pydantic import BaseModel
from kaiwu_agent.utils.registry import Registry  # 修改为你的真实模块名


# --- 用于测试的类和函数 ---
class DummyClass:
    def __init__(self, a, b):
        self.a = a
        self.b = b


def dummy_function(x):
    return x * 2


# 用于测试构造失败的类
class BadClass:
    def __init__(self):
        raise ValueError("fail intentionally")


# pydantic 配置类
class DummyConfig(BaseModel):
    type: str
    a: int
    b: int


@pytest.fixture
def registry():
    return Registry(name="TestRegistry")


def test_register_direct_and_decorator(registry):
    # 直接注册
    registry.register_module(DummyClass)
    assert "DummyClass" in registry.module_names

    # 装饰器注册
    @registry.register_module()
    class AnotherClass:
        pass

    assert "AnotherClass" in registry.module_names

    # 指定别名
    registry.register_module(dummy_function, alias="my_func")
    assert "my_func" in registry.module_names


def test_register_duplicate_error(registry):
    registry.register_module(DummyClass)
    with pytest.raises(KeyError):
        registry.register_module(DummyClass)


def test_build_from_cfg_dict(registry):
    registry.register_module(DummyClass)

    cfg = {"type": "DummyClass", "a": 1, "b": 2}
    obj = registry.build_from_cfg(cfg)

    assert isinstance(obj, DummyClass)
    assert obj.a == 1
    assert obj.b == 2


def test_build_from_cfg_model(registry):
    registry.register_module(DummyClass)

    cfg = DummyConfig(type="DummyClass", a=10, b=20)
    obj = registry.build_from_cfg(cfg)

    assert isinstance(obj, DummyClass)
    assert obj.a == 10
    assert obj.b == 20


def test_build_missing_type(registry):
    with pytest.raises(KeyError, match="Missing 'type'"):
        registry.build_from_cfg({"a": 1})


def test_build_unknown_type(registry):
    with pytest.raises(KeyError, match="not found in Registry"):
        registry.build_from_cfg({"type": "UnknownClass"})


def test_build_invalid_config_type(registry):
    with pytest.raises(TypeError):
        registry.build_from_cfg("not a dict")


def test_build_failure_handling(registry):
    registry.register_module(BadClass)

    cfg = {"type": "BadClass"}
    with pytest.raises(RuntimeError, match="Failed to build BadClass"):
        registry.build_from_cfg(cfg)
