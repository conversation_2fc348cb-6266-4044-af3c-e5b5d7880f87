from kaiwu_agent.agents.chat_agent import FunctionCallAgent
from kaiwu_agent.configs.config import VLM_MODELS
from kaiwu_agent.configs.commons import get_orbbec_client

if __name__ == '__main__':
    # Example of tool functions that can be added
    def ask_database(query):
        """function to interact with a database
        
        parameters:
        - query (str): query
        """
        return f"Results for query: {query}"

    def calculate_sum(a, b):
        """function to calculatethe sum of two number.
        
        parameters:
        - a (float): the first number.
        - b (float): the second number.
        """
        return a + b

    # Example usage
    camera_client = get_orbbec_client(simulation=True)
    agent = FunctionCallAgent(
        system_prompt="I am an intelligent assistant.",
        model=VLM_MODELS["gpt-4o"]["model"],
        api_key=VLM_MODELS["gpt-4o"]["api_key"],
        api_base=VLM_MODELS["gpt-4o"]["base_url"],
        tools=[ask_database, calculate_sum],
        camera_client=camera_client,
    )


    # Start a chat with a text prompt
    response = agent.run("hello!")
    print(response)
