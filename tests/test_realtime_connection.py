import os
import json
import websocket  # pip install websocket-client

# NEW_API_KEY = os.environ.get("NEW_API_KEY")
NEW_API_KEY = 'sk-hQZBRE0osxsf6UGyVe2AppfRmqJRPtLxeG0nBFPpogg12Oj8'
URL_PROXY = 'http://************:3000'


# url = f"wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview-2024-12-17"
url = f"ws://************:3000/v1/realtime?model=gpt-4o-realtime-preview-2024-12-17"
headers = [
    "Authorization: Bearer " + NEW_API_KEY,
    "OpenAI-Beta: realtime=v1"
]

def on_open(ws):
    print("Connected to server.")

def on_message(ws, message):
    data = json.loads(message)
    print("Received event:", json.dumps(data, indent=2))

def on_error(ws, error):
    print("error")
    print(ws)
    print(error)

ws = websocket.WebSocketApp(
    url,
    header=headers,
    on_open=on_open,
    on_message=on_message,
    on_error=on_error
)

print("AAAAAAAAAAA")
# ws.run_forever(http_proxy_host='************',http_proxy_port='3000')
ws.run_forever()
