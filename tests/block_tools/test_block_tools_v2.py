"""
用于处理图像，检测和分类积木块的颜色和位置。

1. 积木检测:
   - 裁剪图像中特定区域进行积木检测，并保存裁剪后的图像。  
   - 使用预定义的检测接口识别图像中的积木块。  
   - 在检测到的积木块上绘制边界框，并保存处理后的图像。  

2. 积木排序与描述: 
   - 根据积木块的垂直位置对其进行分层。  
   - 在每一层中，按照从左到右的顺序对积木进行排序。  
   - 生成JSON格式的积木排列描述。  

3. 配置参数:
   - `api_key`：用于 ChatGPT 请求的 API 密钥。  
   - `det_url`：用于积木图像检测的 API 端点。
   - `gpt_url`：指定要访问的 OpenAI API 服务器地址。  
   - `initial_image_path`：输入图像文件路径。  
   - `cropped_image_path`：裁剪后图像的保存目录。  
   - `rectangle_image_path`：带有检测边界框的图像保存目录。  
   - `output_json_path`：存储检测结果的 JSON 文件路径。  
   - `output_json_path`：存储检测结果的 JSON 文件路径。  
   - `x, y, width, height`：定义裁剪图像的区域， x, y 定义左上角坐标， width, height 定义宽度和高度。  
   - `output_json_path`：存储检测结果的 JSON 文件路径。    
   - `x, y, width, height`：定义裁剪图像的区域， x, y 定义左上角坐标， width, height 定义宽度和高度。  
   - `color_map`：积木颜色分类列表，用于最后描述文件颜色属性的映射。  
   - `block_map`：积木类型映射，用于描述积木类型。  
   
4. 以下五个参数将从工作路径 parameters.json 文件中读取：
   - `angle`：旋转角度，用于对图像进行旋转。
   - `grid_distance`：积木尺寸阈值，基于像素的网格大小，确定积木大小。  
   - `layer_threshold`：积木之间的高度差阈值，决定是否属于同一层。  
   - `head_height`：积木头部高度，统一规范积木的高度。
   - `crop_size`：包括 (x, y, width, height)，裁剪图像的尺寸， x, y 定义左上角坐标， width, height 定义宽度和高度。   
"""
import cv2
import os
import json
from kaiwu_agent.utils.lego.lego_descriptor_v2 import BlockImageProcessor



prompt_text = """
            Each layer has 1 to 3 blocks.  
            The blocks on each layer are described in the order from the left to the right of the picture.  
            Only use the given blocks [one small size blue block, one small size red block, one medium size green block,  
            two medium size yellow blocks, one large size blue block], and describe the blocks and their colors from bottom to top in the following format:  
            eg:  
            small size blue block. small size red block. medium size green block. large size blue block.  
            Requirement: Only answer the content in the format, do not answer anything else.
            """
api_key = "sk-Wyy6gs0pUYfhmP7RLo6XvupsmujD4odpK3yU1tQbwtLh1mWt"
# det_url = "http://*************:8091/segment_objects"
det_url = "http://*************:8091/segment_objects"
gpt_url ="https://api.chatanywhere.tech/v1"
processor = BlockImageProcessor(prompt_text, api_key, det_url, gpt_url)


initial_image_path = "1920_img/test/1920_img_rgb_image_1740633426.8767195.jpg" # 输入图像路径

image = cv2.imread(initial_image_path)

if image is None:
    print("无法读取输入图像，请检查路径！")
else:
    # 水平翻转
    flipped_image = cv2.flip(image, 1)  # 1 表示水平翻转

    # 定义输出路径
    output_dir = os.path.dirname(initial_image_path)
    output_filename = os.path.basename(initial_image_path)
    output_filename_root, output_filename_ext = os.path.splitext(output_filename)
    flipped_image_path = os.path.join(output_dir, f"{output_filename_root}_flipped{output_filename_ext}")

    # 保存翻转后的图像
    cv2.imwrite(flipped_image_path, flipped_image)

    print(f"翻转后的图像已保存到 {flipped_image_path}")
initial_image_path = flipped_image_path

cropped_image_path = "1920_img/test_cropped_image"  # 裁剪后图像的保存路径
output_json_path = "1920_img/test_result.json"  # 输出json文件路径
rectangle_image_path = "1920_img/test_rectangled_image" # 带有检测边界框的图像保存目录

# initial_image_path = "./tests/block_tools/lego_input/20250106-162248.png"  # 输入图像路径
# cropped_image_path = "./assets/cropped_image"  # 裁剪后图像的保存路径
# output_json_path = "./assets/result.json"  # 输出json文件路径

# 获取参数文件路径
file_name = "assets/lego/max1/parameters.json"
parameters = processor.read_parameters(file_name)

# 假设积木一格的大小为 grid_distance 个像素值
grid_distance = parameters.get("grid_distance", 100)
# 判定积木层数的阈值，两块积木之间的差值如果小于该值则认为是同一层积木
layer_threshold = parameters.get("layer_threshold", 90)
# 积木大头的高度，检测到积木大头时，会在计算积木的高度时减去该值
head_height = parameters.get("head_height", 60)
# 图片旋转角度
angle = parameters.get("angle", 1)
# 图片裁剪的尺寸
x, y, width, height = parameters.get("crop_size", (600, 70, 680, 600))
print(angle)

# 提取颜色所用的字典列表
color_map = ["blue", "red", "green", "yellow"]

block_map = {
         "small size blue block": "blue_1x1",
         "small size red block": "red_1x1",
         "medium size green block": "green_1x2",
         "medium size yellow block": "yellow_1x2",
         "large size blue block": "blue_1x3",
   }

import time
start = time.time()
processor.execute(
    initial_image_path,
    cropped_image_path,
    rectangle_image_path,
    x,
    y,
    width,
    height,
    angle, 
    head_height, 
    grid_distance,
    layer_threshold,
    color_map, 
    output_json_path,
    block_map
)

print(time.time()-start)

