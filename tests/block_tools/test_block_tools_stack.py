"""
用于处理图像，检测和分类积木块的颜色和位置，以及积木逐层搭建。

1. 积木检测:
   - 裁剪图像中特定区域进行积木检测，并保存裁剪后的图像。  
   - 使用预定义的检测接口识别图像中的积木块。  
   - 在检测到的积木块上绘制边界框，并保存处理后的图像。  

2. 积木排序与描述: 
   - 根据积木块的垂直位置对其进行分层。  
   - 在每一层中，按照从左到右的顺序对积木进行排序。  
   - 生成JSON格式的积木排列描述。  

3. 配置参数:
   - `api_key`：用于 ChatGPT 请求的 API 密钥。  
   - `det_url`：用于积木图像检测的 API 端点。
   - `gpt_url`：指定要访问的 OpenAI API 服务器地址。  
   - `initial_image_path`：输入图像文件路径。  
   - `cropped_image_path`：裁剪后图像的保存目录。  
   - `rectangle_image_path`：带有检测边界框的图像保存目录。  
   - `output_json_path`：存储检测结果的 JSON 文件路径。  
   - `x, y, width, height`：定义裁剪图像的区域， x, y 定义左上角坐标， width, height 定义宽度和高度。  
   - `output_json_path`：存储检测结果的 JSON 文件路径。    
   - `color_map`：积木颜色分类列表，用于最后描述文件颜色属性的映射。  
   - `block_map`：积木类型映射，用于描述积木类型。  
   
4. 以下五个参数将从工作路径 parameters.json 文件中读取：
   - `angle`：旋转角度，用于对图像进行旋转。
   - `grid_distance`：积木尺寸阈值，基于像素的网格大小，确定积木大小。  
   - `layer_threshold`：积木之间的高度差阈值，决定是否属于同一层。  
   - `head_height`：积木头部高度，统一规范积木的高度。
   - `crop_size`：包括 (x, y, width, height)，裁剪图像的尺寸， x, y 定义左上角坐标， width, height 定义宽度和高度。   
"""
import glob
from kaiwu_agent.utils.lego.lego_descriptor_v2 import BlockImageProcessor
from kaiwu_agent.configs.config import LLM_VLM_MODELS
from kaiwu_agent.configs.brain_agent_react_lego import DESC_PROMPT, COLOR_MAP, BLOCK_MAP, DET_SERVER_IP
from kaiwu_agent.configs.brain_agent_react_lego_ur import DESCRIPTION_CAMERA_PARAMS, EXAMINER_CAMERA_PARAMS

vlm_model = LLM_VLM_MODELS["gpt-4o"]
lego_commons = dict(
    prompt_text=DESC_PROMPT, 
    det_url=f"http://{DET_SERVER_IP}:8091/segment_objects", 
    api_key=vlm_model["api_key"], 
    gpt_url=vlm_model["base_url"], 
    model=vlm_model["model"], 
)

#diffrent from ur current crop area
DESCRIPTION_CAMERA_PARAMS["crop_size"] = [500, 400, 520, 600]
EXAMINER_CAMERA_PARAMS["crop_size"] = [750, 150, 510, 950]


lego_descript = BlockImageProcessor(
    camera_params=DESCRIPTION_CAMERA_PARAMS,
    color_map=COLOR_MAP,
    block_map=BLOCK_MAP,
    **lego_commons
)

lego_exam = BlockImageProcessor(
    camera_params=EXAMINER_CAMERA_PARAMS,
    color_map=COLOR_MAP,
    block_map=BLOCK_MAP,
    **lego_commons
)
des_img_path = "assets/ur_stack/desc/flip_front_rgb_image_des.jpg"
des_res_dir = "assets/ur_stack/desc/"


description_res = lego_descript.execute(des_img_path, des_res_dir)

print(f"current description:{description_res}")
exam_img_dir = "assets/ur_stack/"
exam_res_dir = "assets/ur_stack/exam/"
img_list = sorted(glob.glob(exam_img_dir+"/*index*.jpg"))
total_block_number = all_block_number = len(description_res[0]["block_info"])

for cur_index in range(total_block_number):
    print(f"\nExaming {cur_index}-th block")
    img = img_list[cur_index]
    exam_res = lego_exam.block_examiner(img, exam_res_dir, lego_descript.lego_description, cur_index)
    print(f"examine result:{exam_res}")
