from kaiwu_agent.utils.lego.lego_monitor import Monitor

def test_block_monitor():
    api_key="EMPTY"

    #finetuned block monitor api
    api_url="http://*************:8008/v1"
    # change port 8008 to 8009, or others, for api ivoke failed test
    # api_url="http://*************:8009/v1"
    model_name="Qwen2-VL-7B-block-monitor"

    #discription of blocks, from buttom to top
    cur_block_layers = "第一层：绿色1x2积木、第二层：蓝色1x3积木、第三层：黄色1x2积木、第四层：蓝色1x1积木"
    prompt = f"""The Task is to build a block as the instruct shows. The target block place order from buttom to up is:
    描述中的积木一共有4层，从下到上颜色依次为:{cur_block_layers}, 
    Please check with the image, return the task execution status"""

    monitor = Monitor(monitor_api_key=api_key,
                      monitor_api_url=api_url, 
                      monitor_model_name=model_name
                      )
    # forth layer tilted
    img_path = "tests/block_tools/lego_input/000109_0000_1250796058_1735109414362012_Color_1280x800.png"

    # return format: dict {"succeed": bool,"response": str}
    monitor_result = monitor.monitor_infer(prompt, img_path)

    # generate successfully
    if monitor_result["succeed"]:
        inference_res = monitor_result["response"]
        print(f"Monitor inference result is: {inference_res}")
    else:
        inference_res = monitor_result["response"]
        print(f"Monitor inference failed, failed reason is: {inference_res}")


if __name__ == '__main__':
    test_block_monitor()