
import glob
import cv2
import zerorpc
import os.path as osp
from kaiwu_agent.utils.lego.lego_descriptor import LegoDescriptor
from kaiwu_agent.configs.brain_agent_react_lego import prompt_text, prompt_text_gpt
from kaiwu_agent.configs.config import VLM_MODELS
from kaiwu_agent.utils.orbbec_camera import fetch_latest_image, request_data, save_data


def test_lego_desc_folder(input_folder):
    """
    遍历输入文件夹中的所有图像文件并处理
    """
    for image_path in glob.glob(osp.join(input_folder, "*.png"))[:10]:
        test_lego_desc(image_path)

    print("Processing complete.")



def test_lego_desc(image_path):
    print(f"Processing: {image_path}")
    
    url = "http://*************:8003/segment_objects"
    
    # gpt_url ='http://127.0.0.1:7897'
    # gpt_api_key = "********************************************************************************************************************************************************************"
    
    # 不用设置代理
    # gpt_api_key = "sk-Wyy6gs0pUYfhmP7RLo6XvupsmujD4odpK3yU1tQbwtLh1mWt"
    # gpt_url  = "https://api.chatanywhere.tech/v1"
    
    # model = "Qwen2-VL-72B-Instruct"
    model = "gpt-4o"
    model_cfg = VLM_MODELS[model]
              
    # prompt_text = "lego"
    # prompt_text_gpt = '''请根据文字和图片提示，通过如下步骤解决这个问题：
    # - 现有如下有且仅有下述积木，包括1x3蓝色积木， 1x2绿色积木， 1x2黄色积木， 1x1红色积木，1x1蓝色积木。
    # - 完整积木有四层，每一层只有一块积木，
    # - 图片中坐标格式为（x，y），其中x表示第几层积木，y表示相对于原点(0,0)的偏移
    # 问题：
    #     -  根据完整积木的正视图。要拼成图中的的完整积木需要如何放置这些单个积木
    # 要求：
    #     - 从第一层（最底层为第一层）开始，逐层描述每一层所需要的积木。
    #     - 用积木在横向上占据的位置描述每层的单个积木，可以参考图中给出的坐标信息。
        
    # 限制:，
    # 现在要求返回，每一层积木的颜色，形状，以及每层积木左端点的坐标，坐标格式为（x，y），
    #     - 其中x表示第几层积木，y表示相对于原点(0,0)的偏移;积木的形状参考积木占据坐标格子的数量，若占据两个格子即形状为1×2，
    # Tought：只使用给出积木，请先观察每一层积木的颜色，每个积木占据格子的数量，其左端点的坐标，用尽可能详细的语言进行描述图片内容
    # Action：参考Tought和图片中的内容，输出以下格式：
    # ```json
    # {
    #     "block_info": [
    #         {
    #             "color": "blue",
    #             "shape": [1, 3],
    #             "pos": [X, Y]
    #         },
    #         ...
    #     ]
    # }
    # ```
    # 其中 pos  (X, Y) x表示第几层积木，y表示相对于原点(0,0)的积木左端点的偏移 
    # color 为积木的颜色
    # shape 表示积木的形状 (1,3)表示形状为1x3的积木
    # 把Tought 和action 的内容分别输出
    # '''

    # prompt_text_gpt = """
    # ###角色定义###
    # 你是一个AI助手，在你面前摆放着一张白色的积木底板，在底板上搭建起了一组多层积木，每层只有一个积木。\
    # 从正面看，每个积木都包含了一个长方形底座以及在底座上凸起的一排若干个圆柱，底座和圆柱的颜色一致，底座可插在上一层积木的圆柱上。

    # ###任务描述###
    # 图片中的积木已经提前画上了坐标格子，你的任务是参考坐标信息，自底向上描述每层积木的颜色"color"、形状"shape"，以及该积木左端点的坐标"position"，输出以下内容：
    # ```
    # Tought: 你的思考
    # Block_Info: 积木的信息，按照以下格式输出：
    # [
    #     # 第一层
    #     {
    #         "color": "blue",
    #         "shape": (h, w),
    #         "position": (x, y)
    #     },
    #     # 第二层
    #     ...
    # ]
    # ```
    # 其中：
    # - shape (h, w) 表示积木高度和宽度分别为h和w。积木的形状可参考积木底座（不含圆柱部分）所占据的坐标格子数量，若高度方向占据1个格子且宽度方向占据两个格子，即形状为1×2。目前所有积木高度都为1。
    # - position (x, y) 中的x表示积木的层数（以最底层积木为第0层，向上为正），y表示积木左端点所在的列数（以最底层积木左端点为第0列，向右为正）。
    # """
    
    num_level = 3
    new_prompt_text_gpt = prompt_text_gpt.replace("num_level", str(num_level))
    print(new_prompt_text_gpt)
    processor = LegoDescriptor(num_level=num_level,
                               det_url=url, 
                               gpt_api_key=model_cfg["api_key"], 
                               gpt_url=model_cfg["base_url"], 
                               model=model, 
                               resize_factor=1.0,
                               prompt_text=prompt_text, 
                               prompt_text_gpt=new_prompt_text_gpt,
                               output_folder="assets/lego")
    
    # 读取图像
    # image = cv2.imread(image_path)
    
    head_camera_zerorpc_client = zerorpc.Client()
    head_camera_zerorpc_client.connect("tcp://************:4242")
    image_array, depth_array = request_data(head_camera_zerorpc_client, width=1280, height=720)
    # save_path = save_data("outputs/lego/hello", image_array, depth_array)
    # print(save_path)
    # image = cv2.imread(save_path)
    
    image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)
    
    assert image is not None, f"Failed to load image: {image_path}"
    
    response = processor.process_image(image)
    print(f'response: \n{response}')


if __name__ == '__main__':
    # image_path = "/mnt/sda/code/kaiwu_agent/tests/block_tools/lego_input/000001_0000_5049723130_1733817547383410_Color_1280x800.png"
    # image_path = "tests/block_tools/lego_input/000492_0000_5436816374_1733817934479988_Color_1280x800.png"
    # image_path = "tests/block_tools/lego_input/20241227-150715.jpg"
    image_path = "tests/block_tools/lego_input/lego_rgb_image_1735295932.9007194.jpg"
    
    test_lego_desc(image_path)
    
    # test_lego_desc_folder(input_folder=)
