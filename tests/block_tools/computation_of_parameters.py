import os
import cv2
import numpy as np
import json
import shutil
import logging
logger = logging.getLogger(__name__)

from sklearn.cluster import KMeans
from kaiwu_agent.configs.config import REAL_IMAGE_FOLDER
from kaiwu_agent.utils.common import get_uniq_datetime_str
from kaiwu_agent.utils.orbbec_camera import OrbbecCameraClient
from kaiwu_agent.utils.lego.lego_descriptor_v2 import BlockImageProcessor
from kaiwu_agent.configs.brain_agent_react_lego import CAMERA_SERVER_IP, DET_SERVER_IP


'''
computation_of_parameters.py

更新时间： 2025年3月3日

描述：检测输入图像中的积木块，根据检测结果计算需要的参数，并且记录在 parameters.json 中。

使用方法： 
    1. `initial_image_path` 为从相机中获取的图像路径。
    2. 运行脚本 `python tests/block_tools/computation_of_parameters.py`。
    3. 处理后的图像将被保存到 `cropped_image_path`。
    4. 计算出的参数将被写入当前工作目录中的 `parameters.json` 文件。

测试图片采集要求：
    1. 图片中需要同时存在 1x1, 1x2, 1x3 三种不同 size 的积木，否则 grid_distance 可能会计算错误
    2. 图片中需要同时存在“头部”露出的积木和“头部”被遮挡的积木，否则 head_height 可能会计算错误
    3. 简单的示例：
        第三层： 1x2黄色积木(“头部”露出)
        第二层： 1x1红色积木(“头部”被遮挡)
        第一层： 1x3蓝色积木(“头部”露出)

输出结果示例：
{
    "grid_distance": 101.94444444444444,
    "layer_threshold": 97.52499999999999,
    "head_height": 68.5,
    "angle": 1.2993310400264446
    "crop_size": [
        647,
        191,
        661,
        483
    ]
}

输出结果说明：
    1. angle 图片旋转角度
    2. head_height 积木头部高度
    3. layer_threshold 积木一层高度阈值
    4. grid_distance 积木一格的大小阈值
    5. crop_size 裁剪框的左上角坐标值和长宽(x, y, width, height)
'''

def crop_and_rotate_image(
        input_path, output_path, x, y, width, height,
    ):
    """裁剪图像并绘制矩形框"""
    image = cv2.imread(input_path)
    if image is None:
        print("无法读取输入图像，请检查路径！")
        return
        
    # 裁剪框内的内容
    cropped_image = image[y : y + height, x : x + width]
    image = cropped_image  
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    edges = cv2.Canny(gray, 50, 150, apertureSize=3)

    lines = cv2.HoughLinesP(edges, 1, np.pi / 180, threshold=100, minLineLength=100, maxLineGap=10)
    line_image = image.copy()  # 创建一个副本，用于绘制检测到的直线
    h, w = image.shape[:2]
    filtered_lines = []
    for line in lines:
        x1, y1, x2, y2 = line[0]
        # 只筛选y坐标位于下半部分的直线
        if y1 > h * 0.7 and y2 > h * 0.7:   
            filtered_lines.append(line)
            cv2.line(line_image, (x1, y1), (x2, y2), (0, 255, 0), 2)  # 在图像上绘制直线
    angles = []
    for line in filtered_lines:
        x1, y1, x2, y2 = line[0]
        angle = np.arctan2(y2 - y1, x2 - x1) * 180. / np.pi
        angles.append(angle)

    # 计算平均角度
    if angles:
        avg_angle = np.mean(angles)
    else:
        avg_angle = 0  # 如果没有符合条件的直线，设置平均角度为0
    avg_angle *= 1.1
    print(f"Calculated rotation angle: {avg_angle} degrees")

    center = (w // 2, h // 2)
    M = cv2.getRotationMatrix2D(center, avg_angle, 1.0)
    rotated = cv2.warpAffine(image, M, (w, h), flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE)

    # 保存裁剪后的图像
    output_dir = os.path.dirname(output_path)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    cv2.imwrite(output_path, rotated)
    print(f"crop image saved at: {output_path}")
    return avg_angle

def estimate_grid_distance(size_data):
    # 将数据转换为适当的格式用于聚类分析
    # 使用一维数组表示数据点，并假设三个簇(即1 x grid, 2 x grid, 和3 x grid)
    data_for_clustering = np.array(size_data).reshape(-1, 1)

    # 使用KMeans进行聚类
    n_clusters = min(3, len(size_data)) # 如果积木数量小于3，则聚类数量为积木数量
    kmeans = KMeans(n_clusters=n_clusters)
    kmeans.fit(data_for_clustering)

    # 计算每个簇的中心点，并排序以便于识别哪个是1x, 2x, 3x
    centers = sorted(kmeans.cluster_centers_.flatten())

    # 计算grid的估计值
    grid_estimated = 0
    for i in range(3):
        grid_estimated += centers[i] / (i + 1)
    grid_estimated /= 3

    return grid_estimated

def estimate_layer_and_head(high_data):
    # 1. 对数据排序
    sorted_data = sorted(high_data)
            
    # 2. 计算相邻元素之间的差值，并找到最大的几个差值的位置
    diffs = np.diff(sorted_data)
    max_diff_index = np.argmax(diffs)  # 取最大差值的索引

    # 3. 初步划分数据
    split_index = max_diff_index + 1
    layer_group = sorted_data[:split_index]
    layer_head_group = sorted_data[split_index:]
            
    # 4. 处理特殊情况：如果最大间隔太小，可能没有含头的层 (或只有含头的层)
    if diffs[max_diff_index] / np.std(sorted_data) < 1:
        return np.mean(sorted_data), 0, sorted_data, []  # 认为所有数据都属于 layer_height，没有 layer_head_height
            
    # 5. 估计 layer_height 和 head_height
    layer_height = np.mean(layer_group)
            
    # 当 layer_head_group 只有一个值时，计算 head_height 时避免过度依赖均值
    if len(layer_head_group) == 1:
        head_height = layer_head_group[0] - max(layer_group)  # 取最大 layer_height 作为参考
    else:
        head_height = np.mean(layer_head_group) - layer_height
            
    return layer_height, head_height

def calculate_grid_layer(detection_json):
    # 获取所有积木的边界框和标签
    objects = detection_json
    boxes = objects['res']['bboxes']
    labels = objects['res']['labels']

    # 存储每块积木的高度
    high_data = []
    # 存储每块积木的size
    size_data = []
    # 获取每块积木的size和高度
    for i, label in enumerate(labels):
        size = boxes[i][2] - boxes[i][0]
        high = boxes[i][3] - boxes[i][1]
        size_data.append(size) 
        high_data.append(high)
        
    grid_estimated = estimate_grid_distance(size_data)
    # 一格积木的大小
    grid_distance = grid_estimated
    layer_head_estimated, head_height_estimated = estimate_layer_and_head(high_data)
    # 头部高度，如果头部高度为0，说明检测模型只检测到了含头（或不含头）的积木
    head_hight = head_height_estimated
    # 层高（不含头）
    layer_threshold = layer_head_estimated * 0.83
    return grid_distance, layer_threshold, head_hight


def find_second_smallest(values):
    """
    找到给定列表中的第二小的值。
    如果列表中存在重复的最小值，也返回第二个最小值。
    """
    unique_values = list(set(values))  # 去重
    if len(unique_values) < 2:
        raise ValueError("List must contain at least two distinct elements.")
    
    unique_values.sort()  # 对去重后的列表进行排序
    return unique_values[1]  # 返回第二小的值

def crop_parameter(detection_json):
    # 提取 x_min, y_min, x_max, y_max
    x_min_values = [bbox[0] for bbox in detection_json["res"]["bboxes"]]
    y_min_values = [bbox[1] for bbox in detection_json["res"]["bboxes"]]
    x_max_values = [bbox[2] for bbox in detection_json["res"]["bboxes"]]
    y_max_values = [bbox[3] for bbox in detection_json["res"]["bboxes"]]

    # 计算最小和最大值
    min_x_min = min(x_min_values)
    min_y_min = min(y_min_values)
    max_x_max = max(x_max_values)
    max_y_max = max(y_max_values)

    if min_x_min < 200:
        try:
            second_min = find_second_smallest(x_min_values)
            min_x_min = second_min
        except ValueError as e:
            print(e)

    # 计算图像的宽度和高度
    width = max_x_max - min_x_min
    height = max_y_max - min_y_min

    if width > 2 * (detection_json["res"]["bboxes"][0][2] - detection_json["res"]["bboxes"][0][0]):
        width = 2 * (detection_json["res"]["bboxes"][0][2] - detection_json["res"]["bboxes"][0][0])

    return min_x_min-200, min_y_min-30, width+400, height+60

def main():

    prompt_text = """
            Each layer has 1 to 3 blocks.  
            The blocks on each layer are described in the order from the left to the right of the picture.  
            Only use the given blocks [one small size blue block, one small size red block, one medium size green block,  
            two medium size yellow blocks, one large size blue block], and describe the blocks and their colors from bottom to top in the following format:  
            eg:  
            small size blue block. small size red block. medium size green block. large size blue block.  
            Requirement: Only answer the content in the format, do not answer anything else.
            """
    api_key = "sk-Wyy6gs0pUYfhmP7RLo6XvupsmujD4odpK3yU1tQbwtLh1mWt"
    # det_url = "http://*************:8091/segment_objects"
    det_url = f"http://{DET_SERVER_IP}:8091/segment_objects"

    computation = BlockImageProcessor(prompt_text, api_key, det_url)
    camera_client = OrbbecCameraClient(simulation=False, server_ip=CAMERA_SERVER_IP, test_img_path=None, default_view='front',default_port='4242', width=1920, height=1080, save_folder=REAL_IMAGE_FOLDER)
    # 输入图片路径
    initial_image_path = camera_client.get_realtime_imagepath(add_time=False)

    # 反转图片
    image = cv2.imread(initial_image_path)

    if image is None:
        print("无法读取输入图像，请检查路径！")
    else:
        # 水平翻转
        flipped_image = cv2.flip(image, 1)  # 1 表示水平翻转

        # 定义输出路径
        output_dir = os.path.dirname(initial_image_path)
        output_filename = os.path.basename(initial_image_path)
        output_filename_root, output_filename_ext = os.path.splitext(output_filename)
        flipped_image_path = os.path.join(output_dir, f"{output_filename_root}_flipped{output_filename_ext}")

        # 保存翻转后的图像
        cv2.imwrite(flipped_image_path, flipped_image)

        print(f"翻转后的图像已保存到 {flipped_image_path}")
    initial_image_path = flipped_image_path

    # 裁剪后图片保存路径
    cropped_image_path = "assets/1920_img/test_cropped.jpg"
    # 积木（全部）检测的prompt
    prompt = "small size blue block. small size red block. medium size green block. medium size yellow block. large size blue block."
    # prompt 映射
    block_map = {
            "small size blue block": "blue_1x1",
            "small size red block": "red_1x1",
            "medium size green block": "green_1x2",
            "medium size yellow block": "yellow_1x2",
            "large size blue block": "blue_1x3",
    }

    # 积木（全部）检测，为了计算裁剪尺寸
    detection_json_1 = computation.segment_objects(initial_image_path, prompt, block_map)
    # 计算裁剪尺寸
    x, y, width, height = crop_parameter(detection_json_1)
    print(x, y, width, height)
    # 裁剪图片并计算旋转角度
    angle = crop_and_rotate_image(initial_image_path, cropped_image_path, x, y, width, height)
    # 积木（裁剪后）检测，为了计算 grid_distance, layer_threshold, head_hight
    detection_json_2 = computation.segment_objects(cropped_image_path, prompt, block_map)
    # 计算 grid_distance, layer_threshold, head_hight
    grid_distance, layer_threshold, head_hight = calculate_grid_layer(detection_json_2)

    # 创建要写入的数据行
    parameters = {
        "grid_distance": grid_distance, 
        "layer_threshold": layer_threshold, 
        "head_height": head_hight,
        "angle": angle,
        "crop_size": (x, y, width, height)
    }

    # 定义文件名和路径
    file_name = "assets/lego/max1/parameters.json"
    # file_name = "assets/lego/max1/latest.json"
    backup_dir = "assets/lego/max1/backup"
    if os.path.exists(file_name):
        backup_file = f"{backup_dir}/parameters_{get_uniq_datetime_str()}.json"
        os.makedirs(backup_dir, exist_ok=True)
        shutil.copyfile(src=file_name, dst=backup_file)
        logger.info(f"原 parameter.json 已备份到: {file_name}")

    dir_name = os.path.dirname(file_name)
    if not os.path.exists(dir_name):
        os.makedirs(dir_name)

    # 写入数据到文件
    with open(file_name, 'w') as file:
        json.dump(parameters, file, indent=4)
        
    logger.info(f"新参数已写入：{file_name}")

if __name__ == "__main__":
    main()