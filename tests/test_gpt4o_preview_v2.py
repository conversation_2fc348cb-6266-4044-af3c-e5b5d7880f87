import os
os.system('cls' if os.name == 'nt' else 'clear')

import threading
import pyaudio
import queue
import base64
import json
import time
from websocket import create_connection, WebSocketConnectionClosedException
from dotenv import load_dotenv
import logging
from datetime import datetime
from langchain_community.utilities import SerpAPIWrapper

logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')

load_dotenv()


os.environ['http_proxy'] = 'http://127.0.0.1:7897'
os.environ['https_proxy'] = 'http://127.0.0.1:7897'
os.environ["OPEN_API_KEY"] = "********************************************************************************************************************************************************************"




def get_time():
    """用于获取当前时间"""
    print("++++++++++++++calling tools get time#######################")
    print("TOOL SUCCEEDED, answer is:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    return {"tool_succeed": True, "answer": datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

def google_search(query="查询"):
    """联网搜索工具：用于在互联网上搜索信息。"""
    serpapi_api_key = "00484c51d4a94dde462a6e5eb971d797a0019a7a415901d9500a7231a61b6d51"
    search = SerpAPIWrapper(serpapi_api_key=serpapi_api_key)
    answer = search.run(query)
    print("++++++++++++++++calling tools google search###################3")
    return {
        "tool_succeed": True,
        "answer": answer
    }
    # return google_search

function1 = {
    "name": "get_time",
    "description": "获取当前时间",
    "parameters": {
        "type": "object",
        "properties": {},
        "required": []
    }
}

function2 = {
    "name":"google_search",
    "description":"在互联网上搜索信息。",
    "parameters":{
        "type":"object",
        "properties":{
            "query":{
                "type":"string",
                "description":"query of google search"
                }
            },
        "required":["query"]
    }
}


tools = [
    {
        "type": "function",
        **function1
    },
    {
        "type":"function",
        **function2
    }
]

tools_func=[get_time,google_search]


class BotBackend:
    def __init__(self):
        self.function_name = ""
        self.args = ""
        self.function_id = ""
        self.item_id = ""
        self.finish_reason = ""
        self.content = ""

    def update_function_name(self, function_name):
        self.function_name = function_name

    def update_item_id(self, item_id):
        self.item_id = item_id

    def update_args(self, args):
        self.args = args

    def update_function_id(self, function_id):
        self.function_id = function_id

    def update_finish_reason(self, finish_reason):
        self.finish_reason = finish_reason

    def update_content(self, content):
        self.content += content

    def reset_gpt_response_log_values(self, exclude=None):
        if exclude is None:
            exclude = []

        attributes = {
            "function_name": "",
            "args": "",
            "function_id": "",
            "item_id": "",
            "content": "",
        }

        for attr_name in exclude:
            del attributes[attr_name]
        for attr_name, value in attributes.items():
            setattr(self, attr_name, value)

    def run_function(self):
        args = json.loads(self.args)
        print(args)
        # time.sleep(1)
        schema = tools
        print(schema)
        # time.sleep(1)
        cnt = 0
        for s in schema:
            if self.function_name == s['name']:
                idx = cnt
                break
            cnt += 1
        print(cnt)
        # time.sleep(1)
        
        # result = tools_func[idx](self.args)
        if args:
            result = tools_func[idx](**args)
        else:
            result = tools_func[idx]()
        # time.sleep(2.0)
        print("00000000000000++++++++++++++++")
        print(result)
        
        return result

CHUNK_SIZE = 1024
RATE = 24000
FORMAT = pyaudio.paInt16
API_KEY = "********************************************************************************************************************************************************************"
WS_URL = 'wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview-2024-12-17'

audio_buffer = bytearray()
mic_queue = queue.Queue()

stop_event = threading.Event()

bot_backend = BotBackend()
mic_on_at = 0
mic_active = None
REENGAGE_DELAY_MS = 500



update_event_session = {
    "modalities": ["text", "audio"],
    "instructions": "你是一个查询智能体，你的任务是帮助用户查时间或者在网页上搜索，并且把返回的结果解析播报。",
    "voice": "sage",
    "input_audio_format": "pcm16",
    "output_audio_format": "pcm16",
    "input_audio_transcription": {
        "model": "whisper-1"
    },
    "turn_detection": {
        "type": "server_vad",
        "threshold": 0.5,
        "prefix_padding_ms": 300,
        "silence_duration_ms": 500,
        "create_response": True
    },
    "tools": tools,
    "tool_choice": "required",
    "temperature": 0.8,
    "max_response_output_tokens": "inf"
}


def function_call_callback(output):
    print("AAAAAAAAAAAAAAAAaaaa")
    # time.sleep(1.0)
    item_id = output["id"]
    bot_backend.update_item_id(item_id)
    function_name = output["name"]
    bot_backend.update_function_name(function_name)
    call_id = output["call_id"]
    bot_backend.update_function_id(call_id)
    arguments = output["arguments"]
    bot_backend.update_args(arguments)
    bot_backend.update_finish_reason("function_call")
    print(bot_backend.function_name)
    result = bot_backend.run_function()
    print(json.dumps(result))
    # time.sleep(2.0)
    return {
        "type": "conversation.item.create",
        "item": {
            "id": bot_backend.item_id,
            "type": "function_call_output",
            "call_id": bot_backend.function_id,
            "output": json.dumps(result),
        },
    }


def mic_callback(in_data, frame_count, time_info, status):
    global mic_on_at, mic_active

    if time.time() > mic_on_at:
        if mic_active != True:
            logging.info('🎙️🟢 Mic active')
            mic_active = True
        mic_queue.put(in_data)
    else:
        if mic_active != False:
            logging.info('🎙️🔴 Mic suppressed')
            mic_active = False

    return (None, pyaudio.paContinue)


def send_mic_audio_to_websocket(ws):
    try:
        while not stop_event.is_set():
            if not mic_queue.empty():
                mic_chunk = mic_queue.get()
                logging.info(f'🎤 Sending {len(mic_chunk)} bytes of audio data.')
                encoded_chunk = base64.b64encode(mic_chunk).decode('utf-8')
                message = json.dumps({'type': 'input_audio_buffer.append', 'audio': encoded_chunk})
                try:
                    ws.send(message)
                except WebSocketConnectionClosedException:
                    logging.error('WebSocket connection closed.')
                    break
                except Exception as e:
                    logging.error(f'Error sending mic audio: {e}')
    except Exception as e:
        logging.error(f'Exception in send_mic_audio_to_websocket thread: {e}')
    finally:
        logging.info('Exiting send_mic_audio_to_websocket thread.')


def spkr_callback(in_data, frame_count, time_info, status):
    global audio_buffer, mic_on_at

    bytes_needed = frame_count * 2
    current_buffer_size = len(audio_buffer)

    if current_buffer_size >= bytes_needed:
        audio_chunk = bytes(audio_buffer[:bytes_needed])
        audio_buffer = audio_buffer[bytes_needed:]
        mic_on_at = time.time() + REENGAGE_DELAY_MS / 1000
    else:
        audio_chunk = bytes(audio_buffer) + b'\x00' * (bytes_needed - current_buffer_size)
        audio_buffer.clear()

    return (audio_chunk, pyaudio.paContinue)


def receive_audio_from_websocket(ws):
    global audio_buffer

    try:
        while not stop_event.is_set():
            try:
                message = ws.recv()

                print("+++++++++++++")
                print(message)
                
                if not message:  # Handle empty message (EOF or connection close)
                    logging.info('🔵 Received empty message (possibly EOF or WebSocket closing).')
                    break

                # Now handle valid JSON messages only
                message = json.loads(message)
                event_type = message['type']
                logging.info(f'⚡️ Received WebSocket event: {event_type}')

                if event_type == "session.created":
                    logging.info("Connected: say something to GPT-4o")
                    message['type'] = "session.update"
                    message["session"] = update_event_session
                    ws.send(json.dumps(message))
                
                
                if event_type == 'response.audio.delta':
                    audio_content = base64.b64decode(message['delta'])
                    audio_buffer.extend(audio_content)
                    logging.info(f'🔵 Received {len(audio_content)} bytes, total buffer size: {len(audio_buffer)}')

                elif event_type == 'response.audio.done':
                    logging.info('🔵 AI finished speaking.')
                
                elif event_type == "response.output_item.done":
                        output = message["item"]
                        out_put_type = output["type"]
                        if out_put_type == "function_call":
                            event = function_call_callback(output)
                            print("+&&&&&&&&&&&&&&&&&&&&&&&&&")
                            # await self.ws.send(json.dumps(event))
                            ws.send(json.dumps(event))


            except WebSocketConnectionClosedException:
                logging.error('WebSocket connection closed.')
                break
            except Exception as e:
                logging.error(f'Error receiving audio: {e}')
    except Exception as e:
        logging.error(f'Exception in receive_audio_from_websocket thread: {e}')
    finally:
        logging.info('Exiting receive_audio_from_websocket thread.')


def connect_to_openai():
    ws = None
    try:
        ws = create_connection(WS_URL, header=[f'Authorization: Bearer {API_KEY}', 'OpenAI-Beta: realtime=v1'])
        logging.info('Connected to OpenAI WebSocket.')

        ws.send(json.dumps({
            'type': 'response.create',
            'response': {
                'modalities': ['audio', 'text'],
                # 'instructions': 'Please assist the user',
                'instructions':'You can only take the following actions: 获取当前时间',
            }
        }))

        # Start the recv and send threads
        receive_thread = threading.Thread(target=receive_audio_from_websocket, args=(ws,))
        receive_thread.start()

        mic_thread = threading.Thread(target=send_mic_audio_to_websocket, args=(ws,))
        mic_thread.start()

        # Wait for stop_event to be set
        while not stop_event.is_set():
            time.sleep(0.1)

        # Send a close frame and close the WebSocket gracefully
        logging.info('Sending WebSocket close frame.')
        ws.send_close()

        receive_thread.join()
        mic_thread.join()

        logging.info('WebSocket closed and threads terminated.')
    except Exception as e:
        logging.error(f'Failed to connect to OpenAI: {e}')
    finally:
        if ws is not None:
            try:
                ws.close()
                logging.info('WebSocket connection closed.')
            except Exception as e:
                logging.error(f'Error closing WebSocket connection: {e}')



def main():
    p = pyaudio.PyAudio()

    mic_stream = p.open(
        format=FORMAT,
        channels=1,
        rate=RATE,
        input=True,
        stream_callback=mic_callback,
        frames_per_buffer=CHUNK_SIZE
    )

    spkr_stream = p.open(
        format=FORMAT,
        channels=1,
        rate=RATE,
        output=True,
        stream_callback=spkr_callback,
        frames_per_buffer=CHUNK_SIZE
    )

    try:
        mic_stream.start_stream()
        spkr_stream.start_stream()

        connect_to_openai()

        while mic_stream.is_active() and spkr_stream.is_active():
            time.sleep(0.1)

    except KeyboardInterrupt:
        logging.info('Gracefully shutting down...')
        stop_event.set()

    finally:
        mic_stream.stop_stream()
        mic_stream.close()
        spkr_stream.stop_stream()
        spkr_stream.close()

        p.terminate()
        logging.info('Audio streams stopped and resources released. Exiting.')


if __name__ == '__main__':
    main()