#!/bin/bash

SESSION_NAME="agent"
WORKDIR="/home/<USER>/projects/kaiwu_agent"
CONFIG_DIR="configs/wrc"

# 检查虚拟环境是否存在
if [ ! -d "$WORKDIR/.venv" ]; then
    echo "未找到 .venv 虚拟环境，请先创建"
    exit 1
fi

# 如果session已存在则kill
if tmux has-session -t $SESSION_NAME 2>/dev/null; then
    echo "已存在名为 $SESSION_NAME 的tmux session，正在kill..."
    tmux kill-session -t $SESSION_NAME
fi

BASE_CMD="cd $WORKDIR && source .venv/bin/activate && python main.py -mc model.yaml -ms chatany -m gpt-4.1 -ec .env"
declare -a configs=("tianyi_electrician" "tiangong_box_moving" "ur_light_bulb_check" "franka_gift_package")

# 启动新的tmux session并命名第一个窗口
tmux new-session -d -s $SESSION_NAME -n "${configs[0]}"
tmux send-keys -t $SESSION_NAME:0 "$BASE_CMD -c $CONFIG_DIR/${configs[0]}.yaml" C-m

# 新建3个窗口并分别运行命令和命名窗口
for i in 1 2 3
do
    tmux new-window -t $SESSION_NAME:$i -n "${configs[$i]}"
    tmux send-keys -t $SESSION_NAME:$i "$BASE_CMD -c $CONFIG_DIR/${configs[$i]}.yaml" C-m
done

# 附加到tmux session
tmux attach-session -t $SESSION_NAME
