#!/bin/bash
SESSION_NAME="agent"
WORKDIR=$(pwd)

if tmux has-session -t "$SESSION_NAME" 2>/dev/null; then
  echo "Killing existing session $SESSION_NAME..."
  tmux kill-session -t "$SESSION_NAME"
fi

BASE_CMD="cd $WORKDIR && source .venv/bin/activate && python3 main.py --model-server chatany_lego --model gpt-4.1"
CFG_ROOT=$(dirname configs/waic_agents/start_all_waic.sh)
configs=(tianyi_box_moving.yaml ur_lightbulb_check.yaml)

tmux new-session -d -s "$SESSION_NAME"  # create session

# 每次只用一个 tmux 窗口，即 window 0，然后我们在这个 window 分 pane。
# 左右两列：先 split-window -h，
# 然后每列再垂直切割成多行。

# 列数
cols=2
count=${#configs[@]}
# 每列行数
rows=$(( (count + cols - 1) / cols ))

# 首先在 window 0 做水平分割，创建两列
tmux split-window -h -t "$SESSION_NAME":0  # 一分为左右两列
# 第 0 列在 pane 0（左），第1列在 pane 1（右）

for col in $(seq 0 $((cols-1))); do
  # 计算本列第一个 pane 的 pane 号（初始 0 或 1）
  pane_index=$col
  # 每列第一个 pane 保持，再做垂直分割(rows-1)次
  for ((i=1; i<rows; i++)); do
    cfg_index=$(( col*rows + i ))
    if [ $cfg_index -ge $count ]; then
      break
    fi
    # -t pane_index 分割 pane
    tmux split-window -v -t "$SESSION_NAME":0.$pane_index
    # 不要让新的 pane 被选中，可先选回原 pane
    tmux select-pane -t "$SESSION_NAME":0.$pane_index
    # 下一次，pane_index 也要考虑新产生的 pane id，最好固定使用 target pane to split
  done
done

# 然后拆完所有 pane 后，再往各个 pane 里 send 命令
for idx in "${!configs[@]}"; do
  # 计算列和行
  col=$(( idx / rows ))
  row=$(( idx % rows ))
  pane_id=$(( col * rows + row ))
  cfg=${configs[$idx]}
  # send keys 到 pane
  tmux send-keys -t "$SESSION_NAME":0.$pane_id "$BASE_CMD -c $CFG_ROOT/$cfg" C-m
done

# attach
tmux attach-session -t "$SESSION_NAME"
