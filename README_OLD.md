# kaiwu_agent
基于langraph实现的开物整体Agent框架，是一个分布式多智能体架构，内容包括
- 实现了BrainAgent，其调用InteractionAgent、ChatAgent、ControlAgent等工具，完成用户指令
- 未来可能接入AgentScope等分布式框架，调度管理上述专家Agent，包括远程启停、通讯等
- 向上，给应用层提供接口，包括UI、API、SDK等

注意：
`kaiwu_agent`仅包含业务层逻辑，不含对langgraph框架层的修改，目的是业务层和框架层代码解耦：
- 对langgraph的修改应该提PR合入其github master，便于官方持续兼容你增加的功能，或fork到内部github后修改
- 如耦合在一起，langgraph官方大改代码后，`kaiwu_agent`出现代码冲突，需人工频繁解决

所以，不要把langgraph源码拷贝`kaiwu_agent`里，而是作为依赖库放到`requirements.txt`。

## 📢最新消息
* [2025.05.14] 基于langgraph的react agent可接入海量mcp工具（请make deps安装mcp所需依赖）
* [2025.02.25] 基于FunctionCallAgent实现流式文字生成和并行工具调用
* [2024.12.11] 新增VisualBrainAgent支持Qwen2-VL
* [2024.12.06] 新增集多模态感知、任务规划和工具调用于一体的VisualBrainAgent
* [2024.11.27] 新增天气查询、位置获取、网络搜索等工具
* [2024.11.11] 新增适配BrainAgentV3的显示规划和进度的Web UI
* [2024.11.09] 新增基于ReAct的ReActBrainAgent，推理速度和任务完成率大幅提升

## 功能
目前支持的技能：
- TGBrainAgentV1
  - manipulation: 把苹果放到我手上
  - scene_desc: 你看到了什么（场景描述）
  - release_hand: 把手松开（机器人手指复位）
  - other: 开放性对话，介绍一下创新中心
- TGBrainAgentV2
  - control: 控制型指令
  - chat: 开放性聊天
  - other: 其它
- TGBrainAgentV3：基于LangGraph开发的plan-and-excute方法
  - 执行流程：plan-react-replan-react...
  - 测试用例：你看到了什么、帮我准备一份早餐
- GraphBrainAgent
  - control: 控制型指令
  - chat: 开放性聊天
  - other: 其它
- VisualBrainAgent：基于多模态大模型(GPT-4o/Qwen2-VL)实现集多模态感知、任务规划和工具调用于一体
  - perception: 获取图片进行感知
  - control: 控制
  - interaction: 交互

## TODO 列表

- [x] 支持本地vllm部署模型Qwen
- [x] 使用ReAct思维链进行加速推理和连续执行
- [x] 新增常用工具
- [x] 基于GPT4o实现多模态Agent
- [x] 多模态Agent支持调用Qwen-VL等开源多模态大模型

## 安装
```
# 1. 创建虚拟环境并激活
## langgraph要求python3.10及以上，如不存在，可make py312安装python3.12
make venv
source .venv/bin/activate

# 2. 安装开发依赖
## mcp所需命令行工具
make uv
## python依赖
make deps
```


## 测试
```
# 1. 启动语音服务
## 参考http://10.0.3.101/kaiwu/agent/robot_voice/-/blob/pub/README.md
## 使用语音输入
python3 server.py
## 使用文字输
python3 fake_server.py

# 2. 启动fake control agent，用于debug
## 加上 CONTROL_PORT=9600 前缀可设置端口，避免冲突
## control_agent版本为v1时，对应fake_control_server_v1.py
## control_agent版本为v2时，对应fake_control_server_v2.py
bash tools/fake_control_server.sh

# 3.  (可选) 启动tkinter UI, 用于展示用户指令，plan，以及当前step执行过程
## 将 /tools/tkinter_gui_server.py 在有显示器的展示设备(如4090)上运行，不是在Orin上
python3 tkinter_gui_server.py

# 4.  (可选) 自定义配置
## 可在kaiwu_agent\configs\config.py文件中修改测试图片路径、Prompt以及control agent IP等信息

# 5. 启动
## 测试BrainAgentV3，plan和replan使用GPT4o模型，excute使用Qwen2.5-72B模型
python3 main.py -v 3 -l Qwen25-72B-Instruct -m gpt-4o

## 测试ReActAgent，使用与Franka适配的control agent v2以及SoM感知
python3 main.py -v react -c 2 -p 2

## 测试VisualAgent，使用gpt-4o模型, 开启仿真模式，使用kaiwu_agent\configs\config.py中的image_path图片作为测试图片
python3 main.py -v 4 -m gpt-4o --simulation

## 测试VisualAgent，使用Qwen2-VL模型(目前还支持Qwen2-VL-7B-Instruct、qwen-vl-plus),并将数据保存至assets/data/visual_agent路径
python3 main.py -v 4 -m Qwen2-VL-72B-Instruct -s assets/data/visual_agent

## 测试开物云（从前端或app获取指令）
### 启动假的开物云发送指令端：python3 tools/fake_kaiwuyun_send.py
### 启动假的开物云接收指令端：python3 tools/fake_kaiwuyun_recv.py
python3 main.py -v react -c 2 -p 2 --simulation -u ui

## 启动天工积木搭建demo
### （可选）启动假的lego control agent server: bash tools/fake_lego_control_agent_server.sh
### （可选）给lego control agent发消息：python3 tools/fake_kaiwuyun_send.py
python3 main.py -v react -c lego -em gpt-4o-hk -pm gpt-4o-hk

## 启动UR积木搭建demo
python3 main.py -v react -c lego_ur

## 启动天工lite（运控那边用的能奔跑的lite2、lite4）做纯交互，无操作
python3 main.py -v react -c chat -em gpt-4o

##使用interact_agent和采用讯飞语音套件（不用启动robot_voice）
python3 main.py -v react -c chat --interact_agent --use_xunfei
python3 main.py -v react -c lego -em gpt-4o -pm gpt-4o --interact_agent --use_xunfei
##使用interact_agent并采用asr-llm-tts替换方案,需要启动robot_voice
python3 main.py -v react -c chat --interact_agent --asr_llm_tts
##使用interact_agent并采用qwen-omni替代方案，不需要启动robot_voice
python3 main.py -v react -c chat --interact_agent --use_xunfei --qwen

## 基于FunctionCallAgent使用流式文字生成和并行工具调用(demo3场景)
python3 main.py -v 4 -pm Qwen2.5-72B-Instruct --stream

## 基于FunctionCallAgent使用流式文字生成和并行工具调用(简单工业场景)
python3 main.py -v 4 -p 2 -c 2 -pm Qwen2_5-VL-72B-Instruct --stream

## 基于chat_model聊天大模型的brain agent，直接用大模型的回答，没有任何工具包装
python3 main.py -v chat -em doubao15-pro

## 启动waic_demo agent
电工agent: python3 main.py -c electrician -v react -u compose
天轶搬箱子agent: python3 main.py -c box_move_wrc -v react -u compose
ur质检agent: python3 main.py -c lightbulb_check -v react -u compose
franka打包礼品agent: python3 main.py -c gift_package -v react -u compose
```

## 正式部署
一键启动语音服务及kaiwu_agent，会创建tmux窗口在后台持久化运行
```
bash run.sh 
```

## 代码合入
通过提merge request合入master或dev，禁止直接push

## TODO
- 启用CICD，打开集成测试
