# Set package dir
PKGDIR := kaiwu_agent


# langgraph 0.3.x 版本之后要求python3.11及以上
# langgraph 0.2.x 及以前版本要求python3.10及以上
venv:
	# python3 -c 'import sys; exit(not (sys.version_info >= (3, 10)))' || { echo "Python version must be >= 3.10"; exit 1; }
	python3 -m venv .venv

venv311:
	sudo apt-get install python3.11-venv python3.11-dev -y
	python3.11 -m venv .venv

# 安装 python3.12
py312:
	sudo add-apt-repository ppa:deadsnakes/ppa && sudo apt update && sudo apt install python3.12 -y
	
venv312:
	sudo apt-get install python3.12-venv python3.12-dev -y
	python3.12 -m venv .venv

act_venv:
	source .venv/bin/activate

interact:
	# pyaudio
	sudo apt install portaudio19-dev -y
	pip3 install -r ./requirements_interact.txt

# Install all dependencies
dev:
	make deps

uv:
	# uvx for mcp
	curl -LsSf https://astral.sh/uv/install.sh | sh

deps:
	pip3 config set global.index-url https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple
	pip3 install -r ./requirements.txt

# Set test coverage threshold
# TEST_COVERAGE_THRESHOLD=0   # TODO, 90

# install to pip
install:
	pip install -e . 

# Trigger tests.
test: install
	set -x
	PYTHONPATH=${PWD} python3 -m pytest -x -s -v tests/

# Remove common intermediate files.
clean:
	find . -name '*.pyc' -print0 | xargs -0 rm -f
	find . -name '*.swp' -print0 | xargs -0 rm -f
	find . -name '.DS_Store' -print0 | xargs -0 rm -rf
	find . -name '__pycache__' -print0 | xargs -0 rm -rf
	-rm -rf \
		*.egg-info \
		.coverage* \
		.eggs \
		.mypy_cache \
		.pytest_cache \
		PYTEST_TMPDIR \
		relative_cache \
		Pipfile* \
		build \
		dist \
		output \
		public

# Remove common intermediate files alongside with `pre-commit` hook.
deepclean: clean
	pre-commit uninstall --hook-type pre-push

# CICD environment check etc.
cicd-info:
	env | sort
	# nvidia-smi
	which python
	which pip
	python --version
	python -m site
	which python3
	which pip3
	python3 --version
	python3 -m site
	pwd
	ls -la
