from kaiwu_agent.utils.logger import logger
import sys
import os
import argparse
import logging
import yaml
import re
import pprint
from typing import Any
from dotenv import load_dotenv
from typing import Any, Dict, List, Tuple
from pydantic import BaseModel, ValidationError, Field
from kaiwu_agent.utils.registry import MODELS, AGENTS, MSG_CLIENTS
from kaiwu_agent.utils.common import get_current_datetime


# 支持的配置版本范围（min, max）
SUPPORTED_CONFIG_VERSION_RANGE: Tuple[str, str] = ("0.0.1", "0.2.0")
# 正则匹配 {{ key }}
DEFAULT_PATTERN = re.compile(r"{{\s*(\w+)\s*}}")


class AppConfig(BaseModel):
    version: str = Field("0.0.1", description="config version")
    # manage input and output messages
    msg_client: Dict[str, Any]
    # brain agent config, e.g. GraphBrainAgent
    brain_agent: Dict[str, Any]

    class Config:
        extra = 'allow'  # 允许额外的字段

    def validate_version(self):
        min_v, max_v = SUPPORTED_CONFIG_VERSION_RANGE
        if not (min_v <= self.version <= max_v):
            raise ValueError(f"Config version '{self.version}' not in range [{min_v},{max_v}]")


def load_env_vars(prefix: str = "", doenv_path: str = ".env") -> Dict[str, Any]:
    """读取 .env ，仅读取 `prefix` 开头的变量，然后去掉 `prefix` 前缀。"""
    load_dotenv(doenv_path)
    def _safe_yaml_load(_val: str) -> Any:
        try:
            return yaml.safe_load(_val)
        except:
            return _val

    env_vars: Dict[str, Any] = {}
    for key, val in os.environ.items():
        if key.startswith(prefix):
            parsed_key = key.replace(prefix, "")
            parsed_val = _safe_yaml_load(val)
            env_vars[parsed_key] = parsed_val
            env_vars[parsed_key.lower()] = parsed_val
    logger.info(f"解析 .env 文件获得变量：{env_vars}")
    return env_vars


def load_cli_vars(args: argparse.Namespace) -> Dict[str, Any]:
    """CLI 覆盖：--llm-model-server, --llm-model"""
    # 解析模型服务文件
    if not os.path.exists(args.model_config):
        sys.exit(f"模型配置文件 {args.model_config} 不存在")
    with open(args.model_config, 'r') as f:
        model_cfg = yaml.safe_load(f)
        model_servers = model_cfg["model_servers"]
    if args.model_server not in model_servers:
        sys.exit(f"你指定的模型服务器 '{args.model_server}' 不在配置文件中: {model_cfg.keys()}")

    server_cfg = model_servers[args.model_server]
    if args.model:
        server_cfg['model'] = args.model
    
    merged = {**server_cfg}
    logger.info(f"解析 命令行参数 获得变量：{merged}")
    return merged


def resolve_placeholders(
    value: Any,
    context: dict,
    strict: bool = True,
    pattern: re.Pattern = DEFAULT_PATTERN
) -> Any:
    # —— 字符串：纯占位符保留原类型，混合文本转为 str 拼接 —— #
    if isinstance(value, str):
        full = pattern.fullmatch(value)
        if full:
            key = full.group(1)
            if key in context:
                return context[key]
            elif strict:
                raise KeyError(f"Missing key '{key}' in context")
            else:
                return value

        def repl(m):
            k = m.group(1)
            if k in context:
                return str(context[k])
            elif strict:
                raise KeyError(f"Missing key '{k}' in context")
            else:
                return m.group(0)
        return pattern.sub(repl, value)

    # —— 原生 dict/list/tuple —— #
    elif isinstance(value, dict):
        return {
            k: resolve_placeholders(v, context, strict, pattern)
            for k, v in value.items()
        }
    elif isinstance(value, list):
        return [
            resolve_placeholders(v, context, strict, pattern)
            for v in value
        ]
    elif isinstance(value, tuple):
        return tuple(
            resolve_placeholders(v, context, strict, pattern)
            for v in value
        )

    # —— Pydantic BaseModel —— ##
    elif isinstance(value, BaseModel):
        orig_dict = value.model_dump()
        resolved_dict = resolve_placeholders(orig_dict, context, strict, pattern)
        return value.model_copy(update=resolved_dict)

    # —— 其它类型原样返回 —— ##
    else:
        return value


def parse_args_and_cfg() -> dict:
    parser = argparse.ArgumentParser(description="Kaiwu Agent Config Loader")
    parser.add_argument("-c", "--config", type=str, required=True, help="应用配置文件，yaml/json/toml")
    parser.add_argument("-mc", "--model-config", type=str, default="model.yaml", help="模型配置文件，默认 model.yaml")
    parser.add_argument("-ms", "--model-server", type=str, default="chatany", help="从 model.yaml 里的模型服务器中选一个")
    parser.add_argument("-m", "--model", type=str, default=None, help="覆盖model server里默认的模型名称")
    parser.add_argument("-ec", "--env-config", type=str, default=".env", help="环境变量配置文件，默认 .env")
    args = parser.parse_args()

    # ---------------------------------------------------------------------------
    # --- 配置覆盖顺序: 配置文件 < 环境变量 < CLI参数 < 全局变量 ---
    # ---------------------------------------------------------------------------
    file_cfg = yaml.safe_load(open(args.config, 'r'))
    
    # 加载 .env 及 model.yaml，把其中的 model, api_key, base_url等变量作为全局变量，
    # 然后替换 config.yaml 中的 {{ key_name }} 占位符
    env_vars = load_env_vars(doenv_path=args.env_config)
    cli_vars = load_cli_vars(args)
    current_time, current_weekday = get_current_datetime()
    # CLI 优先覆盖 ENV
    context = {
        "CURRENT_TIME": current_time, 
        "CURRENT_WEEKDAY": current_weekday,
        "CURRENT_DIR": os.path.dirname(__file__),
        **env_vars, 
        **cli_vars,
    }
    filled = resolve_placeholders(file_cfg, context, strict=False)

    try:
        app_cfg = AppConfig.model_validate(filled)
        app_cfg.validate_version()
    except (ValidationError, ValueError) as e:
        sys.exit(f"配置错误: {e}")

    cfg_dict = app_cfg.model_dump()
    logger.info("✅ 配置加载成功，版本校验通过")
    # 打印配置
    logger.info("完整配置:\n%s", pprint.pformat(cfg_dict))
    
    return cfg_dict


def main():
    cfg_dict = parse_args_and_cfg()
    
    # 构建共享变量 msg_client
    msg_client = MSG_CLIENTS.build_from_cfg(cfg_dict["msg_client"])
    cfg_dict = resolve_placeholders(cfg_dict, {"msg_client": msg_client}, strict=True)
    
    # 构建 brain agent，并自动递归构建 graph_creator, model, tools 等子组件
    try:
        brain_agent = AGENTS.build_from_cfg(cfg_dict["brain_agent"])
    except Exception as e:
        logger.error(f"构建 Agent 失败：{e}", exc_info=True)
        sys.exit(1)
    
    # 开始执行，监听消息，让主程序常驻，直到遇到 ctrl-c
    brain_agent.start()
    
    # 清理
    brain_agent.stop()
    logger.info("Kaiwu Agent Done.")
    logging.shutdown()


if __name__ == "__main__":
    main()
