# kaiwu_agent
开物分布式多智能体框架，用于创建具身智能体应用，可接受文本指令，完成多轮任务拆解、工具调用，进行多轮对话。

## 📢最新消息
* [2025.07.31] 支持自定义MCP服务，支持yaml配置，用新的`main.py`取代`main_release.py`
* [2025.05.29] 支持qwen2.5-72b llm做语音交互 
* [2025.05.25] 支持开放性语音交互功能，支持自定义prompt、模型、知识库文档

## 安装启动
- 准备环境（代码对外发布前）
```
## 创建python虚拟环境并激活，要求python3.10及以上版本，如不存在则make py312装python3.12
make venv
source .venv/bin/activate

## mcp所需命令行工具
make uv

## python依赖
make deps
```

- 启动天工开放性语音交互
```
bash run_release.sh
```

## 编译发布
对外发布代码前，`只保留`以下目录文件，其余目录文件`必须删除`，包括删除所有`以.开头`的隐藏文件：
```
docs/                # 不要编译
examples/            # 不要编译，开放源码，便于客户修改
kaiwu_agent/         # <需要编译> <需要编译> <需要编译> ! ！！
README_RELEASE.md    # 不要编译
main.py          # 不要编译
.example.env         # 不要编译
model.example.yaml   # 不要编译
```


## 使用说明
- 如何使用配置系统，修改模型、账号、prompt、RAG知识库文档等：[config.md](docs/config.md)
  - 客户需尽快换成自己的模型，现有账号仅少量余额仅用于测试，随时可能欠费
- 可通过“退出”、“暂停”、“闭嘴”等关键词打断机器人让他闭嘴
