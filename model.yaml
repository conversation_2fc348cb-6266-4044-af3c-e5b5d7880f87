# 说明：
# 1. `model`是默认模型，可在运行时通过命令行参数更改model，切换到其它模型
##################################################################
llm_vlm_servers: &llm_vlm_servers
  # openai官方服务器（需VPN或中美专线）
  ## 这些是演示专用账号，日常研发做实验造数据等不要用，以免干扰演示！！！
  openai:
    model: gpt-4.1
    api_key: ********************************************************************************************************************************************************************
    base_url: https://api.openai.com/v1
  
  # 国内chatanywhere代理
  ## 这些是演示专用账号，日常研发做实验造数据等不要用，以免干扰演示！！！
  chatany: 
    model: gpt-4.1
    api_key: sk-cXzFCmWQ0u9S8Hp48xY9bawKp6SvAc1OTfU2mkbiucHGA2dr
    base_url: https://api.chatanywhere.tech/v1
  chatany_lego: 
    model: gpt-4.1
    api_key: sk-tw19zUxeHt6wC2ntcLdsIbRmswg0K1bV9MBDGgGOCE6bzxAQ
    base_url: https://api.chatanywhere.tech/v1
  chatany_demo3: 
    model: gpt-4.1
    api_key: sk-Wyy6gs0pUYfhmP7RLo6XvupsmujD4odpK3yU1tQbwtLh1mWt
    base_url: https://api.chatanywhere.tech/v1
  
  # 香港代理服务器
  ## 这些是演示专用账号，日常研发做实验造数据等不要用，以免干扰演示！！！
  openai_hk: 
    model: gpt-4.1
    api_key: sk-0phYyUhY4zT8fQFkZWPQL6ei99XWFrL3oLloHnYTwQ42Ic8h
    base_url: http://154.85.43.94:3000/v1
  openai_hk1:
    model: gpt-4.1
    api_key: sk-lynYTCqEGIzf8qE4nntAMDuLAua1FirHkRmb3FUmBbYjdVAB
    base_url: http://154.85.60.169:3000/v1

# VLM Servers
vlm_servers: &vlm_servers
  doubao15-vision-pro:
    model: Doubao-1.5-vision-pro
    api_key: d2c9b4b6-cffe-4dd4-888e-69f01afc051d    
    base_url: https://ark.cn-beijing.volces.com/api/v3

  Qwen2-VL-7B-Instruct: 
    model: Qwen2-VL-7B-Instruct
    api_key: EMPTY
    base_url: http://10.10.255.19:8000/v1

  Qwen2-VL-72B-Instruct: 
    model: Qwen2-VL-72B-Instruct
    api_key: EMPTY
    base_url: http://10.0.3.31:30088/v1

  Qwen2_5-VL-72B-Instruct: 
    model: Qwen2_5-VL-72B-Instruct # AI Station (内网使用速度更快)
    api_key: EMPTY
    base_url: http://10.0.3.31:30800/v1

  Qwen2_5-VL-72B-Instruct-Baidu: 
    model: Qwen2_5-VL-72B-Instruct # 百舸 （方便外网使用）
    api_key: EMPTY
    base_url: http://180.76.238.186:8888/v1

  qwen-vl-plus: 
    model: qwen-vl-plus
    api_key: sk-080c0cab73cf41a0b7ea25cf554aa1db
    base_url: https://dashscope.aliyuncs.com/compatible-mode/v1

  QVQ-72B-Preview: 
    model: QVQ-72B-Preview
    api_key: EMPTY
    base_url: http://120.48.26.181:8086/v1
    
  InternVL2_5-78B: 
    model: InternVL2_5-78B
    api_key: EMPTY
    base_url: http://180.76.179.138:8086/v1

# LLM Servers
llm_servers: &llm_servers
    # TODO 替换账号
    qwen-max:
      model: qwen-max
      api_key: sk-ef9449c55128451dabb8a142b457c455
      base_url: https://dashscope.aliyuncs.com/compatible-mode/v1
    
    doubao15-pro:
      model: doubao-1.5-pro-32k-250115
      api_key: d2c9b4b6-cffe-4dd4-888e-69f01afc051d    
      base_url: https://ark.cn-beijing.volces.com/api/v3
    
    Qwen/Qwen2.5-72B-Instruct-GPTQ-Int8: 
      model: Qwen/Qwen2.5-72B-Instruct-GPTQ-Int8
      api_key: EMPTY
      base_url: http://10.10.122.239:8000/v1
    
    Qwen2_5-72B-Instruct: 
      model: Qwen2_5-72B-Instruct
      api_key: EMPTY
      base_url: http://qwen25-lm-72b.x-humanoid-cloud.com:8888/v1
    
    Qwen25-72B-Instruct: 
      model: Qwen25-72B-Instruct
      api_key: EMPTY
      base_url: http://10.0.3.31:30008/v1
    
    Doubao-1.5-lite-32k-250115: 
      model: ep-20250125141655-m5phz
      api_key: 2c7fa5f8-e0d5-44f0-9779-7a243629ab23
      base_url: https://ark.cn-beijing.volces.com/api/v3
    
    Doubao-1.5-lite-32k-250115: 
      model: ep-20250125142156-77mqf
      api_key: 2c7fa5f8-e0d5-44f0-9779-7a243629ab23
      base_url: https://ark.cn-beijing.volces.com/api/v3
    
    deepseek-v3: 
      model: deepseek-chat
      api_key: ***********************************
      base_url: https://api.deepseek.com

# 汇总
model_servers:
  <<: *llm_vlm_servers
  <<: *vlm_servers
  <<: *llm_servers
