# kaiwu_agent
慧思开物平台——具身智能体框架，简称开物智能体框架，基于langraph实现，是一个分布式多智能体协作架构，负责大脑执行闭环，包括监听语音消息、任务规划、工具执行、结果反思等。

## 目录介绍
```
kaiwu_agent/
    configs/   # 内部示例，不开源
    examples/  # 可开源的示例
    agents/    # 智能体，包括用于任务拆解和工具调用的Brain Agent等
    graphs/    # langgraph的执行图，例如langgraph预置的 react_agent
    messages/  # 消息管理器，负责监听监听语音或者app消息指令，并发送消息文本进行合成播报，或发给app显示
    models/    # langchain 模型，在graph里使用
    tools/     # langchain Tools，定义可用工具
    utils/     # 常用工具
```

## 准备环境
```
# 1. 创建虚拟环境并激活
## langgraph要求python3.10及以上，如不存在，可make py312安装python3.12
make venv
source .venv/bin/activate

# 2. 安装开发依赖
## mcp所需命令行工具
make uv
## python依赖
make deps
```

## 开始运行
```
# 1. 启动语音服务 robot_voice
##  参考http://10.0.3.101/kaiwu/agent/robot_voice/-/blob/pub/README.md
## 使用语音输入
cd /path/to/robot_voice && python3 server.py
## 使用文字输入
cd /path/to/robot_voice &&python3 fake_server.py

# 2. 启动mcp服务器（可选）
...

# 3. 启动kaiwu_agent，连接robot_voice获取语音文本，连接mcp服务器获取工具列表
## WAIC多智能体协作demo
bash configs/waic_agents/start_all_waic.sh

## 天工闲聊（无操作技能）
### 生成完整文本再一次性合成语音
python3 main.py --model-server chatany --model gpt-4.1 -c examples/chat/tiangong_chat.yaml
### 将文本分成多个片段去合成语音，提高语音首响
python3 main.py ... -c examples/chat/tiangong_chat_streaming.yaml
```

## 使用文档
- [如何写config.yaml](docs/config.md)


## 修改日志
